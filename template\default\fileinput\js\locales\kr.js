/*!
 * FileInput Korean Translations
 *
 * This file must be loaded after 'fileinput.js'. Patterns in braces '{}', or
 * any HTML markup tags in the messages must not be converted or translated.
 *
 * @see http://github.com/kartik-v/bootstrap-fileinput
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
(function ($) {
    "use strict";

    $.fn.fileinputLocales['kr'] = {
        fileSingle: '파일',
        filePlural: '파일들',
        browseLabel: '찾기 &hellip;',
        removeLabel: '지우기',
        removeTitle: '선택한 파일들 지우기',
        cancelLabel: '취소',
        cancelTitle: '업로드 중단하기',
        uploadLabel: '업로드',
        uploadTitle: '선택한 파일 업로드하기',
        msgNo: '아니요',
        msgNoFilesSelected: '선택한 파일이 없습니다.',
        msgCancelled: '취소되었습니다.',
        msgPlaceholder: 'Select {files}...',
        msgZoomModalHeading: '자세한 미리보기',
        msgFileRequired: 'You must select a file to upload.',
        msgSizeTooSmall: '파일 "{name}" (<b>{size} KB</b>)이 너무 작습니다. <b>{minSize} KB</b>보다 용량이 커야 합니다..',
        msgSizeTooLarge: '파일 "{name}" (<b>{size} KB</b>)이 너무 큽니다. 허용 파일 사이즈는 <b>{maxSize} KB</b>.입니다.',
        msgFilesTooLess: '업로드하기 위해 최소 <b>{n}</b> {files}개의 파일을 선택해야 합니다.',
        msgFilesTooMany: '선택한 파일의 수 <b>({n})</b>가 업로드 허용 최고치인 <b>{m}</b>를 넘었습니다..',
        msgFileNotFound: '파일 "{name}"을 찾을 수 없습니다.!',
        msgFileSecured: '보안상의 이유로 파일 "{name}"을/를 읽을 수 없습니다..',
        msgFileNotReadable: '파일 "{name}"은/는 읽을 수 없습니다.',
        msgFilePreviewAborted: '파일 "{name}"의 미리보기가 중단되었습니다.',
        msgFilePreviewError: '파일 "{name}"을/를 읽다가 에러가 발생했습니다.',
        msgInvalidFileName: '파일 "{name}" 중 지원 불가능한 문자가 포함되어 있습니다.',
        msgInvalidFileType: '파일 "{name}"의 타입은 지원하지 않습니다. "{types}" 타입의 파일을 선택해 주십시요.',
        msgInvalidFileExtension: '파일 "{name}"의 익스텐션은 지원하지 않습니다. "{extensions}" 타입의 익스텐션을 선택해 주십시요.',
        msgFileTypes: {
            'image': 'image',
            'html': 'HTML',
            'text': 'text',
            'video': 'video',
            'audio': 'audio',
            'flash': 'flash',
            'pdf': 'PDF',
            'object': 'object'
        },
        msgUploadAborted: '파일 업로드가 중단되었습니다.',
        msgUploadThreshold: '업로드 중...',
        msgUploadBegin: 'Initializing...',
        msgUploadEnd: 'Done',
        msgUploadEmpty: '업로드 가능 데이터가 존재하지 않습니다.',
        msgUploadError: 'Error',
        msgValidationError: '유효성 오류',
        msgLoading: '파일 {files} 중 {index}번째를 로딩하고 있습니다. &hellip;',
        msgProgress: '파일 {files}의 {name}이 {percent}% 로딩되었습니다. ',
        msgSelected: '{n} {files}이 선택 되었습니다.',
        msgFoldersNotAllowed: '드래그 앤 드랍 파일만 가능합니다! 드랍한 {n}번째 폴더를 건너 뛰었습니다.',
        msgImageWidthSmall: '이미지 파일 "{name}"의 가로는 최소 {size} px가 되어야 합니다.',
        msgImageHeightSmall: '이미지 파일 "{name}"의 세로는 최소 {size} px가 되어야 합니다.',
        msgImageWidthLarge: '이미지 파일 "{name}"의 가로는 최대 {size} px를 넘을수 없습니다.',
        msgImageHeightLarge: '이미지 파일 "{name}"의 세로는 최대 {size} px를 넘을수 없습니다.',
        msgImageResizeError: '이미지의 사이즈를 재조정을 위한 이미지 사이즈를 가져올 수 없습니다.',
        msgImageResizeException: '이미지 사이즈 재조정이 다음 이유로 실패했습니다.<pre>{errors}</pre>',
        msgAjaxError: 'Something went wrong with the {operation} operation. Please try again later!',
        msgAjaxProgressError: '{operation} failed',
        ajaxOperations: {
            deleteThumb: 'file delete',
            uploadThumb: 'file upload',
            uploadBatch: 'batch file upload',
            uploadExtra: 'form data upload'
        },
        dropZoneTitle: '파일을 여기에 드래그인 드랍을 하십시요 &hellip;',
        dropZoneClickTitle: '<br>(또는 {files} 선택을 위해 클릭하십시요)',
        fileActionSettings: {
            removeTitle: '파일 지우기',
            uploadTitle: '파일 업로드 하기',
            uploadRetryTitle: 'Retry upload',
            downloadTitle: 'Download file',
            zoomTitle: '세부 정보 보기',
            dragTitle: '옭기기 / 재배열하기',
            indicatorNewTitle: '아직 업로드가 안되었습니다.',
            indicatorSuccessTitle: '업로드가 성공하였습니다.',
            indicatorErrorTitle: '업로드 중 에러가 발행했습니다.',
            indicatorLoadingTitle: '업로드 중 ...'
        },
        previewZoomButtonTitles: {
            prev: '전 파일 보기',
            next: '다음 파일 보기',
            toggleheader: '머릿글 토글하기',
            fullscreen: '전채화면 토글하기',
            borderless: '무 테두리 토글하기',
            close: '세부 정보 미리보기 토글하기'
        }
    };
})(window.jQuery);
