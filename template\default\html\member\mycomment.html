{include file=comm/head.html}

{include file=comm/top.html}

<div class="container pages">

	{include file=comm/position.html}
	
	<div class="row mb-5">
	
        {include file=member/left.html}

        <div class="col-12 col-md-10 mt-3">			
			<table class="table">
			  <thead>
			  	<th>序号</th>
			  	<th>文章</th>
			  	<th>评论</th>
			  	<th>状态</th>
			  	<th>时间</th>
			  	<th>操作</th>
			  </thead>
			  <tbody>
			  	{pboot:mycomment num=10}
			    <tr>
			      <td>[mycomment:i]</td>
			      <td>[mycomment:title]</td>
			      <td>[mycomment:comment]</td>
			      <td>{pboot:if([mycomment:status]==1)}已审核{else}<span style="color:red">待审核</span>{/pboot:if}</td>
			      <td>[mycomment:date]</td>
			      <td><span class="del" data-url="[mycomment:delaction]" style="cursor:pointer" >删除</span></td>
			    </tr>
			    {/pboot:mycomment}
			  </tbody>
			</table>
			{include file=comm/page.html}
        </div>
   </div>
 
</div>

<script>
$(".del").on("click",function(){

	 if(!confirm("您确定要删除么？")){
		 return;
	 }
	 
	var url=$(this).data('url');
	var el=$(this).parents("tr");
	 $.ajax({
	    type: 'GET',
	    url: url,
	    dataType: 'json',
	    success: function (response, status) {
	    	if(response.code==0){
	    		alert(response.data);
	    	}else{
	    		el.remove();	
	    	}
	    },
	    error:function(xhr,status,error){
	      alert('返回数据异常！');
	    }
	  });
});
</script>

{include file=comm/foot.html}
