﻿##########################################
官方网站：https://www.pbootcms.com
标签手册：https://www.pbootcms.com/docs.html
##########################################
PbootCMS V3.2.11 build 2025-04-16
1、下架Polyfill功能

PbootCMS V3.2.10 build 2024-09-24
1、修正后台关闭验证码后，依旧提示输入验证码

PbootCMS V3.2.9 build 2024-08-01
1、修正Polyfill.js加载地址

PbootCMS V3.2.8 build 2024-06-30
1、修正Polyfill.js加载地址

PbootCMS V3.2.7 build 2024-04-10
1、后台加入Polyfill.js加载选项

PbootCMS V3.2.6 build 2024-01-22
1. 404页面加入返回首页功能。
2. 修复列表页list排序错误。
3. 优化首页幻灯片。
4. 修复身份验证漏洞。

PbootCMS V3.2.5 build 2023-04-21
1、修复首页404配置未生效的问题。
2、编辑器引入ueditor.all.js，读取正确的ueditor-plus配置(感谢群友提供的反馈意见)。
3、UEditor-plus升级至3.0.0。
4、重构清理缓存机制(感谢晨星傲月的帮助)。
5、修复邮件反馈的相关漏洞。
6、修复清理冗余文件误删定制标签图片的问题。

PbootCMS V3.2.4 build 2023-03-04
1、针对查询效率进行优化，去掉一些不必要的like查询，重构列表页查询代码。
2、后台添加、删除模型字段时默认也会对索引进行同步操作，提升查询效率。
3、xml地图错误修复。
4、针对群友开发的静态网页生成软件兼容性处理。
5、ay_content表索引操作，详情可以查看V3.2.4-update.sql文件。
6、session自动清理改为一天一次。
7、首页分页条问题修复
8、ueditor下划线问题修复(感谢Gitee的jeay)
9、其他细节错误修复。
10、去掉sql标签解析（安全性修复，防止shell注入）

PbootCMS V3.2.3 build 2022-12-30
1、UEditor-plus编辑器跟随官方版本升级至2.7.0，详情可查看https://gitee.com/modstart-lib/ueditor-plus/releases。
2、url规则不再支持普通模式，保留伪静态模式和兼容模式（/index.php已经完成了历史使命）。
3、修复伪静态模式下开启首页404不生效的问题。
4、后台留言功能新增一键删除。
5、修复V3.2.2sql文件逗号缺失导致文件导入错误问题。

PbootCMS V3.2.2 build 2022-11-16
1、清理冗余图片功能可视化，支持图片预览和选择图片手动删除。
2、回收站目录优化，删除后的图片保留原有的目录结构，可更直观的对误删文件进行原有文件路径恢复。
3、后台配置参数url规则中新增首页404配置(默认关闭)，开启后访问首页链接不存在时会返回404(需在config表中添加配置项，详情可查看/static/backup/spl/下的文件)。
4、修复清理冗余图片时会误删富文本编辑器上传的图片问题。

PbootCMS V3.2.1 build 2022-09-21
1、新增冗余图片清理，鉴于pcode为系统自增，可能会存在占用的情况，请自行在后台添加功能菜单，具体参数参照sql文件即可。
2、UEditor-plus编辑器跟随官方Gitee更新至2.4.0版本。
3、修复UEditor-plus编辑器本地保存弹窗不显示信息的问题。
4、session文件夹自动清理时间从7天间隔修改为3天。
5、针对近期有些虚拟主机二级目录伪静态首页返回404的问题做了兼容处理。

PbootCMS V3.2.0 build 2022-08-08
1、新增前台会员邮箱密码找回
2、不存在的页面现在默认返回404(V3.1.6版本因一些原因该功能未实装)
3、UEditor编辑器替换为UEditor-plus gitee地址为：https://gitee.com/modstart-lib/ueditor-plus
4、3.2.0版本开始mysql数据导入文件：static/backup/sql/pbootcms_v316.sql 中的ay_content表已默认添加了ay_content_unique索引
5、去掉eval函数执行php代码（安全性修复）

PbootCMS V3.1.6 build 2022-07-12
1、修复pdo_mysql链接报错的问题
2、自动跳转https修改为301到对应url的https
3、后台首页在线更新按钮弹窗提示，跳转到官网开发手册，以及时通知更新重点
4、带有tag分页的bug修复(感谢@cms88提供的解决方案)
5、修复站点地图读取错误的问题
6、不存在的页面现在默认会返回404状态
7、百度富文本框禁用自动加长
8、定制标签添加和修改正则验证符合要求的格式
9、管理后台文章开内容开关刷新没有正常的设置
10、7月12日下午挂马漏洞修复
备注：感谢夜雨轻寒提供的反馈意见邮件

PbootCMS V3.1.5 build 2022-07-04
1、数据库新增索引以优化大数据量网站的查询速度（sql脚本文件随版本一起发布）
2、Session文件过大问题：Session文件超过7天现在会自动检测删除
3、针对近期挂马已对字符串注入做了过滤
4、其他已知bug的修复
重要通知：为了PbootCMS更好的发展，从该版本开始，PbootCMS不再支持7.0以下的php版本，请用户及时升级php版本至7.0+

PbootCMS V3.1.4 build 2022-04-21
1、修复某些情况下伪静态分页首页和尾页出现问题；
2、优化在线更新逻辑
3、搜索防止非法字符串注入

PbootCMS V3.1.3 build 2022-01-03
1、修复自定义路径存在注入问题；
2、修复API接口搜索存在注入问题；
3、修复用户信息修改存在CSRF问题；
4、修复伪静态搜索结果分页问题；
5、修改授权码存储位置为数据库；
6、修复sitemap.txt不换行问题；
7、兼容PHP8.1版本;

PbootCMS V3.1.2 build 2021-10-28
1、修复扩展多图字段编码长度问题；
2、修复sitemap未过滤定时文章问题；
3、修复上个版本导致邮件标题不对问题;
4、新增自动记录蜘蛛访问到后台系统日志；
5、优化nav参数parent支持传递多个栏目编码；
6、新增nav参数scode='1,2,3'方式限制栏目范围；
7、其他问题修复与优化；

PbootCMS V3.1.1 build 2021-10-26
1、修复上版本sitemap短路径模式不统一问题；
2、修复栏目名称必填导致外链跳转栏目无法添加；
3、修复数据库备份引号未转义问题；
4、优化栏目管理跳转内容支持跳转指定子栏目；
5、优化会话清理功能；
6、优化更彻底的OEM定义；
7、优化轮播及友链支持修改分组；
8、新增sitemap.txt格式的支持；

PbootCMS V3.1.0 build 2021-10-24
1、修复地址串栏目及模型可访问问题；
2、优化if语句执行性能提高系统速度；
3、新增详情页URL路径支持层级路径定义；
4、新增后台栏目及内容开关无刷新操作；
5、新增文章新增时自动提取缩略图；
6、新增后台栏目管理跳转文章链接；
7、新增文章扩展字段多图类型的支持；
8、配合多图扩展新增多图标签{pboot:pics}指定field字段参数；
9、新增详情页URL是否带目录路径控制参数；
10、修复自适应标题标签无站点副标题时横线问题；
11、其他问题修复与优化；

PbootCMS V3.0.9 build 2021-10-13
1、修复上版本栏目分享到微信出现打不开问题；
2、修复pboot:sql循环标签无法传带引号参数问题；
3、增强系统安全性;

PbootCMS V3.0.8 build 2021-10-11
1、修复部分环境错误页面状态码不对问题；
2、启用访问地址严格模式，避免多地址指向问题；
3、其他问题修复与优化；

PbootCMS V3.0.7 build 2021-10-09
1、新增列表页自定义多层级路径的支持；
2、新增专题单页多图的标题支持；
3、新增后台清理站点内会话目录的功能(右上角)；
4、去除错误页面显示的服务器信息；

PbootCMS V3.0.6 build 2021-09-29
1、新增文章多图标题功能,前台标签:[pics:title]；
2、新增在config中定义cmsname变量修改系统显示名称；
3、去除后台底部版权区域扩大操作范围；
4、支持修改CMS名称后自动隐藏官方信息；
5、栏目新增def1-def3三个备用描述字段；

PbootCMS V3.0.5 build 2021-06-18
1、修复系统存在的安全漏洞（重要）；

PbootCMS V3.0.4 build 2021-02-14
1、新增{pboot:sql sql="语句"}[sql:字段]{/pboot:sql}万能循环标签；
2、新增自动跳转HTTPS功能参数；
3、新增自动跳转主域名功能参数；
4、修复地址栏扩展后可附带任意字符问题;
5、修复部分主机CDN后台跳转异常；
6、升级layui为最新版本；
7、修复系统存在的安全问题；
8、优化内容含有标签的处理；

PbootCMS V3.0.3 build 2020-10-07
1、修复上版本出现的地址后缀不一致问题；
2、修复搜索功能多语言混乱问题；
3、新增{pboot:isregister}用于js检查用户名是否已经注册；
4、修复已经注册的邮箱仍然发送验证码问题；
5、修复系统存在的安全问题；

PbootCMS V3.0.2 build 2020-08-04
1、修复会员评论无昵称时不解析标签问题；
2、修复默认模板留言页面判断错误问题；
3、修复两处存在安全隐患的漏洞；
4、修复首页筛选兼容模式错误；
5、修复tag标签地址错误问题；
6、修复扩展类文件地址错误问题；
7、修复文章关闭后tag依然调用问题；
8、修复后台设置搜索词标题不解析问题；
9、修复内置附件标签不支持截取问题；
10、其他问题修复与优化；

PbootCMS V3.0.1 build 2020-07-09
1、修复会员权限不足报错页面被404覆盖掉问题；
2、新增对循环体标签整体进行访问权限控制，如list,nav等；
3、新增内容权限不足时调整登录界面“不等待跳登录”参数控制；

PbootCMS V3.0.0 build 2020-07-06
1、新增会员功能，支持注册、登录、资料、积分等操作;
2、支持对会员相关功能进行后台配置;
3、支持对栏目设置会员权限、权限类型、权限提示；
4、支持对内容设置会员权限、权限类型、权限提示；
5、支持模板中设置指定页面必须登录；
6、支持模板中设置标签指定权限可见或隐藏；
7、支持内容评论功能(参考默认模板)；
8、支持会员注册邮件验证码功能；
9、支持留言关联登录用户，新增标签nickname、username、headpic；

//相关表单字段
username、password、checkcode //登录必填
username、nickname、password、rpassword、checkcode //注册必填
comment、checkcode //评论必填

//会员页面标签
{pboot:ucenter} 个人中心地址
{pboot:login} 登录地址
{pboot:register} 注册地址
{pboot:umodify} 资料修改地址
{pboot:logout} 退出登录地址
{pboot:upload} 文件上传AJAX接口
{pboot:islogin} 是否登录状态
{pboot:mustlogin} 设置页面必须登录
{pboot:sendemail} 发送邮件验证码接口,参数to
{pboot:registercodestatus} 会员注册验证码状态0、1、2
{pboot:logincodestatus} 会员登录验证码状态
{pboot:registerstatus} 是否开启注册
{pboot:loginstatus} 是否开启登录
{pboot:commentstatus} 是否开启评论

//会员模板控制标签，如：{content:title showgcode=1}
showgcode=* 指定等级显示，支持多个逗号隔开
showucode=* 指定用户显示，支持多个逗号隔开
hidegcode=* 指定等级隐藏，支持多个逗号隔开
hideucode=* 指定用户隐藏，支持多个逗号隔开
showgcodelt=* 等级小于显示
showgcodegt=* 等级大于显示
showgcodele=* 等级小于等于显示
showgcodege=* 等级大于等于显示
hidegcodelt=* 等级小于隐藏
hidegcodegt=* 等级大于隐藏
hidegcodele=* 等级小于等于隐藏
hidegcodege=* 等级大于等于隐藏
showlogin=1 登录后显示
hidelogin=1 登录后隐藏
对于内容及栏目，也支持在后台直接控制

//会员资料标签,全局可用
{user:ucode} 会员编码
{user:username} 会员用户名
{user:useremail} 会员邮箱
{user:usermobile} 会员手机
{user:gcode} 等级编码
{user:gname} 等级名称
{user:registertime} 注册时间
{user:logincount} 登录次数
{user:lastloginip} 最后登录IP
{user:lastlogintime} 最后登录时间
{user:headpic} 头像URL
{user:***} 自定义会员字段，如果{user:sex}

//文章评论
{pboot:commentcodestatus} 验证码是否开启
{pboot:commentaction} 评论提交地址

{pboot:comment contentid={content:id}}
	[comment:i] 序号0开始
	[comment:n]	序号1开始
	[comment:pid] 父评论ID
	[comment:contentid]	评论文章ID
	[comment:comment] 评论内容
	[comment:ip] IP地址
	[comment:os] 操作系统
	[comment:bs] 浏览器
	[comment:date] 日期
	[comment:uid] 评论人ID
	[comment:username] 评论人账号
	[comment:nickname] 评论人昵称
	[comment:headpic] 评论人头像
	[comment:pid] 父评论人ID
	[comment:pusername] 父评论人账号
	[comment:pnickname] 父评论人昵称
	[comment:pheadpic] 父评论人头像
	[comment:likes] 点赞数量
	[comment:oppose] 反对数量
	[comment:replyaction] 评论回复提交地址
	{pboot:commentsub} 子评论输出
		[commentsub:***] 子评论调用字段同上
	{/pboot:commentsub}
{/pboot:comment}

//我的评论
{pboot:mycommentpage} 我的评论页面地址
{pboot:mycomment}
	[mycomment:i] 序号0开始
	[mycomment:n]	序号1开始
	[mycomment:pid] 父评论ID
	[mycomment:contentid]	评论文章ID
	[mycomment:comment] 评论内容
	[mycomment:title] 文章标题
	[mycomment:ip] IP地址
	[mycomment:os] 操作系统
	[mycomment:bs] 浏览器
	[mycomment:date] 日期
	[mycomment:uid] 评论人ID
	[mycomment:username] 评论人账号
	[mycomment:nickname] 评论人昵称
	[mycomment:headpic] 评论人头像
	[mycomment:pid] 父评论人ID
	[mycomment:pusername] 父评论人账号
	[mycomment:pnickname] 父评论人昵称
	[mycomment:pheadpic] 父评论人头像
	[mycomment:likes] 点赞数量
	[mycomment:oppose] 反对数量
	[mycomment:status] 评论状态1审核2待审核
	[mycomment:replyaction] 评论回复提交地址
	[mycomment:delaction] 评论删除地址
{/pboot:mycomment}


PbootCMS V2.0.9 build 2020-06-18
1、新增多语言分享链接自动切换功能，并新增后台开关;
2、新增后台设置网站页面标题样式功能;
3、修复列表序号翻页不连续问题;
4、修复地址生成函数特殊情况后缀判断不准问题；
5、新增百度快速推送功能；
6、系统安全问题修复；

PbootCMS V2.0.8 build 2020-04-26
1、修复兼容模式分享链接导致打不开页面问题；
2、修复几处可能影响系统安全的漏洞；
3、修改使用IP访问不再需要域名授权码；
4、修复个别空间的兼容性问题。

PbootCMS V2.0.7 build 2020-04-06
1、修复后台被登录成功后系统更新处可能造成的安全漏洞；
2、修复dropblank不支持全角空格过滤问题;
3、新增万能码用户后台SVIP尊贵标识；
4、修复模板子目录在目标文件夹存在时失败问题；
5、修复后台内容编辑页面切换语言导致无限循环问题；
6、新增后台切换多语言时同步切换前台语言；

PbootCMS V2.0.6 build 2020-03-18
1、修复搜索结果关键字标红无法正常匹配大小写问题；
2、新增后台“安全配置”中配置模板文件子目录功能；
3、修复operate参数无法使用小数问题；
4、新增tags列表支持指定参数target=tag跳转到tag.html模板，参考默认模板；
5、新增home下ExtLabelController控制器文件扩展个人标签，升级不覆盖；
6、优化调整前台代码结构；
7、新增后台内容列表每页显示数量选择；
8、修复tags重复显示问题；
9、新增config/route.php文件自定义二开路由支持；
10、新增API内容列表返回自适应的内容网页链接地址contentlink；
11、修复API内容API链接错误并修改参数为apilink；
12、修复API留言及表单无法关闭功能问题；

PbootCMS V2.0.5 build 2020-01-31
1、优化前台404.html，并支持内容内{info}自适应提示文字；
2、优化内容列表排序方式,并修复api使用随机排序无效问题；
3、修复后台入口文件修改名字后在线升级无法自适应问题；
4、新增lfield="a,b,*"限制列表查询字段，提高大数据时速度；
5、新增系统自适应入口文件子目录的能力；
6、新增留言及表单数据清空功能(管理员)；
7、新增留言及表单数据导出功能；
8、新增mark=1标签参数对搜索结果关键字进行标红，如：[search:title mark=1]；
9、其他问题修复与优化。

PbootCMS V2.0.4 build 2020-01-21
1、修复部分循环体控制参数无效问题；
2、修复一处安全漏洞，并全局加强系统安全过滤；
3、修复面包屑参数中不能使用html的问题；
4、修复API接口中post方式page无效问题；
5、新增程序全自动修改数据库名；
6、新增operate参数对标签进行加减乘除等运算，如：[list:i operate=+100]；
7、新增站点关闭功能；
8、新增站点访问IP黑白名单功能；
9、升级layui到最新版本。

PbootCMS V2.0.3 build 2019-10-25
1、修复一处PHP7环境下的安全漏洞；
2、新增关闭留言、表单功能的开关；
3、新增缩略图未上传时自动获取文章图片；
4、其他问题修复与优化。

PbootCMS V2.0.2 build 2019-09-15
1、伪静态时不再允许访问普通模式地址；
2、新增xx:ispics判断是否有多图；
3、新增页面内容关键词过滤功能；
4、升级layui到最新版本；
5、其他问题修复与优化。

PbootCMS V2.0.1 build 2019-08-15
1、优化编辑器工具栏浮动功能；
2、优化数据库备份文件名称;
3、优化基础环境组件检查功能；
4、修复默认模板一处链接错误；
5、修复API一处提示文字错误；
6、修复内链替换alt、title内容的问题;
7、去除内置测试账号，避免被恶意利用；

PbootCMS V2.0.0 build 2019-08-12
1、全新多种URL地址模式上线，模型、栏目、内容自由后台设置；
2、后台不再依赖pathinfo提高环境兼容性；
3、新增模型下面有栏目时不允许直接删除；
4、调整跨语言区域调用内容不再自动切换区域;
5、新增上下篇preico标签调用缩略图；
6、优化在线更新文件过多时下载问题；
7、优化sitamap生成格式；
8、其他大量系统框架优化及调整;
9、修复个别空间商的兼容性问题；

PbootCMS V1.4.3 build 2019-08-10
1、修复调用指定内容单页链接指向错误问题；
2、优化调整模板引擎链接生成过程；
3、优化在线更新功能；
4、新增升级到2.X分支版本的通道；
5、其他问题修复与优化；

PbootCMS V1.4.2 build 2019-07-22
1、修复内容tags为空时也有代码输出问题；
2、新增文章内链替换次数系统配置参数；
3、新增修改栏目时可选同步修改子栏目模板的功能；
4、新增批量添加栏目功能；
5、新增自定义表单添加到左侧导航菜单功能；
6、修复角色修改时区域数量显示不全问题；
7、修复IE浏览器翻页兼容性问题；
8、优化调整内链替换为长关键字优先；
9、新增连续多次登录失败后锁定IP功能；
10、新增自动记住上次添加内容栏目功能；
11、修复一处可能被利用的安全漏洞（感谢stick.wang@Zionlab）
12、新增表单提交验证码独立开关并优化邮箱提醒标题；
13、其他问题修复与优化；

PbootCMS V1.4.1 build 2019-07-12
1、扩大自定义字段默认长度；
2、升级layui为最新版本；
3、修复后台IE浏览器部分兼容性问题；
4、修复新增区域无法绑定域名问题；
5、修复图片无法拖动排序问题；
6、修复单独修改用户角色不成功问题；
7、修复微信分享参数导致安全拦截提示问题；
8、修复登录失败时验证码不刷新问题；
9、修复编辑器未能按设置大小缩放图片问题；
10、优化合并邮箱配置功能到系统配置；
11、增加会话文件路径后台切换功能；
12、新增单页模型也在左侧导航中显示；
13、新增文章图片水印功能；
14、新增文章内容内链功能；
15、其他问题修复与优化；

PbootCMS V1.4.0 build 2019-06-07
1、修复部分浏览器多图无法删除的问题；
2、新增万能授权码填写后自动后台隐藏功能；
3、修复QQ中打开搜索页面附加参数导致错误问题；
4、修复后台密码泄露后系统配置可能构成的安全问题；

PbootCMS V1.3.9 build 2019-05-15
1、优化相关目录权限不足报错机制；
2、修复PHP56有时后台更新无法使用问题；
3、优化后台在线升级功能；
4、新增中文路径部署检测；
5、优化升级系统框架模板引擎；
6、新增自定义路由正则匹配的支持；
7、新增后台配置数字分页条数量；
8、其他问题修复与优化；

PbootCMS V1.3.8 build 2019-04-12
1、新增同时支持fsockopen及stream_socket_client发送邮件；
2、优化邮件发送失败错误提示信息；
3、新增手机版绑定域名后自动跳转到对应域名地址上；
4、修改会话文件默认为使用系统环境缓存路径；
5、修复mysqli配置文件读取端口不一致问题；
6、修复tags为空时仍然会输出代码字符问题；
7、修复开启缓存功能后异样地址会导致文件产生问题；
8、修复编辑器在PHP7下多图上传名字重复问题；
9、修复编辑器多图上传位置错乱问题；

PbootCMS V1.3.7 build 2019-03-12
1、修复部分情况数据库语句执行发生的错误不能正常显示的问题；
2、修复搜索时表单传递字段控制被过滤掉的问题；
3、新增多个搜索通过传递searchtpl字段自定义不同搜索结果页模板；
4、修复分享非默认语言链接后无法直接访问问题；
5、新增跨语言调用内容及列表的支持；
6、新增留言使用lg=*调取指定语言留言记录功能；
7、新增order=random调取随机排序内容列表；
8、修复上传图片字段使用网络图片时地址错误问题；
9、修复栏目seo标题使用自动标题时不显示问题；

PbootCMS V1.3.6 build 2019-01-12
1、框架问题修复与优化;
2、优化后台在线升级功能;
3、修复api内容列表无法传递自定义排序问题;
4、去除后台执行sql语句功能;
5、新增超级管理员清空日志功能；
6、修复测试邮件发送失败返回中文乱码的问题;

PbootCMS V1.3.5 build 2018-12-24
1、修复邮件测试功能被误拦截问题;
2、修复使用首页分页与筛选时出现两种链接类型问题;
3、修复编辑器工具栏不能正常浮动到顶部及视频插入问题;
4、修复gif不能正常缩放及png透明图片缩放后背景变黑问题;
5、新增内容截取时使用more='*'设置省略号内容;
6、新增调节参数maxwidth、maxheight、height、width对图片进行处理;
7、进一步加强系统入侵防御能力;
8、后台主页、验证码、上传、默认模板等的改进与优化;
9、其他问题修复与优化;

PbootCMS V1.3.3 build 2018-11-29
1、新增sort指定栏目输出支持后台排序规则;
2、修复PHP7.3环境下的一些兼容性问题;
3、修改自定义表单数据调用列表为formlist标签，避免标签冲突问题;
4、修复多个列表的页面筛选对非筛选列表干扰问题;
5、修复if判断详情内容是否为空无效问题;
6、修复自定义字段英文单词之间空格被过滤掉的问题;
7、修复一处可能导致恶意利用的漏洞（重要）;
8、进一步增强系统框架安全防御策略;
9、验证码加入英文字母增强复杂度;

PbootCMS V1.3.2 build 2018-11-22
1、修复用户名验证规则不统一导致修改特殊字符后无法登录问题;
2、修复js无法获取到点赞及反对cookie的问题;
3、修复content标签无法使用scode调取单页问题;
4、新增start=*设置数据列表起始数据;
5、加强后台执行数据库脚本功能的安全性;
6、新增默认正文内容不再对PB标签进行解析;
7、修复上版本API搜索接口无法正常实现功能的问题;
8、新增lencn=* 截取长度,解决中英文显示长度不同问题;
9、其他问题修复与优化。

PbootCMS V1.3.1 build 2018-11-14
1、优化在线升级功能;
2、修复表单Ajax提交返回状态码不正确问题;
3、优化会话层级目录创建，避免会话目录缺失导致登录失败的情况;

PbootCMS V1.3.0 build 2018-11-12
1、优化会话创建机制,避免会话文件过多问题;
2、修复一些可能导致注入的安全问题(感谢topsec(zhan_ran)及CNVD的反馈);
3、修复搜索结果无法按预期显示的的问题;
4、新增内容页、列表页404自动匹配的支持;
5、新增支持在根目录定义sn.html文件自定义免费授权码填写提示;
6、新增API通过scode来调用单篇内容;
7、新增[nav:rows]输出该菜单下内容数量;
8、新增[sort:rows]输出该栏目下内容数量;
9、新增[sort:toprows]输出顶级栏目下内容数量;
10、新增[sort:parentrows]输出父栏目下内容数量;
11、新增内置tags功能，不再需要扩展字段实现;
12、新增输出指定内容或栏目tags标签功能;
13、新增列表使用参数tags='*,*'过滤列表;
14、新增列表使用参数scode=*匹配全部栏目;
15、新增指定内容输出单页时支持scode来指定内容;
16、新增栏目独立seo标题设置字段[nav:title];
17、新增开启iis\apache伪静态时自动拷贝伪静态规则;
18、新增后台内容列表百度站长推送及熊掌号推送;
19、新增多语言\区域绑定域名功能（站群系统）;
20、新增留言及表单记录调用时使用page=0关闭分页;
21、其他问题修复与优化。

PbootCMS V1.2.2 build 2018-10-12
1、修复自定义表单多选类型提交数据错误问题;
2、修复发送邮件设置完成不立即生效问题;
3、修复发送测试邮件不能正确返回状态的问题;
4、修复分页代码数字条省略号不准问题;
5、修复后台编辑器的一些问题;
6、修复自定义标签的图片不自适应多级目录问题;
7、修复使用mysql数据库时可能导致的安全问题;
8、修复列表筛选与指定分类列表冲突问题;
9、修复多次调用内容标签导致重复计算访问量问题;
10、大幅提高Sqlite数据库数据写入速度;
11、优化后台登录错误处理机制;
12、优化数据列表排序方式;
13、新增模板嵌套引用的支持;
14、新增在线更新涉及数据库时自动备份数据库;
15、新增在线更新分支选择功能;
16、新增对指定代码块不做标签解析的标签;
17、新增列表多条件排序自定义任意组合的支持,如:order='istop desc,id asc'
18、新增MySQL数据库事务的支持;
19、新增自定义标签多行文本类型;
20、新增文章定时发布的支持;
21、其它问题修复与优化。

PbootCMS V1.2.1 build 2018-09-12
1、新增在线升级新版本红点提示;
2、新增程序部署到非根目录时虚拟目录大小写不区分;
3、新增表单提交频率安全检测;
4、调整程序非伪静态时不再显示html后缀;
5、调整程序留言表单外的自定义表单不再要求验证码;
6、修复上版本首页分页不正常问题;
7、优化完善默认模板;

PbootCMS V1.2.0 build 2018-09-04
1、系统优化及安全修复;
2、修复API搜索与页面实现不统一;
3、优化后台内容页栏目显示逻辑;
4、新增后台轮播、链接页面批量排序;
5、升级layui到最新版本;
6、新增列表标签isico、ispics、istop、isrecommend、isheadline调节参数;
7、新增面包屑连接符separatoriconc及indexicon参数设置字体图标class;
8、优化分页条代码;
9、新增内容截取时自动添加省略号;
10、基于bootstrap v4的全新默认模板正式上线;
11、修复在线更新浏览器兼容性问题;

PbootCMS V1.1.9 build 2018-08-17
1、系统优化及安全修复(重要);
2、修复自定义表单Mysql时添加失败问题;
3、修复后台模板一些小错误;
4、修复影响缓存效果的一些问题;
5、新增后台在线更新功能;
6、新增附件大小标签;

PbootCMS V1.1.8 build 2018-08-07
1、修复提交表单多选字段接收数据问题;
2、修复登录过程中二次登录在页面不刷新时验证失败问题;
3、新增搜索结果fuzzy参数来控制是否模糊匹配;
4、新增父分类、顶级分类名称及链接独立标签，具体见手册;
5、新增内容多图拖动排序功能;

PbootCMS V1.1.7 build 2018-08-01
1、修复测试邮件不发送到填写的测试邮箱问题;
2、修复友情链接添加时排序报错问题;
3、修复API幻灯片及友情链接排序无效;
4、修复站内Ajax调用API数据时间戳过期问题;
5、新增自定义标签开关类型;
6、新增自定义表单数据调取标签;
7、新增单页设置跳转后内容不再出现管理列表中;
8、新增搜索支持多字段同时匹配;
9、新增点击上传的图片自动添加到编辑器;
10、优化表单提交验证;
11、其它问题修复与优化;

PbootCMS V1.1.6 build 2018-07-21
1、优化编辑器配置及安全加固;
2、优化内容管理列表布局;
3、增强系统后台及标签解析引擎安全性;
4、调整runtime标签展示方式;
5、新增Gzip页面压缩功能及后台开关;
6、新增支持根目录定义404.html匹配错误URL访问;
7、新增缩略图自动缩放更小图功能;
8、新增config/route.php文件里定义路由规则的支持;
9、新增调用指定栏目 标签scode支持多个分类;
10、新增如果栏目及内容关键字标签为空时自动使用全局关键字;
11、新增如果栏目及内容描述标签为空时自动使用全局描述;
12、新增全局自动页面标题标签{pboot:pagetitle};
13、新增全局自动页面关键字标签{pboot:pagekeywords};
14、新增全局自动页面描述标签{pboot:pagedescription}
15、新增留言表单添加更多字段的支持;
16、新增幻灯片、友情链接、扩展字段排序的支持;
17、新增sitemap生成，读取地址/index.php/sitemap;
18、新增URL路径支持中文;
19、新增中文用户名登录的支持;
20、新增API正文内容返回图片路径自动添加网址路径;
21、新增邮件配置中发送测试邮件功能;
22、新增API调用自定义表单数据接口;
23、其它问题修复与优化;
注意：升级到此版本需要同时升级数据库，脚本见升级包中。

PbootCMS V1.1.5 build 2018-07-10
1、修复开启静态缓存后无法切换语言问题;
2、修复开启静态缓存后无法自动切换手机版;
3、修复数据库安全检查不准问题;
4、修复后台多选字段无法取消问题;
5、新增调试模式后台开关;
6、新增访问地址不存在时404状态信息;
7、新增运行时间标签{pboot:runtime};
8、新增留言验证码判断标签{pboot:checkcodestatus};
9、新增筛选全部按钮的class和active分别设置;
10、新增类似filter=title|php,asp多条件过滤支持;
11、新增搜索列表scode、field调节控制参数支持;
12、新增任意字段搜索及综合搜索支持;
13、新增列表零开头序号标签[list:n];
14、新增loop循环语句标签;
15、新增模型字段下拉选择类型;
16、优化内容页访问量代码插入方式;
17、优化会话文件处理方式;
18、优化框架模板解析引擎;
19、优化后台单页数据读取方式;
20、优化系统配置载入方式;
21、优化API数据读取;

PbootCMS V1.1.4 build 2018-06-25
1、修复自定义表单表名重复仍然添加成功问题;
2、修复分享到微信导致页面错误的问题;
3、修复静态缓存失效问题并提高缓存效果;
4、新增多条件筛选时多选类型支持;
5、新增全站多条件筛选的支持;
6、新增登录时数据库目录写入权限判断提示;
7、新增后台开启高速静态缓存功能;
8、新增后台填写授权码功能;
9、新增指定列表scode使用逗号调用多个分类功能;
10、新增{pboot:keyword}标签;
11、新增数据库安全提示及修改功能;

PbootCMS V1.1.3 build 2018-06-15
1、优化Sqlite查询过程高并发锁死问题;
2、优化编辑器配置;
3、优化标签参数获取方法;
4、修复部分环境截取描述文本出现乱码问题;
5、修复API搜索数据获取方式错误;
6、新增API获取指定分类子类接口;
7、新增多条件筛选功能;
8、新增无缩略图时自动调用默认图功能;
9、新增上下篇标签参数notext定义"没有了"文本;
10、新增内容管理复制和移动功能;

PbootCMS V1.1.2 build 2018-06-12
1、修复httpurl非标准端口时解析错误;
2、修复内容为空时提取描述报警告问题;
3、修复非根目录情况下图片路径使用IF判断不准问题;
4、修复授权码带空格时导致无法正常匹配问题;
5、修复数据库中存在乱码时读取异常问题;
6、修复面包屑不显示当前分类的问题;
7、修复API中数据调用的一些错误;
8、新增表单数据删除功能;
9、新增栏目自定义路径名称功能;
10、新增留言和自定义表单提交API;

PbootCMS V1.1.1 build 2018-06-03
1、更新Layui到最新版本;
2、修复表备份无数据时处理错误;
3、优化添加自定义字段数据库处理方式;
4、修复修改模型时类型默认不选中问题;
5、修复在单页面多次调用面包屑数据重复问题;
6、修复访客留言不显示问题;
7、新增删除栏目时彻底删除本类及子类内容;
8、修复单页预览按钮地址错误;
9、新增自定义表单功能,调用方法见手册;
10、新增二维码生成标签及当前域名获取标签;
11、新增添加内容时自动提取内容描述文字;
12、新增文件上传过程提示图标;
13、新增数据库管理执行Sql脚本功能;
注意：升级到此版本需要同时升级数据库，脚本见升级包中。

PbootCMS V1.1.0 build 2018-05-24
1、修复新增语言后站点和公司信息图片首次出现警告问题;
2、优化文章访问量计数方式;
3、新增扩展字段编辑器类型;
4、新增后台文章列表有图的标注;
5、修复模板部分手误错误;
6、增强搜索时字段安全性;
7、新增PHP环境自动转义判断;
8、新增总页数、总行数分页标签;
9、调整独立分页标签为不带文字的链接;
10、修复多图多次输出num干扰问题;
11、修复新增语言后未做保存导致手机版切换错误;

PbootCMS V1.0.9 build 2018-05-14
1、新增图片上传直接显示及删除功能;
2、修复内容过滤参数及状态失效问题;
3、优化后台菜单显示视觉流畅性;
4、框架入口文件安全加强;
5、面包屑新增indextext参数，配置"首页"文字;
6、新增API认证签名生成标签，方便进行Ajax加载，具体见手册示例;
7、新增上一篇及下一篇链接及标题独立标签，并支持len截取;

PbootCMS V1.0.8 build 2018-05-07
1、修复后台菜单高亮错误;
2、修改编辑器为不替换div和不自动拉高;
3、修复删除栏目时子类未全部删除问题;
4、新增后台栏目管理折叠功能;
5、新增手机版绑定独立域名功能;
6、修复多语言切换时栏目编码导致报错;
7、修复副栏目内容无法正常调取问题;
8、新增指定内容扩展字段多选输出标签;

PbootCMS V1.0.7 build 2018-05-02
1、采用LayUI新版管理后台;
2、修复后台单页无法显示更多问题;
3、调整默认分页数字条长度为5;
4、修复API调用全部自定义标签错误;
5、新增内容批量删除功能;
6、新增栏目批量删除功能;
7、修复面包屑对外链跳转问题;
8、修复栏目修改直接保存类型错误;
9、其它问题修复和优化;

PbootCMS V1.0.6 build 2018-04-24
1、修复无任何内容执行排序报错;
2、修复可能存在的跨站请求伪造漏洞;

PbootCMS V1.0.5 build 2018-04-23
1、新增后台配置项自动新增功能;
2、修复logo不自适应多级目录路径;
3、新增十类API调用，方便小程序、APP、Ajax等数据获取;
4、修复个别虚拟主机分页代码报警告问题;
5、新增服务器未开启sockets导致非加密发送邮件错误提示;


PbootCMS V1.0.4 build 2018-04-19
1、优化框架配置文件载入方式;
2、修复当前分类列表页过滤参数无效;
3、新增首页分页功能，大家可以做博客了;
4、新增模型类菜单桌面同步显示;
5、新增当前栏目位置标签;

PbootCMS V1.0.3 build 2018-04-16
1、修复多图输出只输出一张问题;
2、修复PHP7.1中合并多维数组警告;
3、修复登录地址末尾带有斜杠时错误;
4、新增配置文件自动去除Bom,避免空白;

PbootCMS V1.0.2 build 2018-04-14
1、新增自动去除模板的Bom信息，避免空白;
2、优化编辑器可见按钮;
3、修复编辑器无法插入动态地图问题;
4、修复文章排序无法按预期排列问题;

PbootCMS V1.0.1 build 2018-04-13
1、修复数据库导出未正常解码问题;
2、修复系统栏目出现多个新闻内容问题;
3、新增默认及内置区域不允许删除;
4、新增删除区域后强制重新登录;
5、修复前端多语言无法正常切换问题;
6、修复新增语言无法匹配默认模板问题;
7、Mysql脚本默认内容解码有问题，大家重新导入sql;
8、修复友情链接图片无法自适应多级目录;

PbootCMS V1.0.0 build 2018-04-12
1、正式发布第一个版本。
