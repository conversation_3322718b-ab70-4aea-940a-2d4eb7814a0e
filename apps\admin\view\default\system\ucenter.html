{include file='common/head.html'}

<div class="layui-body">

	{if(![$session.pwsecurity])}
	<blockquote class="layui-elem-quote layui-text-red" id="note">
		{if(![$session.pwsecurity])}
		<p>
			<i class="fa fa-info-circle" aria-hidden="true"></i>
			 您的账号密码为初始密码，存在安全隐患，必须修改密码才能查看后台主页！
		</p>
		{/if}
    </blockquote>
    {/if}
    
	<div class="layui-tab layui-tab-brief" lay-filter="tab">
	  <ul class="layui-tab-title">
	    <li class="layui-this">资料修改</li>
	  </ul>
	  <div class="layui-tab-content">
	  	   <div class="layui-tab-item layui-show">
	  	   		<form action="{URL}" method="post" class="layui-form">
	  	   			<input type="hidden" name="formcheck" value="{$formcheck}" > 
	  	   			<div class="layui-form-item">
	                     <label class="layui-form-label">用户账号</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="username" required  lay-verify="required" value="{$session.username}" placeholder="请输入用户账号" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">真实姓名</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="realname" value="{$session.realname}"  placeholder="请输入真实姓名" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">当前密码</label>
	                     <div class="layui-input-block">
	                     	<input type="password" name="cpassword" required lay-verify="required" placeholder="请输入当前密码" autocomplete="off" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">用户密码</label>
	                     <div class="layui-input-block">
	                     	<input type="password" name="password"  placeholder="请输入用户密码" autocomplete="off" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">确认密码</label>
	                     <div class="layui-input-block">
	                     	<input type="password" name="rpassword"  placeholder="请输入确认密码" autocomplete="off" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
						 <div class="layui-input-block">
						    <button class="layui-btn" lay-submit>立即提交</button>
						    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
						 </div>
				    </div>
	  	   		</form>
	  	   </div>
	  </div>
	 </div>
</div>

{include file='common/foot.html'}
