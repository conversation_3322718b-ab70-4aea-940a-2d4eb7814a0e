<?php
/**
 * @copyright (C)2016-2099 Hnaoyun Inc.
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017年11月4日
 *  内核启动类
 */

namespace core\basic;

class Kernel
{
    // 存储URL路径数组
    private static $pathArray;

    /**
     * 系统启动入口
     */
    public static function run()
    {
        // 检查缓存
        self::checkCache();

        // 解析路径信息
        $pathInfo = self::parsePathInfo();

        // 处理域名绑定
        $pathInfo = self::handleDomainBinding($pathInfo);

        // 处理URL路由
        $pathInfo = self::handleUrlRoute($pathInfo);

        // 解析MVC路径
        $mvcPath = self::parseMvcPath($pathInfo);

        // 构建控制器路径
        $controllerPath = self::buildControllerPath($mvcPath);

        // 初始化应用
        self::initApplication();

        // 检查授权
        self::checkLicense();

        // 执行控制器
        self::executeController($controllerPath);
    }

    /**
     * 解析路径信息
     * @return string 解析后的路径信息
     */
    private static function parsePathInfo()
    {
        // 字符编码转换处理
        if (isset($_SERVER['PATH_INFO']) && !mb_check_encoding($_SERVER['PATH_INFO'], 'UTF-8')) {
            $_SERVER['PATH_INFO'] = mb_convert_encoding($_SERVER['PATH_INFO'], 'utf-8', 'GBK');
        }
        if (isset($_SERVER['REQUEST_URI']) && !mb_check_encoding($_SERVER['REQUEST_URI'], 'UTF-8')) {
            $_SERVER['REQUEST_URI'] = mb_convert_encoding($_SERVER['REQUEST_URI'], 'utf-8', 'GBK');
        }
        if (isset($_SERVER['ORIG_PATH_INFO']) && !mb_check_encoding($_SERVER['ORIG_PATH_INFO'], 'UTF-8')) {
            $_SERVER['ORIG_PATH_INFO'] = mb_convert_encoding($_SERVER['ORIG_PATH_INFO'], 'utf-8', 'GBK');
        }

        $pathInfo = '';

        // 获取PATH_INFO
        if (isset($_SERVER['PATH_INFO']) && $_SERVER['PATH_INFO']) {
            $pathInfo = $_SERVER['PATH_INFO'];
        } elseif (isset($_SERVER["REDIRECT_URL"]) && $_SERVER["REDIRECT_URL"]) {
            $pathInfo = str_replace('/' . basename($_SERVER['SCRIPT_NAME']), '', $_SERVER['REDIRECT_URL']);
            $pathInfo = str_replace(SITE_DIR, '', $pathInfo);
            $_SERVER['PATH_INFO'] = $pathInfo;
        }

        // 从GET参数获取路径信息
        if (!$pathInfo) {
            if (isset($_GET['p']) && $_GET['p']) {
                $pathInfo = $_GET['p'];
            } elseif (isset($_GET['s']) && $_GET['s']) {
                $pathInfo = $_GET['s'];
            }
        }

        // 路径安全检查
        if ($pathInfo) {
            $pattern = '{^\/?([\x{4e00}-\x{9fa5}\w\-\/\.' . Config::get('url_allow_char') . ']+?)?$}u';
            if (preg_match($pattern, $pathInfo)) {
                $pathInfo = preg_replace($pattern, '$1', $pathInfo);
            } else {
                $hasIllegalChar = true;
            }
        }

        // 处理非法字符
        if (isset($hasIllegalChar) && $hasIllegalChar) {
            http_response_code(404);
            $defendFile = ROOT_PATH . '/defend.html';
            if (file_exists($defendFile)) {
                require $defendFile;
                exit();
            } else {
                error('您访问路径含有非法字符，防注入系统提醒您请勿尝试非法操作！');
            }
        }

        define('P', $pathInfo);
        return $pathInfo;
    }

    /**
     * 处理域名绑定
     * @param string $pathInfo 路径信息
     * @return string 处理后的路径信息
     */
    private static function handleDomainBinding($pathInfo)
    {
        $module = '';

        // 检查域名绑定配置
        if ($domainBindConfig = Config::get('app_domain_bind')) {
            $currentHost = get_http_host();
            if (isset($domainBindConfig[$currentHost])) {
                $module = $domainBindConfig[$currentHost];
            }
        }

        // 检查入口文件绑定
        if (defined('URL_BIND')) {
            if ($module && URL_BIND != $module) {
                error('系统配置的模块域名绑定与入口文件绑定冲突，请核对！');
            } else {
                $module = URL_BIND;
            }
        }

        return $module ? trim_slash($module) . '/' . $pathInfo : $pathInfo;
    }

    /**
     * 处理URL路由
     * @param string $pathInfo 路径信息
     * @return string 处理后的路径信息
     */
    private static function handleUrlRoute($pathInfo)
    {
        if ($urlRoutes = Config::get('url_route')) {
            // 处理根路径路由
            if (!$pathInfo && isset($urlRoutes['/'])) {
                return $urlRoutes['/'];
            }

            // 遍历路由规则
            foreach ($urlRoutes as $pattern => $target) {
                $pattern = trim_slash($pattern);
                $regex = "{" . $pattern . "}i";
                if (preg_match($regex, $pathInfo)) {
                    $target = trim_slash($target);
                    $pathInfo = preg_replace($regex, $target, $pathInfo);
                    break;
                }
            }
        }

        return $pathInfo;
    }

    /**
     * 解析MVC路径
     * @param string $pathInfo 路径信息
     * @return array MVC路径数组
     */
    private static function parseMvcPath($pathInfo)
    {
        $publicApps = Config::get('public_app', true);
        $mvcPath = array();

        if ($pathInfo) {
            $pathInfo = trim_slash($pathInfo);
            $pathArray = explode('/', $pathInfo);
            self::$pathArray = $pathArray;
            $pathCount = count($pathArray);

            // 根据路径段数量解析MVC
            if ($pathCount >= 3) {
                $mvcPath['m'] = $pathArray[0]; // 模块
                $mvcPath['c'] = $pathArray[1]; // 控制器
                $mvcPath['f'] = $pathArray[2]; // 方法
            } elseif ($pathCount == 2) {
                $mvcPath['m'] = $pathArray[0]; // 模块
                $mvcPath['c'] = $pathArray[1]; // 控制器
            } elseif ($pathCount == 1) {
                $mvcPath['m'] = $pathArray[0]; // 模块
            }
        }

        // 设置默认值
        if (!isset($mvcPath['m'])) {
            $mvcPath['m'] = $publicApps[0]; // 默认模块
        }
        if (!isset($mvcPath['c'])) {
            $mvcPath['c'] = 'Index'; // 默认控制器
        }
        if (!isset($mvcPath['f'])) {
            $mvcPath['f'] = 'index'; // 默认方法
        }

        // 检查模块是否开放
        if (!in_array(strtolower($mvcPath['m']), $publicApps)) {
            error('您访问的模块' . $mvcPath['m'] . '未开放,请核对后重试！');
        }

        return $mvcPath;
    }

    /**
     * 构建控制器路径
     * @param array $mvcPath MVC路径数组
     * @return string 控制器路径
     */
    private static function buildControllerPath($mvcPath)
    {
        // 定义模块常量
        define('M', strtolower($mvcPath['m']));
        define('APP_MODEL_PATH', APP_PATH . '/' . M . '/model');
        define('APP_CONTROLLER_PATH', APP_PATH . '/' . M . '/controller');

        // 设置视图路径
        if (($templateDirs = Config::get('tpl_dir')) && array_key_exists(M, $templateDirs)) {
            if (strpos($templateDirs[M], ROOT_PATH) === false) {
                define('APP_VIEW_PATH', ROOT_PATH . $templateDirs[M]);
            } else {
                define('APP_VIEW_PATH', $templateDirs[M]);
            }
        } else {
            define('APP_VIEW_PATH', APP_PATH . '/' . M . '/view');
        }

        // 处理控制器路径（支持子目录）
        if (strpos($mvcPath['c'], '.') > 0) {
            $controllerPath = str_replace('.', '/', $mvcPath['c']);
            $controller = ucfirst(basename($controllerPath));
            $controllerPath = dirname($controllerPath) . '/' . $controller;
        } else {
            $controller = ucfirst($mvcPath['c']);
            $controllerPath = $controller;
        }

        $controllerFile = APP_CONTROLLER_PATH . '/' . $controllerPath . 'Controller.php';
        $reservedControllers = array('List', 'Content', 'About');
        $paramOffset = 0;

        // 处理前台特殊控制器
        if (M == 'home' && (!file_exists($controllerFile) || in_array($controller, $reservedControllers))) {
            $controller = 'Index';
            $controllerPath = 'Index';
            define('F', $mvcPath['c']);
            $paramOffset = -1;
        } elseif (M == 'home' && in_array($controller, Config::get('second_rvar'))) {
            define('F', 'index');
            define('RVAR', $mvcPath['f']);
        } else {
            define('F', $mvcPath['f']);
        }

        define('C', $controller);

        // 定义URL常量
        if (isset($_SERVER["REQUEST_URI"])) {
            define('URL', $_SERVER["REQUEST_URI"]);
        } else {
            define('URL', $_SERVER["ORIG_PATH_INFO"] . '?' . $_SERVER["QUERY_STRING"]);
        }

        // 解析URL参数到$_GET
        $pathCount = count(self::$pathArray);
        for ($i = 3 + $paramOffset; $i < $pathCount; $i = $i + 2) {
            if (isset(self::$pathArray[$i + 1])) {
                $_GET[self::$pathArray[$i]] = self::$pathArray[$i + 1];
            } else {
                $_GET[self::$pathArray[$i]] = null;
            }
        }

        return $controllerPath;
    }

    /**
     * 初始化应用
     */
    private static function initApplication()
    {
        // 调试模式下检查应用文件
        Config::get('debug') ? Check::checkAppFile() : '';

        // API模块特殊处理
        if (M == 'api') {
            // 处理跨域会话
            if ($sessionId = request('sid')) {
                session_id($sessionId);
                session_start();
            }
            header("Access-Control-Allow-Origin: *");
        } else {
            // 检查浏览器和操作系统
            Check::checkBs();
            Check::checkOs();
        }

        // 加载公共函数文件
        if (file_exists(APP_PATH . '/common/function.php')) {
            require APP_PATH . '/common/function.php';
        }

        // 加载模块配置文件
        $moduleConfigFile = APP_PATH . '/' . M . '/config/config.php';
        if (file_exists($moduleConfigFile)) {
            Config::assign($moduleConfigFile);
        }

        // 加载模块函数文件
        $moduleFunctionFile = APP_PATH . '/' . M . '/function/function.php';
        if (file_exists($moduleFunctionFile)) {
            require $moduleFunctionFile;
        }

        // 实例化公共控制器
        if (file_exists(APP_PATH . '/common/' . ucfirst(M) . 'Controller.php')) {
            $commonControllerClass = '\\app\\common\\' . ucfirst(M) . 'Controller';
            $commonController = new $commonControllerClass();
        }
    }

    /**
     * 执行控制器
     * @param string $controllerPath 控制器路径
     */
    private static function executeController($controllerPath)
    {
        $controllerFileName = $controllerPath . 'Controller.php';
        $controllerFilePath = APP_CONTROLLER_PATH . '/' . $controllerFileName;
        $controllerClassName = '\\app\\' . M . '\\controller\\' . str_replace('/', '\\', $controllerPath) . 'Controller';
        $methodName = F;

        // 检查控制器文件是否存在
        if (!file_exists($controllerFilePath)) {
            http_response_code(404);
            $notFoundFile = ROOT_PATH . '/404.html';
            if (file_exists($notFoundFile)) {
                require $notFoundFile;
                exit();
            } else {
                error('对不起，您访问的页面类文件不存在，请核对后再试！');
            }
        }

        // 检查控制器类是否存在
        if (!class_exists($controllerClassName)) {
            error('类文件中不存在访问的类名！' . $controllerClassName);
        }

        // 实例化控制器
        $controller = new $controllerClassName();

        // 执行方法
        if (method_exists($controllerClassName, $methodName)) {
            if (strtolower($controllerClassName) != strtolower($methodName)) {
                $result = $controller->$methodName();
            } else {
                $result = $controller;
            }
        } else {
            // 尝试调用空方法拦截器
            if (method_exists($controllerClassName, '_empty')) {
                $result = $controller->_empty();
            } else {
                error('不存在您调用的类或方法' . $methodName . '，可能正在开发中，请耐心等待！');
            }
        }

        // 输出结果
        if ($result !== null) {
            print_r($result);
            exit();
        }
    }

    /**
     * 检查缓存
     */
    private static function checkCache()
    {
        // 如果未开启缓存或是API模块或禁用缓存参数，则跳过
        if (!Config::get('tpl_html_cache') || URL_BIND == 'api' || get('nocache', 'int') == 1) {
            return;
        }

        // 加载区域配置
        $areaConfigFile = RUN_PATH . '/config/' . md5('area') . '.php';
        if (!file_exists($areaConfigFile)) {
            return;
        } else {
            Config::assign($areaConfigFile);
        }

        // 处理多语言
        $languages = Config::get('lgs');
        if (count($languages) > 1) {
            $currentHost = get_http_host();
            foreach ($languages as $language) {
                if ($language['domain'] == $currentHost) {
                    cookie('lg', $language['acode']);
                }
            }
        }

        // 设置默认语言
        if (!isset($_COOKIE['lg'])) {
            $defaultLanguage = current(Config::get('lgs'));
            cookie('lg', $defaultLanguage['acode']);
        }

        // 加载系统配置
        $configFile = RUN_PATH . '/config/' . md5('config') . '.php';
        if (!Config::assign($configFile)) {
            return;
        }

        // 判断是否为手机版
        if (Config::get('open_wap') && (is_mobile() || Config::get('wap_domain') == get_http_host())) {
            $deviceType = 'wap';
        } else {
            $deviceType = '';
        }

        // 生成缓存文件路径
        $cacheFile = RUN_PATH . '/cache/' . md5(get_http_url() . $_SERVER["REQUEST_URI"] . cookie('lg') . $deviceType) . '.html';

        // 检查缓存是否有效
        if (file_exists($cacheFile) && time() - filemtime($cacheFile) < Config::get('tpl_html_cache_time')) {
            ob_start();
            include $cacheFile;
            $content = ob_get_contents();
            ob_end_clean();

            // 启用GZIP压缩
            if (Config::get('gzip') && !headers_sent() && extension_loaded("zlib") && strstr($_SERVER["HTTP_ACCEPT_ENCODING"], "gzip")) {
                $content = gzencode($content, 6);
                header("Content-Encoding: gzip");
                header("Vary: Accept-Encoding");
                header("Content-Length: " . strlen($content));
            }

            echo $content;
            exit();
        }
    }

    /**
     * 检查授权
     */
    private static function checkLicense()
    {
        // 获取服务器IP地址
        $serverIp = isset($_SERVER['LOCAL_ADDR']) ? $_SERVER['LOCAL_ADDR'] : $_SERVER['SERVER_ADDR'];
        if ($serverIp == '::1') {
            $serverIp = '127.0.0.1';
        }

        $licenseStatus = 0;

        // 检查授权码
        if ($licenseCode = Config::get('licensecode')) {
            $licenseCode = explode('/', base64_decode(substr($licenseCode, 0, -1)));
            $authorizedList = explode(',', $licenseCode[0]);
            $authorizedUser = $licenseCode[1];
        } else {
            $authorizedList = Config::get('sn', true);
            $authorizedUser = Config::get('sn_user');
        }

        if ($authorizedList) {
            // 检查用户授权
            $userHash = strtoupper(substr(md5(substr(sha1($authorizedUser), 0, 20)), 10, 10));
            $licenseStatus = $licenseStatus ?: (in_array($userHash, $authorizedList) ? 3 : 0);

            // 检查服务器IP授权
            $hostHash = strtoupper(substr(md5(substr(sha1($serverIp), 0, 15)), 10, 10));
            $licenseStatus = $licenseStatus ?: (in_array($hostHash, $authorizedList) ? 2 : 0);

            // 检查域名授权
            $currentDomain = isset($_SERVER['HTTP_X_FORWARDED_HOST']) ? $_SERVER['HTTP_X_FORWARDED_HOST'] : (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '');
            $domainHash = strtoupper(substr(md5(substr(sha1($currentDomain), 0, 10)), 10, 10));
            $licenseStatus = $licenseStatus ?: (in_array($domainHash, $authorizedList) ? 1 : 0);
        }

        define('LICENSE', $licenseStatus);

        // 本地开发环境跳过授权检查
        if (!LICENSE && (filter_var(get_http_host(), FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) || get_http_host() == 'localhost')) {
            return;
        }

        // 检查授权状态
        if (!$licenseStatus && (defined('URL_BIND') && URL_BIND != 'admin')) {
            $snFile = ROOT_PATH . '/sn.html';
            if (file_exists($snFile)) {
                require $snFile;
                exit();
            } else {
                error('未匹配到本域名(' . $currentDomain . ')有效授权码，请到<a href="http://www.pbootcms.com" target="_blank">PbootCMS官网</a>免费获取，并登录系统后台填写到"全局配置>>配置参数"中。');
            }
        }
    }
}
