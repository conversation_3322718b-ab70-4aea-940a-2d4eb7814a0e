/*基础CSS*/
html {
	font-size: 14px;
	overflow-x: hidden;
}
li {
	list-style: none;
}
a {
	color: #212529
}
a:hover {
	text-decoration: none;
}
p, dl, dt, dd, ul {
	margin: 0px;
	padding: 0px;
}
.red{
	color:red;
}
.text-secondary a {
	color: #6c757d;
}
.card, .figure {
	margin: 15px 0;
}
.navbar-expand-lg .navbar-nav .nav-link {
	padding-right: 1.2rem;
	padding-left: 1.2rem;
	font-size: 14px;
}
.navbar-brand {
	font-size: 20px;
	padding-top: 0px;
	padding-bottom: 0px;
}
.shadow-sm-top {
	box-shadow: 0 -.125rem .25rem rgba(0,0,0,.075) !important;
}
/*上下外边距*/
.mt-6, .my-6 {
	margin-top: 4rem !important;
}
.mt-7, .my-7 {
	margin-top: 5rem !important;
}
.mt-8, .my-8 {
	margin-top: 6rem !important;
}
.mt-9, .my-9 {
	margin-top: 7rem !important;
}
.mt-10, .my-10 {
	margin-top: 8rem !important;
}
.mb-6, .my-6 {
	margin-bottom: 4rem !important;
}
.mb-7, .my-7 {
	margin-bottom: 5rem !important;
}
.mb-8, .my-8 {
	margin-bottom: 6rem !important;
}
.mb-9, .my-9 {
	margin-bottom: 7rem !important;
}
.mb-10, .my-10 {
	margin-bottom: 8rem !important;
}
/*上下内边距*/
.pt-6, .mp-6 {
	padding-top: 4rem !important;
}
.pt-7, .py-7 {
	padding-top: 5rem !important;
}
.pt-8, .py-8 {
	padding-top: 6rem !important;
}
.pt-9, .py-9 {
	padding-top: 7rem !important;
}
.pt-10, .py-10 {
	padding-top: 8rem !important;
}
.pb-6, .py-6 {
	padding-bottom: 4rem !important;
}
.pb-7, .py-7 {
	padding-bottom: 5rem !important;
}
.pb-8, .py-8 {
	padding-bottom: 6rem !important;
}
.pb-9, .py-9 {
	padding-bottom: 7rem !important;
}
.pb-10, .py-10 {
	padding-bottom: 8rem !important;
}
/*边框*/
.border-top-dashed {
	border-top: 1px dashed #dee2e6 !important;
}
.border-right-dashed {
	border-right: 1px dashed #dee2e6 !important;
}
.border-bottom-dashed {
	border-bottom: 1px dashed #dee2e6 !important;
}
.border-right-dashed {
	border-top: 1px dashed #dee2e6 !important;
}
/*字距*/
.ls-1 {
	letter-spacing: 1px;
}
.ls-2 {
	letter-spacing: 2px;
}
.ls-3 {
	letter-spacing: 3px;
}
.ls-4 {
	letter-spacing: 4px;
}
.ls-5 {
	letter-spacing: 5px;
}
/*分页*/
.page-num:not(:disabled) {
	cursor: pointer;
}
.page-num, .page-link {
	position: relative;
	display: block;
	padding: .5rem .75rem;
	margin-left: -1px;
	line-height: 1.25;
	color: #17a2b8;
	background-color: #fff;
	border: 1px solid #dee2e6;
}
a.page-num:hover, .page-link:hover {
	text-decoration: none;
}
.page-num-current {
	z-index: 1;
	color: #fff;
	background-color: #17a2b8;
	border-color: #17a2b8;
}
.page-num-current:hover {
	color: #fff;
}
/*其他*/
.pages {
	min-height: 500px;
	padding-top: 10px;
	padding-bottom: 10px;
}
.code, .code img {
	height: 100px;
}
.position, .position a {
	color: #999;
}
.content {
	color: #666;
	line-height: 2;
}
.content img, .content iframe {
	max-width: 100% !important;
}
.content li {
	list-style: disc outside none;
	margin-left: 20px;
}
/*产品轮播*/
.view {
	max-width: 500px;
}
.view .swiper-slide {
	height: 300px;
	max-width: 500px;
	padding: 0 3px;
	box-sizing: border-box;
	overflow: hidden;
	text-align: center;
}
.view .swiper-slide img {
	height: 100%;
}
.view .arrow-left {
	background: url(../images/index_tab_l.png) no-repeat left center;
	position: absolute;
	left: 10px;
	width: 28px;
	height: 300px;
	z-index: 10;
	display: none;
}
.view .arrow-right {
	background: url(../images/index_tab_r.png) no-repeat right center;
	position: absolute;
	right: 10px;
	width: 28px;
	height: 300px;
	z-index: 10;
	display: none;
}
.preview {
	position: relative;
	width: 440px;
	left: 30px;
}
.preview .swiper-slide {
	width: 87.5px;
	height: 70px;
	margin: 0 3px;
	box-sizing: border-box;
	overflow: hidden;
	cursor: pointer;
}
.preview .swiper-slide img {
	display: inline-block;
	height: 100%;
}
.preview .active-nav {
	border: 1px solid #F00;
}
.preview .arrow-left {
	background: #fff url(../images/feel3.png) no-repeat left center;
	position: absolute;
	left: -20px;
	top: 0;
	width: 20px;
	height: 70px;
	z-index: 10;
}
.preview .arrow-right {
	background: #fff url(../images/feel4.png) no-repeat right center;
	position: absolute;
	right: -20px;
	top: 0;
	width: 20px;
	height: 70px;
	z-index: 10;
}

@media only screen and (max-width: 768px) {
.view .swiper-slide {
	height: 200px;
}
.preview {
	display: none;
}
.view .arrow-left, .view .arrow-right {
	height: 200px;
	display: block;
}
}
/*在线客服*/
.online {
	position: fixed;
	bottom: 150px;
	right: 10px;
	width: 60px;
	z-index: 999;
	color: #999;
}
.online a {
	color: #666;
}
.online a:hover {
	text-decoration: none;
}
.online dl {
	background: #27a8e1;
	padding: 10px 5px;
	margin-bottom: 1px;
	position: relative;
}
.online dl dd {
	color: #fff;
	text-align: center;
	font-size: 12px;
	cursor: pointer;
}
.online dl dd i {
	font-size: 25px;
}
.online dl:hover {
	background: #1781bd;
}
.online dl dt {
	position: absolute;
	top: 0px;
	right: 60px;
	background: #fff;
	border: 1px solid #ccc;
	z-index: 9999;
	display: none;
	padding: 10px 15px;
	font-weight: normal;
}
.online dl dt h3 {
	display: block;
	font-size: 16px;
	color: #666;
	border-bottom: 1px solid #ccc;
	padding-bottom: 5px;
}
.online dl dt h3 i {
	margin-right: 5px;
}
.online dl dt h3 span {
	float: right;
	cursor: pointer;
}
.online dl dt input {
	border: 1px solid #ccc;
	border-radius: 5px;
	margin-top: 15px;
	height: 40px;
	line-height: 40px;
	padding: 3px 5px;
	width: 100%;
}
.online dl dt button {
	margin: 10px 0;
	border: none;
	border-radius: 5px;
	width: 100%;
	font-size: 18px;
	height: 40px;
	line-height: 40px;
	background: #28a7e1;
	color: #fff;
	cursor: pointer;
}

/*sm屏幕以上*/
@media only screen and (min-width: 576px) {
.card-img-200 {
	height: 200px;
	overflow: hidden;
}
.card-img-180 {
	height: 180px;
	overflow: hidden;
}
.card-img-150 {
	height: 150px;
	overflow: hidden;
}
.card-img-120 {
	height: 120px;
	overflow: hidden;
}
.card-img-100 {
	height: 100px;
	overflow: hidden;
}
}
/*sm屏幕以下*/
@media only screen and (max-width: 576px) {
.page-num {
	display: none;
}
.head-sm-height {
	height: 61px !important;
}
}
/*********响应式样式******************/

/*行距*/
.lh-1 {
	line-height: 1;
}
.lh-2 {
	line-height: 2;
}
.lh-3 {
	line-height: 3;
}
.lh-4 {
	line-height: 4;
}
/*字体大小*/
.fs-12 {
	font-size: .857rem;
}
.fs-14 {
	font-size: 1rem;
}
.fs-16 {
	font-size: 1.142rem;
}
.fs-18 {
	font-size: 1.285rem;
}
.fs-20 {
	font-size: 1.428rem;
}
.fs-22 {
	font-size: 1.571rem;
}
.fs-24 {
	font-size: 1.714rem;
}
.fs-26 {
	font-size: 1.857rem;
}
.fs-28 {
	font-size: 2rem;
}
.fs-30 {
	font-size: 2.142rem;
}
.fs-32 {
	font-size: 2.286rem;
}
.fs-34 {
	font-size: 2.429rem;
}
.fs-36 {
	font-size: 2.571rem;
}
.fs-38 {
	font-size: 2.714rem;
}
.fs-40 {
	font-size: 2.857rem;
}
/*高度*/
.h-5px {
	height: 5px;
}
.h-10px {
	height: 10px;
}
.h-15px {
	height: 15px;
}
.h-20px {
	height: 20px;
}
.h-25px {
	height: 25px;
}
.h-30px {
	height: 30px;
}
.h-35px {
	height: 35px;
}
.h-40px {
	height: 40px;
}
.h-45px {
	height: 45px;
}
.h-50px {
	height: 50px;
}
.h-55px {
	height: 55px;
}
.h-60px {
	height: 60px;
}
.h-65px {
	height: 65px;
}
.h-70px {
	height: 70px;
}
.h-75px {
	height: 75px;
}
.h-80px {
	height: 80px;
}
.h-85px {
	height: 85px;
}
.h-90px {
	height: 90px;
}
.h-95px {
	height: 95px;
}
.h-100px {
	height: 100px;
}
.h-110px {
	height: 110px;
}
.h-120px {
	height: 120px;
}
.h-130px {
	height: 130px;
}
.h-140px {
	height: 140px;
}
.h-150px {
	height: 150px;
}
.h-160px {
	height: 160px;
}
.h-170px {
	height: 170px;
}
.h-180px {
	height: 180px;
}
.h-190px {
	height: 190px;
}
.h-200px {
	height: 200px;
}
.h-210px {
	height: 210px;
}
.h-220px {
	height: 220px;
}
.h-230px {
	height: 230px;
}
.h-240px {
	height: 240px;
}
.h-250px {
	height: 250px;
}
.h-260px {
	height: 260px;
}
.h-270px {
	height: 270px;
}
.h-280px {
	height: 280px;
}
.h-290px {
	height: 290px;
}
.h-300px {
	height: 300px;
}
.h-310px {
	height: 310px;
}
.h-320px {
	height: 320px;
}
.h-330px {
	height: 330px;
}
.h-340px {
	height: 340px;
}
.h-350px {
	height: 350px;
}
.h-360px {
	height: 360px;
}
.h-370px {
	height: 370px;
}
.h-380px {
	height: 380px;
}
.h-390px {
	height: 390px;
}
.h-400px {
	height: 400px;
}
.h-410px {
	height: 410px;
}
.h-420px {
	height: 420px;
}
.h-430px {
	height: 430px;
}
.h-440px {
	height: 440px;
}
.h-450px {
	height: 450px;
}
.h-460px {
	height: 410px;
}
.h-470px {
	height: 470px;
}
.h-480px {
	height: 480px;
}
.h-490px {
	height: 490px;
}
.h-500px {
	height: 500px;
}

/*sm屏幕以上*/
@media only screen and (min-width: 576px) {
/*行距*/
.lh-sm-1 {
	line-height: 1;
}
.lh-sm-2 {
	line-height: 2;
}
.lh-sm-3 {
	line-height: 3;
}
.lh-sm-4 {
	line-height: 4;
}
/*字体大小*/
.fs-sm-12 {
	font-size: .857rem;
}
.fs-sm-14 {
	font-size: 1rem;
}
.fs-sm-16 {
	font-size: 1.142rem;
}
.fs-sm-18 {
	font-size: 1.285rem;
}
.fs-sm-20 {
	font-size: 1.428rem;
}
.fs-sm-22 {
	font-size: 1.571rem;
}
.fs-sm-24 {
	font-size: 1.714rem;
}
.fs-sm-26 {
	font-size: 1.857rem;
}
.fs-sm-28 {
	font-size: 2rem;
}
.fs-sm-30 {
	font-size: 2.142rem;
}
.fs-sm-32 {
	font-size: 2.286rem;
}
.fs-sm-34 {
	font-size: 2.429rem;
}
.fs-sm-36 {
	font-size: 2.571rem;
}
.fs-sm-38 {
	font-size: 2.714rem;
}
.fs-sm-40 {
	font-size: 2.857rem;
}
/*高度*/
.h-sm-5px {
	height: 5px;
}
.h-sm-10px {
	height: 10px;
}
.h-sm-15px {
	height: 15px;
}
.h-sm-20px {
	height: 20px;
}
.h-sm-25px {
	height: 25px;
}
.h-sm-30px {
	height: 30px;
}
.h-sm-35px {
	height: 35px;
}
.h-sm-40px {
	height: 40px;
}
.h-sm-45px {
	height: 45px;
}
.h-sm-50px {
	height: 50px;
}
.h-sm-55px {
	height: 55px;
}
.h-sm-60px {
	height: 60px;
}
.h-sm-65px {
	height: 65px;
}
.h-sm-70px {
	height: 70px;
}
.h-sm-75px {
	height: 75px;
}
.h-sm-80px {
	height: 80px;
}
.h-sm-85px {
	height: 85px;
}
.h-sm-90px {
	height: 90px;
}
.h-sm-95px {
	height: 95px;
}
.h-sm-100px {
	height: 100px;
}
.h-sm-110px {
	height: 110px;
}
.h-sm-120px {
	height: 120px;
}
.h-sm-130px {
	height: 130px;
}
.h-sm-140px {
	height: 140px;
}
.h-sm-150px {
	height: 150px;
}
.h-sm-160px {
	height: 160px;
}
.h-sm-170px {
	height: 170px;
}
.h-sm-180px {
	height: 180px;
}
.h-sm-190px {
	height: 190px;
}
.h-sm-200px {
	height: 200px;
}
.h-sm-210px {
	height: 210px;
}
.h-sm-220px {
	height: 220px;
}
.h-sm-230px {
	height: 230px;
}
.h-sm-240px {
	height: 240px;
}
.h-sm-250px {
	height: 250px;
}
.h-sm-260px {
	height: 260px;
}
.h-sm-270px {
	height: 270px;
}
.h-sm-280px {
	height: 280px;
}
.h-sm-290px {
	height: 290px;
}
.h-sm-300px {
	height: 300px;
}
.h-sm-310px {
	height: 310px;
}
.h-sm-320px {
	height: 320px;
}
.h-sm-330px {
	height: 330px;
}
.h-sm-340px {
	height: 340px;
}
.h-sm-350px {
	height: 350px;
}
.h-sm-360px {
	height: 360px;
}
.h-sm-370px {
	height: 370px;
}
.h-sm-380px {
	height: 380px;
}
.h-sm-390px {
	height: 390px;
}
.h-sm-400px {
	height: 400px;
}
.h-sm-410px {
	height: 410px;
}
.h-sm-420px {
	height: 420px;
}
.h-sm-430px {
	height: 430px;
}
.h-sm-440px {
	height: 440px;
}
.h-sm-450px {
	height: 450px;
}
.h-sm-460px {
	height: 410px;
}
.h-sm-470px {
	height: 470px;
}
.h-sm-480px {
	height: 480px;
}
.h-sm-490px {
	height: 490px;
}
.h-sm-500px {
	height: 500px;
}
}

/*md屏幕以上*/
@media only screen and (min-width: 768px) {
/*行距*/
.lh-md-1 {
	line-height: 1;
}
.lh-md-2 {
	line-height: 2;
}
.lh-md-3 {
	line-height: 3;
}
.lh-md-4 {
	line-height: 4;
}
/*字体大小*/
.fs-md-12 {
	font-size: .857rem;
}
.fs-md-14 {
	font-size: 1rem;
}
.fs-md-16 {
	font-size: 1.142rem;
}
.fs-md-18 {
	font-size: 1.285rem;
}
.fs-md-20 {
	font-size: 1.428rem;
}
.fs-md-22 {
	font-size: 1.571rem;
}
.fs-md-24 {
	font-size: 1.714rem;
}
.fs-md-26 {
	font-size: 1.857rem;
}
.fs-md-28 {
	font-size: 2rem;
}
.fs-md-30 {
	font-size: 2.142rem;
}
.fs-md-32 {
	font-size: 2.286rem;
}
.fs-md-34 {
	font-size: 2.429rem;
}
.fs-md-36 {
	font-size: 2.571rem;
}
.fs-md-38 {
	font-size: 2.714rem;
}
.fs-md-40 {
	font-size: 2.857rem;
}
/*高度*/
.h-md-5px {
	height: 5px;
}
.h-md-10px {
	height: 10px;
}
.h-md-15px {
	height: 15px;
}
.h-md-20px {
	height: 20px;
}
.h-md-25px {
	height: 25px;
}
.h-md-30px {
	height: 30px;
}
.h-md-35px {
	height: 35px;
}
.h-md-40px {
	height: 40px;
}
.h-md-45px {
	height: 45px;
}
.h-md-50px {
	height: 50px;
}
.h-md-55px {
	height: 55px;
}
.h-md-60px {
	height: 60px;
}
.h-md-65px {
	height: 65px;
}
.h-md-70px {
	height: 70px;
}
.h-md-75px {
	height: 75px;
}
.h-md-80px {
	height: 80px;
}
.h-md-85px {
	height: 85px;
}
.h-md-90px {
	height: 90px;
}
.h-md-95px {
	height: 95px;
}
.h-md-100px {
	height: 100px;
}
.h-md-110px {
	height: 110px;
}
.h-md-120px {
	height: 120px;
}
.h-md-130px {
	height: 130px;
}
.h-md-140px {
	height: 140px;
}
.h-md-150px {
	height: 150px;
}
.h-md-160px {
	height: 160px;
}
.h-md-170px {
	height: 170px;
}
.h-md-180px {
	height: 180px;
}
.h-md-190px {
	height: 190px;
}
.h-md-200px {
	height: 200px;
}
.h-md-210px {
	height: 210px;
}
.h-md-220px {
	height: 220px;
}
.h-md-230px {
	height: 230px;
}
.h-md-240px {
	height: 240px;
}
.h-md-250px {
	height: 250px;
}
.h-md-260px {
	height: 260px;
}
.h-md-270px {
	height: 270px;
}
.h-md-280px {
	height: 280px;
}
.h-md-290px {
	height: 290px;
}
.h-md-300px {
	height: 300px;
}
.h-md-310px {
	height: 310px;
}
.h-md-320px {
	height: 320px;
}
.h-md-330px {
	height: 330px;
}
.h-md-340px {
	height: 340px;
}
.h-md-350px {
	height: 350px;
}
.h-md-360px {
	height: 360px;
}
.h-md-370px {
	height: 370px;
}
.h-md-380px {
	height: 380px;
}
.h-md-390px {
	height: 390px;
}
.h-md-400px {
	height: 400px;
}
.h-md-410px {
	height: 410px;
}
.h-md-420px {
	height: 420px;
}
.h-md-430px {
	height: 430px;
}
.h-md-440px {
	height: 440px;
}
.h-md-450px {
	height: 450px;
}
.h-md-460px {
	height: 410px;
}
.h-md-470px {
	height: 470px;
}
.h-md-480px {
	height: 480px;
}
.h-md-490px {
	height: 490px;
}
.h-md-500px {
	height: 500px;
}
}

/*lg屏幕以上*/
@media only screen and (min-width: 992px) {
/*行距*/
.lh-lg-1 {
	line-height: 1;
}
.lh-lg-2 {
	line-height: 2;
}
.lh-lg-3 {
	line-height: 3;
}
.lh-lg-4 {
	line-height: 4;
}
/*字体大小*/
.fs-lg-12 {
	font-size: .857rem;
}
.fs-lg-14 {
	font-size: 1rem;
}
.fs-lg-16 {
	font-size: 1.142rem;
}
.fs-lg-18 {
	font-size: 1.285rem;
}
.fs-lg-20 {
	font-size: 1.428rem;
}
.fs-lg-22 {
	font-size: 1.571rem;
}
.fs-lg-24 {
	font-size: 1.714rem;
}
.fs-lg-26 {
	font-size: 1.857rem;
}
.fs-lg-28 {
	font-size: 2rem;
}
.fs-lg-30 {
	font-size: 2.142rem;
}
.fs-lg-32 {
	font-size: 2.286rem;
}
.fs-lg-34 {
	font-size: 2.429rem;
}
.fs-lg-36 {
	font-size: 2.571rem;
}
.fs-lg-38 {
	font-size: 2.714rem;
}
.fs-lg-40 {
	font-size: 2.857rem;
}
/*高度*/
.h-lg-5px {
	height: 5px;
}
.h-lg-10px {
	height: 10px;
}
.h-lg-15px {
	height: 15px;
}
.h-lg-20px {
	height: 20px;
}
.h-lg-25px {
	height: 25px;
}
.h-lg-30px {
	height: 30px;
}
.h-lg-35px {
	height: 35px;
}
.h-lg-40px {
	height: 40px;
}
.h-lg-45px {
	height: 45px;
}
.h-lg-50px {
	height: 50px;
}
.h-lg-55px {
	height: 55px;
}
.h-lg-60px {
	height: 60px;
}
.h-lg-65px {
	height: 65px;
}
.h-lg-70px {
	height: 70px;
}
.h-lg-75px {
	height: 75px;
}
.h-lg-80px {
	height: 80px;
}
.h-lg-85px {
	height: 85px;
}
.h-lg-90px {
	height: 90px;
}
.h-lg-95px {
	height: 95px;
}
.h-lg-100px {
	height: 100px;
}
.h-lg-110px {
	height: 110px;
}
.h-lg-120px {
	height: 120px;
}
.h-lg-130px {
	height: 130px;
}
.h-lg-140px {
	height: 140px;
}
.h-lg-150px {
	height: 150px;
}
.h-lg-160px {
	height: 160px;
}
.h-lg-170px {
	height: 170px;
}
.h-lg-180px {
	height: 180px;
}
.h-lg-190px {
	height: 190px;
}
.h-lg-200px {
	height: 200px;
}
.h-lg-210px {
	height: 210px;
}
.h-lg-220px {
	height: 220px;
}
.h-lg-230px {
	height: 230px;
}
.h-lg-240px {
	height: 240px;
}
.h-lg-250px {
	height: 250px;
}
.h-lg-260px {
	height: 260px;
}
.h-lg-270px {
	height: 270px;
}
.h-lg-280px {
	height: 280px;
}
.h-lg-290px {
	height: 290px;
}
.h-lg-300px {
	height: 300px;
}
.h-lg-310px {
	height: 310px;
}
.h-lg-320px {
	height: 320px;
}
.h-lg-330px {
	height: 330px;
}
.h-lg-340px {
	height: 340px;
}
.h-lg-350px {
	height: 350px;
}
.h-lg-360px {
	height: 360px;
}
.h-lg-370px {
	height: 370px;
}
.h-lg-380px {
	height: 380px;
}
.h-lg-390px {
	height: 390px;
}
.h-lg-400px {
	height: 400px;
}
.h-lg-410px {
	height: 410px;
}
.h-lg-420px {
	height: 420px;
}
.h-lg-430px {
	height: 430px;
}
.h-lg-440px {
	height: 440px;
}
.h-lg-450px {
	height: 450px;
}
.h-lg-460px {
	height: 410px;
}
.h-lg-470px {
	height: 470px;
}
.h-lg-480px {
	height: 480px;
}
.h-lg-490px {
	height: 490px;
}
.h-lg-500px {
	height: 500px;
}
}
