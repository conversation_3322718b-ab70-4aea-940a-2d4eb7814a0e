## Prerequisites

- [ ] I have searched for similar issues in both open and closed tickets and cannot find a duplicate.
- [ ] The issue still exists against the latest `master` branch of bootstrap-fileinput.
- [ ] This is not an usage question. I confirm having read the plugin [documentation](http://plugins.krajee.com/file-input) and [demos](http://plugins.krajee.com/file-input/demo).
- [ ] This is not a general programming / coding question. (Those should be directed to the [webtips Q & A forum](http://webtips.krajee.com/questions)).
- [ ] I have attempted to find the simplest possible steps to reproduce the issue.
- [ ] I have included a failing test as a pull request (Optional).

## Steps to reproduce the issue

1.
2.
3.

## Expected behavior and actual behavior

When I follow those steps, I see...

I was expecting...

## Environment

Browsers

- [ ] Google Chrome
- [ ] Mozilla Firefox
- [ ] Internet Explorer
- [ ] Safari

Operating System

- [ ] Windows
- [ ] Mac OS X
- [ ] Linux
- [ ] Mobile

Libraries

- jQuery version:
- bootstrap-fileinput version:

## Isolating the problem

- [ ] This bug happens [on the plugin demos page](http://plugins.krajee.com/file-input/demo)
- [ ] The bug happens consistently across all tested browsers
- [ ] This bug happens when using bootstrap-fileinput without other plugins
- [ ] I can reproduce this bug in [a jsbin](https://jsbin.com/)
