<?php
/**
 * 完整解密后的 Kernel.php
 * 从Fatal Error中成功提取的完整源码
 * 解密时间: 2025-01-23
 */

namespace core\basic;

class Kernel
{
    private static $ldus6400a9d589628396658dc1cd8cebb0eaArray;

    public static function run()
    {
        self::qrdkhdcsq24742ed332d9e099d3d85fcd2e56323c();
        $ldus6400a9d589628396658dc1cd8cebb0ea_info = self::cqupdusbtfrb5aa1c55c76f8b8cbf0dcf3124acee32();
        $ldus6400a9d589628396658dc1cd8cebb0ea_info = self::hjqwqxtke773dfbd86b59a3f0e603fd93e5877ba($ldus6400a9d589628396658dc1cd8cebb0ea_info);
        $ldus6400a9d589628396658dc1cd8cebb0ea_info = self::hjqprhuq58c2568ee2f5c980755d9cbb21004b20($ldus6400a9d589628396658dc1cd8cebb0ea_info);
        $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e = self::cquxccqmmpdus04e141f498f476494adf0703a2782dfa($ldus6400a9d589628396658dc1cd8cebb0ea_info);
        $crtujrqqqj_ldus1d433070c61b28e383e475867f59d167 = self::jqcxllpdusfa48f7ab12d147dc39abeb2782440125($dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e);
        self::qrdkhrvv4d17d228c938f35db8e6d2839363e3b6();
        self::qxcqtmqdfa6a7ddc9a8eec15e0783fd10ef1dbc();
        self::qrdkhrtujrqqqj130920de147a66d9c199c09df9411263($crtujrqqqj_ldus1d433070c61b28e383e475867f59d167);
    }

    private static function cqupdusbtfrb5aa1c55c76f8b8cbf0dcf3124acee32()
    {
        if (isset($_SERVER['PATH_INFO']) && !mb_check_encoding($_SERVER['PATH_INFO'], 'UTF-8')) {
            $_SERVER['PATH_INFO'] = mb_convert_encoding($_SERVER['PATH_INFO'], 'utf-8', 'GBK');
        }
        if (isset($_SERVER['REQUEST_URI']) && !mb_check_encoding($_SERVER['REQUEST_URI'], 'UTF-8')) {
            $_SERVER['REQUEST_URI'] = mb_convert_encoding($_SERVER['REQUEST_URI'], 'utf-8', 'GBK');
        }
        if (isset($_SERVER['ORIG_PATH_INFO']) && !mb_check_encoding($_SERVER['ORIG_PATH_INFO'], 'UTF-8')) {
            $_SERVER['ORIG_PATH_INFO'] = mb_convert_encoding($_SERVER['ORIG_PATH_INFO'], 'utf-8', 'GBK');
        }
        
        $ldus6400a9d589628396658dc1cd8cebb0ea_info = '';
        if (isset($_SERVER['PATH_INFO']) && $_SERVER['PATH_INFO']) {
            $ldus6400a9d589628396658dc1cd8cebb0ea_info = $_SERVER['PATH_INFO'];
        } elseif (isset($_SERVER["REDIRECT_URL"]) && $_SERVER["REDIRECT_URL"]) {
            $ldus6400a9d589628396658dc1cd8cebb0ea_info = str_replace('/' . basename($_SERVER['SCRIPT_NAME']), '', $_SERVER['REDIRECT_URL']);
            $ldus6400a9d589628396658dc1cd8cebb0ea_info = str_replace(SITE_DIR, '', $ldus6400a9d589628396658dc1cd8cebb0ea_info);
            $_SERVER['PATH_INFO'] = $ldus6400a9d589628396658dc1cd8cebb0ea_info;
        }
        
        if (!$ldus6400a9d589628396658dc1cd8cebb0ea_info) {
            if (isset($_GET['p']) && $_GET['p']) {
                $ldus6400a9d589628396658dc1cd8cebb0ea_info = $_GET['p'];
            } elseif (isset($_GET['s']) && $_GET['s']) {
                $ldus6400a9d589628396658dc1cd8cebb0ea_info = $_GET['s'];
            }
        }
        
        if ($ldus6400a9d589628396658dc1cd8cebb0ea_info) {
            $lduuqjt9d5d7c2e14605fcf3ccd159b872f42d4 = '{^\/?([\x{4e00}-\x{9fa5}\w\-\/\.' . Config::get('url_allow_char') . ']+?)?$}u';
            if (preg_match($lduuqjt9d5d7c2e14605fcf3ccd159b872f42d4, $ldus6400a9d589628396658dc1cd8cebb0ea_info)) {
                $ldus6400a9d589628396658dc1cd8cebb0ea_info = preg_replace($lduuqjt9d5d7c2e14605fcf3ccd159b872f42d4, '$1', $ldus6400a9d589628396658dc1cd8cebb0ea_info);
            } else {
                $kxq6e9ebe287cf81e2e2653457b2077f522 = true;
            }
        }
        
        if (isset($kxq6e9ebe287cf81e2e2653457b2077f522) && $kxq6e9ebe287cf81e2e2653457b2077f522) {
            http_response_code(404);
            $kqfqtk265ba6c9798f627cc6788e854d3a5dee = ROOT_PATH . '/defend.html';
            if (file_exists($kqfqtk265ba6c9798f627cc6788e854d3a5dee)) {
                require $kqfqtk265ba6c9798f627cc6788e854d3a5dee;
                exit();
            } else {
                error('您访问路径含有非法字符，防注入系统提醒您请勿尝试非法操作！');
            }
        }
        
        define('P', $ldus6400a9d589628396658dc1cd8cebb0ea_info);
        return $ldus6400a9d589628396658dc1cd8cebb0ea_info;
    }

    private static function hjqwqxtke773dfbd86b59a3f0e603fd93e5877ba($ldus6400a9d589628396658dc1cd8cebb0eaInfo)
    {
        $ldus6400a9d589628396658dc1cd8cebb0ea = '';
        if (!!$krvdxtmce84f9b892e52ae7c9a56f909181c76d = Config::get('app_domain_bind')) {
            $mqjoqj_tdvqdc94ddf4d2a22202a181dd703d447dc1 = get_http_host();
            if (isset($krvdxtmce84f9b892e52ae7c9a56f909181c76d[$mqjoqj_tdvqdc94ddf4d2a22202a181dd703d447dc1])) {
                $ldus6400a9d589628396658dc1cd8cebb0ea = $krvdxtmce84f9b892e52ae7c9a56f909181c76d[$mqjoqj_tdvqdc94ddf4d2a22202a181dd703d447dc1];
            }
        }
        
        if (defined('URL_BIND')) {
            if ($ldus6400a9d589628396658dc1cd8cebb0ea && URL_BIND != $ldus6400a9d589628396658dc1cd8cebb0ea) {
                error('系统配置的模块域名绑定与入口文件绑定冲突，请核对！');
            } else {
                $ldus6400a9d589628396658dc1cd8cebb0ea = URL_BIND;
            }
        }
        
        return $ldus6400a9d589628396658dc1cd8cebb0ea ? trim_slash($ldus6400a9d589628396658dc1cd8cebb0ea) . '/' . $ldus6400a9d589628396658dc1cd8cebb0eaInfo : $ldus6400a9d589628396658dc1cd8cebb0eaInfo;
    }

    private static function hjqprhuq58c2568ee2f5c980755d9cbb21004b20($ldus6400a9d589628396658dc1cd8cebb0eaInfo)
    {
        if (!!$jrhuqa3f54e0c115fa9af0e87b91cde482b07 = Config::get('url_route')) {
            if (!$ldus6400a9d589628396658dc1cd8cebb0eaInfo && isset($jrhuqa3f54e0c115fa9af0e87b91cde482b07['/'])) {
                return $jrhuqa3f54e0c115fa9af0e87b91cde482b07['/'];
            }
            
            foreach ($jrhuqa3f54e0c115fa9af0e87b91cde482b07 as $kqgedd822bb0307a4e73daae9f73d6eb9a1 => $odqhq5bcb6a0b0997c73630ba934b89bddd7e) {
                $kqgedd822bb0307a4e73daae9f73d6eb9a1 = trim_slash($kqgedd822bb0307a4e73daae9f73d6eb9a1);
                $jqc3e2a6e5199b9fbe934ba1c2f0e9fc1b1 = "{" . $kqgedd822bb0307a4e73daae9f73d6eb9a1 . "}i";
                if (preg_match($jqc3e2a6e5199b9fbe934ba1c2f0e9fc1b1, $ldus6400a9d589628396658dc1cd8cebb0eaInfo)) {
                    $odqhq5bcb6a0b0997c73630ba934b89bddd7e = trim_slash($odqhq5bcb6a0b0997c73630ba934b89bddd7e);
                    $ldus6400a9d589628396658dc1cd8cebb0eaInfo = preg_replace($jqc3e2a6e5199b9fbe934ba1c2f0e9fc1b1, $odqhq5bcb6a0b0997c73630ba934b89bddd7e, $ldus6400a9d589628396658dc1cd8cebb0eaInfo);
                    break;
                }
            }
        }
        
        return $ldus6400a9d589628396658dc1cd8cebb0eaInfo;
    }

    private static function cquxccqmmpdus04e141f498f476494adf0703a2782dfa($ldus6400a9d589628396658dc1cd8cebb0eaInfo)
    {
        $dllm022243b7ba5d8cd32e3b72d1f3b7ef9e = Config::get('public_app', true);
        
        if ($ldus6400a9d589628396658dc1cd8cebb0eaInfo) {
            $ldus6400a9d589628396658dc1cd8cebb0ea_info = trim_slash($ldus6400a9d589628396658dc1cd8cebb0eaInfo);
            $ldus6400a9d589628396658dc1cd8cebb0ea_array = explode('/', $ldus6400a9d589628396658dc1cd8cebb0ea_info);
            self::$ldus6400a9d589628396658dc1cd8cebb0eaArray = $ldus6400a9d589628396658dc1cd8cebb0ea_array;
            $ldus6400a9d589628396658dc1cd8cebb0ea_count = count($ldus6400a9d589628396658dc1cd8cebb0ea_array);
            
            if ($ldus6400a9d589628396658dc1cd8cebb0ea_count >= 3) {
                $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['m'] = $ldus6400a9d589628396658dc1cd8cebb0ea_array[0];
                $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['c'] = $ldus6400a9d589628396658dc1cd8cebb0ea_array[1];
                $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['f'] = $ldus6400a9d589628396658dc1cd8cebb0ea_array[2];
            } elseif ($ldus6400a9d589628396658dc1cd8cebb0ea_count == 2) {
                $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['m'] = $ldus6400a9d589628396658dc1cd8cebb0ea_array[0];
                $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['c'] = $ldus6400a9d589628396658dc1cd8cebb0ea_array[1];
            } elseif ($ldus6400a9d589628396658dc1cd8cebb0ea_count == 1) {
                $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['m'] = $ldus6400a9d589628396658dc1cd8cebb0ea_array[0];
            }
        }
        
        if (!isset($dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['m'])) {
            $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['m'] = $dllm022243b7ba5d8cd32e3b72d1f3b7ef9e[0];
        }
        if (!isset($dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['c'])) {
            $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['c'] = 'Index';
        }
        if (!isset($dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['f'])) {
            $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['f'] = 'index';
        }
        
        if (!in_array(strtolower($dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['m']), $dllm022243b7ba5d8cd32e3b72d1f3b7ef9e)) {
            error('您访问的模块' . $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e['m'] . '未开放,请核对后重试！');
        }
        
        return $dccqmm_ldus0d756b36951f7095aa2eaf1c8f311e9e;
    }

    private static function jqcxllpdusfa48f7ab12d147dc39abeb2782440125($dccqmm2c7402b9a53f4a15bc3d6172525d2fefPath)
    {
        define('M', strtolower($dccqmm2c7402b9a53f4a15bc3d6172525d2fefPath['m']));
        define('APP_MODEL_PATH', APP_PATH . '/' . M . '/model');
        define('APP_CONTROLLER_PATH', APP_PATH . '/' . M . '/controller');

        if (($ulq_kxj4a36a374d6bd71648c892517838205d8 = Config::get('tpl_dir')) && array_key_exists(M, $ulq_kxj4a36a374d6bd71648c892517838205d8)) {
            if (strpos($ulq_kxj4a36a374d6bd71648c892517838205d8[M], ROOT_PATH) === false) {
                define('APP_VIEW_PATH', ROOT_PATH . $ulq_kxj4a36a374d6bd71648c892517838205d8[M]);
            } else {
                define('APP_VIEW_PATH', $ulq_kxj4a36a374d6bd71648c892517838205d8[M]);
            }
        } else {
            define('APP_VIEW_PATH', APP_PATH . '/' . M . '/view');
        }

        if (strpos($dccqmm2c7402b9a53f4a15bc3d6172525d2fefPath['c'], '.') > 0) {
            $crtujrqqqj_ldus1d433070c61b28e383e475867f59d167 = str_replace('.', '/', $dccqmm2c7402b9a53f4a15bc3d6172525d2fefPath['c']);
            $controller = ucfirst(basename($crtujrqqqj_ldus1d433070c61b28e383e475867f59d167));
            $crtujrqqqj_ldus1d433070c61b28e383e475867f59d167 = dirname($crtujrqqqj_ldus1d433070c61b28e383e475867f59d167) . '/' . $controller;
        } else {
            $controller = ucfirst($dccqmm2c7402b9a53f4a15bc3d6172525d2fefPath['c']);
            $crtujrqqqj_ldus1d433070c61b28e383e475867f59d167 = $controller;
        }

        $cqdmm_fxqq_ldusc71c071dbe79557532bd857f65c24a07 = APP_CONTROLLER_PATH . '/' . $crtujrqqqj_ldus1d433070c61b28e383e475867f59d167 . 'Controller.php';
        $mdod_crtujrqqqj6e30dacbdbe585801988276405beb952 = array('List', 'Content', 'About');
        $dknhmufe12b19aff36e01df38640c34e337da5 = 0;

        if (M == 'home' && (!file_exists($cqdmm_fxqq_ldusc71c071dbe79557532bd857f65c24a07) || in_array($controller, $mdod_crtujrqqqj6e30dacbdbe585801988276405beb952))) {
            $controller = 'Index';
            $crtujrqqqj_ldus1d433070c61b28e383e475867f59d167 = 'Index';
            define('F', $dccqmm2c7402b9a53f4a15bc3d6172525d2fefPath['c']);
            $dknhmufe12b19aff36e01df38640c34e337da5 = -1;
        } elseif (M == 'home' && in_array($controller, Config::get('second_rvar'))) {
            define('F', 'index');
            define('RVAR', $dccqmm2c7402b9a53f4a15bc3d6172525d2fefPath['f']);
        } else {
            define('F', $dccqmm2c7402b9a53f4a15bc3d6172525d2fefPath['f']);
        }

        define('C', $controller);

        if (isset($_SERVER["REQUEST_URI"])) {
            define('URL', $_SERVER["REQUEST_URI"]);
        } else {
            define('URL', $_SERVER["ORIG_PATH_INFO"] . '?' . $_SERVER["QUERY_STRING"]);
        }

        $ldus6400a9d589628396658dc1cd8cebb0ea_count = count(self::$ldus6400a9d589628396658dc1cd8cebb0eaArray);
        for ($i = 3 + $dknhmufe12b19aff36e01df38640c34e337da5; $i < $ldus6400a9d589628396658dc1cd8cebb0ea_count; $i = $i + 2) {
            if (isset(self::$ldus6400a9d589628396658dc1cd8cebb0eaArray[$i + 1])) {
                $_GET[self::$ldus6400a9d589628396658dc1cd8cebb0eaArray[$i]] = self::$ldus6400a9d589628396658dc1cd8cebb0eaArray[$i + 1];
            } else {
                $_GET[self::$ldus6400a9d589628396658dc1cd8cebb0eaArray[$i]] = null;
            }
        }

        return $crtujrqqqj_ldus1d433070c61b28e383e475867f59d167;
    }

    private static function qrdkhrvv4d17d228c938f35db8e6d2839363e3b6()
    {
        Config::get('debug') ? Check::checkAppFile() : '';

        if (M == 'api') {
            if (!!$mxkccd405b886a0702897ac8b501376c733 = request('sid')) {
                session_id($mxkccd405b886a0702897ac8b501376c733);
                session_start();
            }
            header("Access-Control-Allow-Origin: *");
        } else {
            Check::checkBs();
            Check::checkOs();
        }

        if (file_exists(APP_PATH . '/common/function.php')) {
            require APP_PATH . '/common/function.php';
        }

        $dll_crtfxc9f92147c311577f39434e32a04d6fdb5 = APP_PATH . '/' . M . '/config/config.php';
        if (file_exists($dll_crtfxc9f92147c311577f39434e32a04d6fdb5)) {
            Config::assign($dll_crtfxc9f92147c311577f39434e32a04d6fdb5);
        }

        $dll_fhtcuxrt7dbc4517031428415af7cff4aae10548 = APP_PATH . '/' . M . '/function/function.php';
        if (file_exists($dll_fhtcuxrt7dbc4517031428415af7cff4aae10548)) {
            require $dll_fhtcuxrt7dbc4517031428415af7cff4aae10548;
        }

        if (file_exists(APP_PATH . '/common/' . ucfirst(M) . 'Controller.php')) {
            $crvv_cqdmm_tdvq232cbcc56146fbb051019745e44a1305 = '\\app\\common\\' . ucfirst(M) . 'Controller';
            $crvv_cqdmm692d41d742a3d78fc2399d0c1e299c23 = new $crvv_cqdmm_tdvq232cbcc56146fbb051019745e44a1305();
        }
    }

    private static function qrdkhrtujrqqqj130920de147a66d9c199c09df9411263($controllerPath)
    {
        $cqdmm_fxqqe364a9cd3e650e3deda2d215f9e3b013 = $controllerPath . 'Controller.php';
        $cqdmm_fxqq_ldusc71c071dbe79557532bd857f65c24a07 = APP_CONTROLLER_PATH . '/' . $cqdmm_fxqqe364a9cd3e650e3deda2d215f9e3b013;
        $cqdmm_tdvqa46b8ce39060fce7ea6b63b39ebaacfd = '\\app\\' . M . '\\controller\\' . str_replace('/', '\\', $controllerPath) . 'Controller';
        $fhtcuxrt_tdvq2145156bf28451084d421feb1e5f5129 = F;

        if (!file_exists($cqdmm_fxqq_ldusc71c071dbe79557532bd857f65c24a07)) {
            http_response_code(404);
            $fxqq_e793d7cc744684b39f1f92efd0abaec9404 = ROOT_PATH . '/404.html';
            if (file_exists($fxqq_e793d7cc744684b39f1f92efd0abaec9404)) {
                require $fxqq_e793d7cc744684b39f1f92efd0abaec9404;
                exit();
            } else {
                error('对不起，您访问的页面类文件不存在，请核对后再试！');
            }
        }

        if (!class_exists($cqdmm_tdvqa46b8ce39060fce7ea6b63b39ebaacfd)) {
            error('类文件中不存在访问的类名！' . $cqdmm_tdvqa46b8ce39060fce7ea6b63b39ebaacfd);
        }

        $controller = new $cqdmm_tdvqa46b8ce39060fce7ea6b63b39ebaacfd();

        if (method_exists($cqdmm_tdvqa46b8ce39060fce7ea6b63b39ebaacfd, $fhtcuxrt_tdvq2145156bf28451084d421feb1e5f5129)) {
            if (strtolower($cqdmm_tdvqa46b8ce39060fce7ea6b63b39ebaacfd) != strtolower($fhtcuxrt_tdvq2145156bf28451084d421feb1e5f5129)) {
                $jquhjt117df2c1c294fbdeb95b6f7b211a4e57 = $controller->$fhtcuxrt_tdvq2145156bf28451084d421feb1e5f5129();
            } else {
                $jquhjt117df2c1c294fbdeb95b6f7b211a4e57 = $controller;
            }
        } else {
            if (method_exists($cqdmm_tdvqa46b8ce39060fce7ea6b63b39ebaacfd, '_empty')) {
                $jquhjt117df2c1c294fbdeb95b6f7b211a4e57 = $controller->_empty();
            } else {
                error('不存在您调用的类或方法' . $fhtcuxrt_tdvq2145156bf28451084d421feb1e5f5129 . '，可能正在开发中，请耐心等待！');
            }
        }

        if ($jquhjt117df2c1c294fbdeb95b6f7b211a4e57 !== null) {
            print_r($jquhjt117df2c1c294fbdeb95b6f7b211a4e57);
            exit();
        }
    }

    private static function qrdkhdcsq24742ed332d9e099d3d85fcd2e56323c()
    {
        if (!Config::get('tpl_html_cache') || URL_BIND == 'api' || get('nocache', 'int') == 1) {
            return;
        }

        $qc_cdcsq8274e88d348ec893147a02a23db7c7aa = RUN_PATH . '/config/' . md5('area') . '.php';
        if (!file_exists($qc_cdcsq8274e88d348ec893147a02a23db7c7aa)) {
            return;
        } else {
            Config::assign($qc_cdcsq8274e88d348ec893147a02a23db7c7aa);
        }

        $qcm68fc81129ac16f62b36cd2eb14d78ad7 = Config::get('lgs');
        if (count($qcm68fc81129ac16f62b36cd2eb14d78ad7) > 1) {
            $krvdxt455fdf25fe62beed005b6aae0acb6f7e = get_http_host();
            foreach ($qcm68fc81129ac16f62b36cd2eb14d78ad7 as $odqhq5bcb6a0b0997c73630ba934b89bddd7e) {
                if ($odqhq5bcb6a0b0997c73630ba934b89bddd7e['domain'] == $krvdxt455fdf25fe62beed005b6aae0acb6f7e) {
                    cookie('lg', $odqhq5bcb6a0b0997c73630ba934b89bddd7e['acode']);
                }
            }
        }

        if (!isset($_COOKIE['lg'])) {
            $kqfdhquf892edf4dfc2cb7e8a4b6f7f5e9c1064 = current(Config::get('lgs'));
            cookie('lg', $kqfdhquf892edf4dfc2cb7e8a4b6f7f5e9c1064['acode']);
        }

        $crtfxc_cdcsqa31703345c95eee7a206e72d53cf7cd9 = RUN_PATH . '/config/' . md5('config') . '.php';
        if (!Config::assign($crtfxc_cdcsqa31703345c95eee7a206e72d53cf7cd9)) {
            return;
        }

        if (Config::get('open_wap') && (is_mobile() || Config::get('wap_domain') == get_http_host())) {
            $zdl3a943642e7430744b3424e094affaf39 = 'wap';
        } else {
            $zdl3a943642e7430744b3424e094affaf39 = '';
        }

        $cdcsq_fxqq97f0dc96ec543e9b39234611130e909a = RUN_PATH . '/cache/' . md5(get_http_url() . $_SERVER["REQUEST_URI"] . cookie('lg') . $zdl3a943642e7430744b3424e094affaf39) . '.html';

        if (file_exists($cdcsq_fxqq97f0dc96ec543e9b39234611130e909a) && time() - filemtime($cdcsq_fxqq97f0dc96ec543e9b39234611130e909a) < Config::get('tpl_html_cache_time')) {
            ob_start();
            include $cdcsq_fxqq97f0dc96ec543e9b39234611130e909a;
            $crtuqtu4b7edb180081d0b57b4509804630aa94 = ob_get_contents();
            ob_end_clean();

            if (Config::get('gzip') && !headers_sent() && extension_loaded("zlib") && strstr($_SERVER["HTTP_ACCEPT_ENCODING"], "gzip")) {
                $crtuqtu4b7edb180081d0b57b4509804630aa94 = gzencode($crtuqtu4b7edb180081d0b57b4509804630aa94, 6);
                header("Content-Encoding: gzip");
                header("Vary: Accept-Encoding");
                header("Content-Length: " . strlen($crtuqtu4b7edb180081d0b57b4509804630aa94));
            }

            echo $crtuqtu4b7edb180081d0b57b4509804630aa94;
            exit();
        }
    }

    private static function qxcqtmqdfa6a7ddc9a8eec15e0783fd10ef1dbc()
    {
        $mxl82a81b05064b014081242c9ad50497a3 = isset($_SERVER['LOCAL_ADDR']) ? $_SERVER['LOCAL_ADDR'] : $_SERVER['SERVER_ADDR'];
        if ($mxl82a81b05064b014081242c9ad50497a3 == '::1') {
            $mxl82a81b05064b014081242c9ad50497a3 = '127.0.0.1';
        }

        $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866df = 0;

        if (!!$qxcqtmq0399ffb2cc9f62345a01bdc8d6c866dfcode = Config::get('licensecode')) {
            $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866dfcode = explode('/', base64_decode(substr($qxcqtmq0399ffb2cc9f62345a01bdc8d6c866dfcode, 0, -1)));
            $mta38dd047d78092a30c40a0a141a09bfc = explode(',', $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866dfcode[0]);
            $mta38dd047d78092a30c40a0a141a09bfc_user = $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866dfcode[1];
        } else {
            $mta38dd047d78092a30c40a0a141a09bfc = Config::get('sn', true);
            $mta38dd047d78092a30c40a0a141a09bfc_user = Config::get('sn_user');
        }

        if (!!$mta38dd047d78092a30c40a0a141a09bfc) {
            $kqgedd822bb0307a4e73daae9f73d6eb9a1_user = strtoupper(substr(md5(substr(sha1($mta38dd047d78092a30c40a0a141a09bfc_user), 0, 20)), 10, 10));
            $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866df = $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866df ?: (in_array($kqgedd822bb0307a4e73daae9f73d6eb9a1_user, $mta38dd047d78092a30c40a0a141a09bfc) ? 3 : 0);

            $kqgedd822bb0307a4e73daae9f73d6eb9a1_host = strtoupper(substr(md5(substr(sha1($mxl82a81b05064b014081242c9ad50497a3), 0, 15)), 10, 10));
            $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866df = $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866df ?: (in_array($kqgedd822bb0307a4e73daae9f73d6eb9a1_host, $mta38dd047d78092a30c40a0a141a09bfc) ? 2 : 0);

            $srmu786735bfb6feadf6b09ad46b1c61340c = isset($_SERVER['HTTP_X_FORWARDED_HOST']) ? $_SERVER['HTTP_X_FORWARDED_HOST'] : (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '');
            $kqgedd822bb0307a4e73daae9f73d6eb9a1_domain = strtoupper(substr(md5(substr(sha1($srmu786735bfb6feadf6b09ad46b1c61340c), 0, 10)), 10, 10));
            $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866df = $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866df ?: (in_array($kqgedd822bb0307a4e73daae9f73d6eb9a1_domain, $mta38dd047d78092a30c40a0a141a09bfc) ? 1 : 0);
        }

        define('LICENSE', $qxcqtmq0399ffb2cc9f62345a01bdc8d6c866df);

        if (!LICENSE && (filter_var(get_http_host(), FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) || get_http_host() == 'localhost')) {
            return;
        }

        if (!$qxcqtmq0399ffb2cc9f62345a01bdc8d6c866df && (defined('URL_BIND') && URL_BIND != 'admin')) {
            $fxqq_e793d7cc744684b39f1f92efd0abaec9sn = ROOT_PATH . '/sn.html';
            if (file_exists($fxqq_e793d7cc744684b39f1f92efd0abaec9sn)) {
                require $fxqq_e793d7cc744684b39f1f92efd0abaec9sn;
                exit();
            } else {
                error('未匹配到本域名(' . $srmu786735bfb6feadf6b09ad46b1c61340c . ')有效授权码，请到<a href="http://www.pbootcms.com" target="_blank">PbootCMS官网</a>免费获取，并登录系统后台填写到"全局配置>>配置参数"中。');
            }
        }
    }
}
