{include file='common/head.html'}

<div class="layui-body">
	{if([$list])}
	<div class="layui-tab layui-tab-brief" lay-filter="tab">
	  <ul class="layui-tab-title">
	    <li class="layui-this" lay-id="t1">会员字段列表</li>
	    <li lay-id="t2">会员字段新增</li>
	  </ul>
	  <div class="layui-tab-content">
	  	   <div class="layui-tab-item layui-show">
	  	   		<table class="layui-table">
	            	<thead>
	                    <tr>
	                    	<th width=40>序号</th>
	                    	<th>描述</th>
	                    	<th>字段</th>
	                    	<th>长度</th>
	                    	<th>是否必填</th>
	                    	<th>是否启用</th>
	                    	<th>排序</th>
	                        <th>操作</th>
	                    </tr>
	                </thead>
	                <tbody>
	                    {foreach $fields(key,value)}
	                    <tr>
	                    	<td>[num]</td>
	                    	<td>[value->description]</td>
	                    	<td>[value->name]</td>
	                    	<td>[value->length]</td>
	                    	<td>
		                        {if($value->required)}
		                        <a href="{url./admin/'.C.'/mod/id/'.$value->id.'/field/required/value/0}"><i class='fa fa-toggle-on' title="点击关闭必填"></i></a>
		                        {else}
		                        <a href="{url./admin/'.C.'/mod/id/'.$value->id.'/field/required/value/1}"><i class='fa fa-toggle-off' title="点击开启必填"></i></a>
		                        {/if}
	                    	</td>
	                    	<td>
		                        {if($value->status)}
		                        <a href="{url./admin/'.C.'/mod/id/'.$value->id.'/field/status/value/0}"><i class='fa fa-toggle-on' title="点击禁用"></i></a>
		                        {else}
		                        <a href="{url./admin/'.C.'/mod/id/'.$value->id.'/field/status/value/1}"><i class='fa fa-toggle-off' title="点击启用"></i></a>
		                        {/if}
	                    	</td>
	                    	<td>[value->sorting]</td>
	                        <td>
	                            {fun=get_btn_del($value->id)}
	                            {fun=get_btn_mod($value->id)}
	                        </td>
	                    </tr>
	                    {/foreach}
	                </tbody>
	            </table>
	            <div class="page">{$pagebar}</div>
	  	   </div>
	  	   
	  	    <div class="layui-tab-item">
	   			<form action="{url./admin/MemberField/add}" method="post" class="layui-form">
					<input type="hidden" name="formcheck" value="{$formcheck}" > 
	                <div class="layui-form-item">
	                     <label class="layui-form-label">字段描述</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="description" required lay-verify="required"  placeholder="请输入字段描述，如：电话" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">字段名称</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="name" required maxlength="20" lay-verify="required"  placeholder="请输入字段名称，必须以字母开头，如：telephone" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">字段长度</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="length" value="20" required lay-verify="required" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">是否必填</label>
	                     <div class="layui-input-block">
	                     	<input type="radio" name="required" value="1" title="必填" checked>
							<input type="radio" name="required" value="0" title="非必填"  checked>
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">是否启用</label>
	                     <div class="layui-input-block">
	                     	<input type="radio" name="status" value="1" title="启用" checked>
							<input type="radio" name="status" value="0" title="禁用">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">字段排序</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="sorting" value="255" required lay-verify="required" class="layui-input">
	                     </div>
	                </div>
	   			
	   				<div class="layui-form-item">
						 <div class="layui-input-block">
						    <button class="layui-btn" lay-submit>立即提交</button>
						    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
						 </div>
					</div>
	   			</form>
	   	 	</div>
	   </div>
	 </div>
	{/if} 
	
	{if([$mod])}
	<div class="layui-tab layui-tab-brief" lay-filter="tab">
	  <ul class="layui-tab-title">
	    <li class="layui-this">会员字段修改</li>
	  </ul>
	  <div class="layui-tab-content">
	  	<div class="layui-tab-item layui-show">
	  		<form action="{url./admin/MemberField/mod/id/'.[$get.id].'}{$backurl}" method="post" class="layui-form">
	  			<input type="hidden" name="formcheck" value="{$formcheck}" > 
                <div class="layui-form-item">
                     <label class="layui-form-label">字段描述</label>
                     <div class="layui-input-block">
                     	<input type="text" name="description"  value="{$field->description}" placeholder="请输入字段描述，如：联系人" class="layui-input">
                     </div>
                </div>
                
                <div class="layui-form-item">
                     <label class="layui-form-label">字段名称</label>
                     <div class="layui-input-block">
                     	<input type="text" name="name" readonly value="{$field->name}" class="layui-input readonly">
                     </div>
                </div>
                
                <div class="layui-form-item">
                     <label class="layui-form-label">字段长度</label>
                     <div class="layui-input-block">
                     	<input type="text" name="length" readonly value="{$field->length}" class="layui-input readonly">
                     </div>
                </div>
                
                <div class="layui-form-item">
                     <label class="layui-form-label">是否必填</label>
                     <div class="layui-input-block">
                     	<input type="radio" name="required" value="1" title="必填" {if([$field->required]==1)}checked{/if}>
						<input type="radio" name="required" value="0" title="非必填" {if([$field->required]==0)}checked{/if}>
                     </div>
                </div>
                
                <div class="layui-form-item">
                     <label class="layui-form-label">是否启用</label>
                     <div class="layui-input-block">
                     	<input type="radio" name="status" value="1" title="启用" {if([$field->status]==1)}checked{/if}>
						<input type="radio" name="status" value="0" title="禁用" {if([$field->status]==0)}checked{/if}>
                     </div>
                </div>
                
                <div class="layui-form-item">
                     <label class="layui-form-label">字段排序</label>
                     <div class="layui-input-block">
                     	<input type="text" name="sorting" value="{$field->sorting}" required lay-verify="required" class="layui-input">
                     </div>
                </div>
   			
   				<div class="layui-form-item">
					 <div class="layui-input-block">
					    <button class="layui-btn" lay-submit>立即提交</button>
					    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
					    {fun=get_btn_back()}
					 </div>
				</div>
	  		</form>
	  	</div>
	  </div>
	 </div>
	{/if} 
</div>
{include file='common/foot.html'}
