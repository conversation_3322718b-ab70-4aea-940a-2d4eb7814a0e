{"version": 3, "sources": ["swiper.js"], "names": ["global", "factory", "exports", "module", "define", "amd", "Swiper", "this", "doc", "document", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "location", "hash", "win", "window", "navigator", "userAgent", "history", "CustomEvent", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "Dom7", "arr", "i", "length", "$", "selector", "context", "els", "tempParent", "html", "trim", "indexOf", "toCreate", "innerHTML", "push", "match", "split", "nodeType", "unique", "uniqueArray", "fn", "prototype", "Class", "Methods", "addClass", "className", "classes", "j", "classList", "add", "removeClass", "remove", "hasClass", "contains", "toggleClass", "toggle", "attr", "attrs", "value", "arguments$1", "arguments", "getAttribute", "attrName", "removeAttr", "removeAttribute", "data", "key", "el", "dom7ElementDataStorage", "dataKey", "transform", "elStyle", "webkitTransform", "transition", "duration", "webkitTransitionDuration", "transitionDuration", "on", "assign", "args", "len", "eventType", "targetSelector", "listener", "capture", "handleLiveEvent", "e", "target", "eventData", "dom7EventData", "unshift", "is", "apply", "parents", "k", "handleEvent", "undefined", "events", "event$1", "dom7LiveListeners", "proxyListener", "event", "dom7Listeners", "off", "handlers", "handler", "splice", "trigger", "evt", "detail", "bubbles", "cancelable", "filter", "dataIndex", "dispatchEvent", "transitionEnd", "callback", "dom", "fireCallBack", "call", "outerWidth", "<PERSON><PERSON><PERSON><PERSON>", "styles", "offsetWidth", "parseFloat", "outerHeight", "offsetHeight", "offset", "box", "getBoundingClientRect", "clientTop", "clientLeft", "scrollTop", "scrollY", "scrollLeft", "scrollX", "top", "left", "css", "props", "prop", "each", "text", "textContent", "compareWith", "matches", "webkitMatchesSelector", "msMatchesSelector", "index", "child", "previousSibling", "eq", "returnIndex", "append", "<PERSON><PERSON><PERSON><PERSON>", "tempDiv", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "prepend", "this$1", "insertBefore", "next", "nextElement<PERSON><PERSON>ling", "nextAll", "nextEls", "prev", "previousElementSibling", "prevAll", "prevEls", "parent", "parentNode", "closest", "find", "foundElements", "found", "<PERSON><PERSON><PERSON><PERSON>", "toAdd", "Object", "keys", "for<PERSON>ach", "methodName", "testDiv", "Utils", "deleteProps", "obj", "object", "nextTick", "delay", "now", "getTranslate", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "WebKitCSSMatrix", "map", "a", "replace", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "toString", "m41", "m42", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>", "url", "params", "param", "query", "urlToParse", "href", "paramsPart", "decodeURIComponent", "isObject", "o", "constructor", "extend", "len$1", "to", "nextSource", "keysArray", "nextIndex", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "enumerable", "Support", "touch", "Modernizr", "DocumentTouch", "pointerEvents", "pointer<PERSON><PERSON>bled", "PointerEvent", "prefixedPointerEvents", "msPointer<PERSON><PERSON><PERSON>", "transforms3d", "csstransforms3d", "flexbox", "observer", "passiveListener", "supportsPassive", "opts", "defineProperty", "get", "gestures", "SwiperClass", "self", "eventsListeners", "eventName", "staticAccessors", "components", "configurable", "priority", "method", "once", "once<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "emit", "Array", "isArray", "slice", "useModulesParams", "instanceParams", "instance", "modules", "moduleName", "useModules", "modulesParams", "moduleParams", "modulePropName", "moduleProp", "bind", "moduleEventName", "create", "set", "use", "installModule", "name", "proto", "static", "install", "m", "concat", "defineProperties", "update", "updateSize", "width", "height", "swiper", "$el", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "size", "updateSlides", "$wrapperEl", "swiperSize", "rtl", "rtlTranslate", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "slides", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "slidesNumberEvenToRows", "slideSize", "virtualSize", "marginLeft", "marginTop", "marginRight", "marginBottom", "slidesPerColumn", "Math", "floor", "ceil", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerColumnFill", "max", "newSlidesGrid", "slidesPerRow", "numFullColumns", "slide", "newSlideOrderIndex", "column", "row", "-webkit-box-ordinal-group", "-moz-box-ordinal-group", "-ms-flex-order", "-webkit-order", "order", "slideStyles", "currentTransform", "currentWebKitTransform", "roundLengths", "swiperSlideSize", "centeredSlides", "abs", "slidesPerGroup", "effect", "setWrapperSize", "i$1", "slidesGridItem", "i$2", "slidesGridItem$1", "watchOverflow", "checkOverflow", "watchSlidesProgress", "watchSlidesVisibility", "updateSlidesOffset", "updateAutoHeight", "speed", "activeSlides", "newHeight", "setTransition", "activeIndex", "swiperSlideOffset", "offsetLeft", "offsetTop", "updateSlidesProgress", "translate", "offsetCenter", "slideVisibleClass", "slideProgress", "minTranslate", "slideBefore", "slideAfter", "progress", "updateProgress", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "wasBeginning", "wasEnd", "updateSlidesClasses", "activeSlide", "realIndex", "slideActiveClass", "loop", "slideDuplicateClass", "slideDuplicateActiveClass", "nextSlide", "slideNextClass", "prevSlide", "slidePrevClass", "slideDuplicateNextClass", "slideDuplicatePrevClass", "updateActiveIndex", "newActiveIndex", "snapIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "normalizeSlideIndex", "updateClickedSlide", "slideFound", "clickedSlide", "clickedIndex", "slideToClickedSlide", "virtualTranslate", "currentTranslate", "setTranslate", "byController", "x", "y", "previousTranslate", "transition$1", "transitionStart", "runCallbacks", "direction", "autoHeight", "dir", "animating", "slideTo", "internal", "slideIndex", "preventInteractionOnTransition", "initialSlide", "initialized", "allowSlideNext", "allowSlidePrev", "onSlideToWrapperTransitionEnd", "destroyed", "slideToLoop", "newIndex", "loopedSlides", "slideNext", "loopFix", "_clientLeft", "slidePrev", "normalize", "val", "prevIndex", "normalizedTranslate", "normalizedSnapGrid", "prevSnap", "slideReset", "slideToClosest", "currentSnap", "slidesPerViewDynamic", "slideToIndex", "loopCreate", "loopFillGroupWithBlank", "blankSlidesNum", "blankNode", "loopAdditionalSlides", "prependSlides", "appendSlides", "cloneNode", "diff", "loop<PERSON><PERSON><PERSON>", "grabCursor", "setGrabCursor", "moving", "simulate<PERSON>ouch", "isLocked", "cursor", "unsetGrabCursor", "manipulation", "appendSlide", "prependSlide", "addSlide", "activeIndexBuffer", "baseLength", "slidesBuffer", "currentSlide", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "<PERSON><PERSON>", "ua", "device", "ios", "android", "androidChrome", "desktop", "windows", "iphone", "ipod", "ipad", "<PERSON><PERSON>", "phonegap", "os", "osVersion", "toLowerCase", "webView", "osVersionArr", "metaViewport", "minimalUi", "pixelRatio", "devicePixelRatio", "onResize", "breakpoints", "setBreakpoint", "freeMode", "newTranslate", "min", "attachEvents", "touchEvents", "wrapperEl", "onTouchStart", "touchEventsData", "touches", "originalEvent", "isTouchEvent", "type", "which", "isTouched", "isMoved", "noSwiping", "noSwipingSelector", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "targetTouches", "pageX", "currentY", "pageY", "startX", "startY", "edgeSwipeDetection", "iOSEdgeSwipeDetection", "edgeSwipeThreshold", "iOSEdgeSwipeThreshold", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "threshold", "allowThresholdMove", "preventDefault", "formElements", "allowTouchMove", "onTouchMove", "preventedByNestedSwiper", "touchReleaseOnEdges", "touchAngle", "diffX", "diffY", "sqrt", "pow", "atan2", "PI", "touchMoveStopPropagation", "nested", "stopPropagation", "startTranslate", "allowMomentumBounce", "touchRatio", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "velocities", "position", "time", "onTouchEnd", "currentPos", "touchEndTime", "timeDiff", "lastClickTime", "clickTimeout", "freeModeMomentum", "lastMoveEvent", "pop", "velocityEvent", "distance", "velocity", "freeModeMinimumVelocity", "freeModeMomentumVelocityRatio", "momentumDuration", "freeModeMomentumRatio", "momentumDistance", "newPosition", "afterBouncePosition", "needsLoopFix", "doBounce", "bounceAmount", "freeModeMomentumBounceRatio", "freeModeMomentumBounce", "freeModeSticky", "longSwipesMs", "stopIndex", "groupSize", "ratio", "longSwipes", "longSwipesRatio", "shortSwipes", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "touchEventsTarget", "start", "passiveListeners", "passive", "move", "end", "detachEvents", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakPointsParams", "originalParams", "needsReLoop", "points", "point", "sort", "b", "innerWidth", "Browser", "isIE", "<PERSON><PERSON><PERSON><PERSON>", "isUiWebView", "test", "defaults", "init", "uniqueNavElements", "preloadImages", "updateOnImagesReady", "noSwipingClass", "containerModifierClass", "slideClass", "slideBlankClass", "wrapperClass", "runCallbacksOnInit", "prototypes", "wasLocked", "navigation", "addClasses", "classNames", "suffixes", "suffix", "removeClasses", "images", "loadImage", "imageEl", "src", "srcset", "sizes", "checkForComplete", "image", "onReady", "complete", "onload", "onerror", "imagesLoaded", "imagesToLoad", "currentSrc", "extendedDefaults", "SwiperClass$$1", "prototypeGroup", "protoMethod", "moduleParamName", "swiperParams", "passedParams", "swipers", "containerEl", "newParams", "touchEventsTouch", "touchEventsDesktop", "__proto__", "spv", "breakLoop", "translateValue", "destroy", "deleteInstance", "cleanStyles", "extendDefaults", "newDefaults", "Device$1", "Support$1", "support", "Browser$1", "browser", "Resize", "resize", "resize<PERSON><PERSON>ler", "orientationChangeHandler", "Observer", "func", "MutationObserver", "WebkitMutationObserver", "attach", "options", "mutations", "observerUpdate", "requestAnimationFrame", "observe", "attributes", "childList", "characterData", "observers", "observeParents", "containerParents", "disconnect", "Observer$1", "Virtual", "force", "ref", "ref$1", "previousFrom", "from", "previousTo", "previousSlidesGrid", "renderSlide", "previousOffset", "offsetProp", "slidesAfter", "slidesBefore", "onRendered", "lazy", "load", "renderExternal", "slidesToRender", "prependIndexes", "appendIndexes", "cache", "$slideEl", "newCache", "cachedIndex", "Virtual$1", "beforeInit", "overwriteParams", "Keyboard", "handle", "kc", "keyCode", "charCode", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "keyboard", "onlyInViewport", "inView", "windowWidth", "windowHeight", "innerHeight", "swiperOffset", "swiperCoord", "returnValue", "enable", "disable", "Keyboard$1", "Mousewheel", "lastScrollTime", "isSupported", "element", "implementation", "hasFeature", "isEventSupported", "sX", "sY", "pX", "pY", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "pixelX", "pixelY", "handleMouseEnter", "mouseEntered", "handleMouseLeave", "mousewheel", "releaseOnEdges", "delta", "rtlFactor", "forceToAxis", "invert", "sensitivity", "timeout", "autoplay", "autoplayDisableOnInteraction", "stop", "getTime", "eventsTarged", "Navigation", "$nextEl", "$prevEl", "disabledClass", "lockClass", "nextEl", "prevEl", "Pagination", "pagination", "current", "total", "paginationType", "bullets", "firstIndex", "lastIndex", "midIndex", "dynamicBullets", "bulletSize", "dynamicMainBullets", "dynamicBulletIndex", "bullet", "$bullet", "bulletIndex", "bulletActiveClass", "$firstDisplayedBullet", "$lastDisplayedBullet", "dynamicBulletsLength", "bulletsOffset", "formatFractionCurrent", "formatFractionTotal", "progressbarDirection", "progressbarOpposite", "scale", "scaleX", "scaleY", "renderCustom", "render", "paginationHTML", "numberOfBullets", "renderBullet", "bulletClass", "renderFraction", "currentClass", "totalClass", "renderProgressbar", "progressbarFillClass", "clickable", "clickableClass", "modifierClass", "progressbarOppositeClass", "hiddenClass", "Sc<PERSON><PERSON>", "scrollbar", "dragSize", "trackSize", "$dragEl", "newSize", "newPos", "hide", "opacity", "divider", "moveDivider", "display", "scrollbarHide", "setDragPosition", "positionRatio", "clientX", "clientY", "onDragStart", "dragTimeout", "onDragMove", "onDragEnd", "snapOnRelease", "enableDraggable", "activeListener", "disableDraggable", "$swiperEl", "dragEl", "draggable", "Parallax", "setTransform", "p", "currentOpacity", "currentScale", "parallax", "slideEl", "parallaxEl", "$parallaxEl", "parallaxDuration", "Zoom", "getDistanceBetweenTouches", "x1", "y1", "x2", "y2", "onGestureStart", "zoom", "gesture", "fakeGestureTouched", "fakeGestureMoved", "scaleStart", "$imageEl", "$imageWrapEl", "maxRatio", "isScaling", "onGestureChange", "scaleMove", "minRatio", "onGestureEnd", "changedTouches", "touchesStart", "slideWidth", "slideHeight", "scaledWidth", "scaledHeight", "minX", "maxX", "minY", "maxY", "touchesCurrent", "prevPositionX", "prevPositionY", "prevTime", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "onTransitionEnd", "out", "in", "touchX", "touchY", "translateX", "translateY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "Lazy", "loadInSlide", "loadInDuplicate", "$images", "elementClass", "loadedClass", "loadingClass", "imageIndex", "background", "slideOriginalIndex", "originalSlide", "duplicatedSlide", "slideExist", "initialImageLoaded", "elIndex", "loadPrevNext", "loadPrevNextAmount", "amount", "maxIndex", "minIndex", "Controller", "LinearSpline", "guess", "i1", "i3", "binarySearch", "array", "interpolate", "getInterpolateFunction", "c", "controller", "spline", "setTranslate$1", "multiplier", "controlledTranslate", "controlled", "control", "setControlledTranslate", "by", "inverse", "setControlledTransition", "a11y", "makeElFocusable", "addElRole", "role", "addElLabel", "label", "disableEl", "enableEl", "onEnterKey", "$targetEl", "notify", "lastSlideMessage", "nextSlideMessage", "firstSlideMessage", "prevSlideMessage", "click", "message", "notification", "liveRegion", "updateNavigation", "updatePagination", "bulletEl", "$bulletEl", "paginationBulletMessage", "History", "pushState", "hashNavigation", "paths", "get<PERSON>ath<PERSON><PERSON><PERSON>", "scrollToSlide", "replaceState", "setHistoryPopState", "pathArray", "pathname", "part", "setHistory", "slugify", "includes", "currentState", "state", "HashNavigation", "onHashCange", "newHash", "setHash", "watchState", "Autoplay", "run", "$activeSlideEl", "reverseDirection", "stopOnLastSlide", "running", "pause", "paused", "waitForTransition", "Fade", "tx", "ty", "slideOpacity", "fadeEffect", "crossFade", "eventTriggered", "triggerEvents", "C<PERSON>", "$cubeShadowEl", "swiper<PERSON><PERSON><PERSON>", "swiperHeight", "cubeEffect", "wrapperRotate", "shadow", "slideAngle", "round", "tz", "slideShadows", "shadowBefore", "shadowAfter", "-webkit-transform-origin", "-moz-transform-origin", "-ms-transform-origin", "transform-origin", "shadowOffset", "shadowAngle", "sin", "cos", "scale1", "shadowScale", "scale2", "zFactor", "Flip", "flipEffect", "limitRotation", "rotateY", "rotateX", "zIndex", "Coverflow", "coverflowEffect", "center", "rotate", "depth", "offsetMultiplier", "modifier", "translateZ", "stretch", "slideTransform", "$shadowBeforeEl", "$shadowAfterEl", "<PERSON><PERSON><PERSON><PERSON>", "hideOnClick", "toEdge", "fromEdge", "bulletElement", "number", "activeIndexChange", "snapIndexChange", "slidesLengthChange", "snapGridLengthChange", "dragClass", "containerClass", "zoomedSlideClass", "touchStart", "touchEnd", "doubleTap", "loadOnTransitionStart", "preloaderClass", "scroll", "scrollbarDragMove", "notificationClass", "paginationUpdate", "disableOnInteraction", "beforeTransitionStart", "slider<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;CAYC,SAAUA,EAAQC,GACE,iBAAZC,SAA0C,oBAAXC,OAAyBA,OAAOD,QAAUD,IAC9D,mBAAXG,QAAyBA,OAAOC,IAAMD,OAAOH,GACnDD,EAAOM,OAASL,IAHnB,CAIEM,KAAM,WAAe,aAarB,IAAIC,EAA2B,oBAAbC,SAA4B,CAC5CC,KAAM,GACNC,iBAAkB,aAClBC,oBAAqB,aACrBC,cAAe,CACbC,KAAM,aACNC,SAAU,IAEZC,cAAe,WACb,OAAO,MAETC,iBAAkB,WAChB,MAAO,IAETC,eAAgB,WACd,OAAO,MAETC,YAAa,WACX,MAAO,CACLC,UAAW,eAGfC,cAAe,WACb,MAAO,CACLC,SAAU,GACVC,WAAY,GACZC,MAAO,GACPC,aAAc,aACdC,qBAAsB,WACpB,MAAO,MAIbC,SAAU,CAAEC,KAAM,KAChBnB,SAEAoB,EAAyB,oBAAXC,OAA0B,CAC1CrB,SAAUD,EACVuB,UAAW,CACTC,UAAW,IAEbL,SAAU,GACVM,QAAS,GACTC,YAAa,WACX,OAAO3B,MAETI,iBAAkB,aAClBC,oBAAqB,aACrBuB,iBAAkB,WAChB,MAAO,CACLC,iBAAkB,WAChB,MAAO,MAIbC,MAAO,aACPC,KAAM,aACNC,OAAQ,GACRC,WAAY,aACZC,aAAc,cACZX,OAgBAY,EAAO,SAAcC,GAGvB,IAFA,IAESC,EAAI,EAAGA,EAAID,EAAIE,OAAQD,GAAK,EAF1BrC,KAGJqC,GAAKD,EAAIC,GAIhB,OAPWrC,KAKNsC,OAASF,EAAIE,OAEXtC,MAGT,SAASuC,EAAEC,EAAUC,GACnB,IAAIL,EAAM,GACNC,EAAI,EACR,GAAIG,IAAaC,GACXD,aAAoBL,EACtB,OAAOK,EAGX,GAAIA,EAEF,GAAwB,iBAAbA,EAAuB,CAChC,IAAIE,EACAC,EACAC,EAAOJ,EAASK,OACpB,GAAyB,GAArBD,EAAKE,QAAQ,MAAkC,GAArBF,EAAKE,QAAQ,KAAW,CACpD,IAAIC,EAAW,MAQf,IAP4B,IAAxBH,EAAKE,QAAQ,SAAgBC,EAAW,MAChB,IAAxBH,EAAKE,QAAQ,SAAgBC,EAAW,SAChB,IAAxBH,EAAKE,QAAQ,QAAwC,IAAxBF,EAAKE,QAAQ,SAAgBC,EAAW,MAC1C,IAA3BH,EAAKE,QAAQ,YAAmBC,EAAW,SACf,IAA5BH,EAAKE,QAAQ,aAAoBC,EAAW,WAChDJ,EAAa1C,EAAIa,cAAciC,IACpBC,UAAYJ,EAClBP,EAAI,EAAGA,EAAIM,EAAW3B,WAAWsB,OAAQD,GAAK,EACjDD,EAAIa,KAAKN,EAAW3B,WAAWqB,SAUjC,IAFEK,EALGD,GAA2B,MAAhBD,EAAS,IAAeA,EAASU,MAAM,aAK9CT,GAAWxC,GAAKS,iBAAiB8B,EAASK,QAH3C,CAAC5C,EAAIU,eAAe6B,EAASK,OAAOM,MAAM,KAAK,KAKlDd,EAAI,EAAGA,EAAIK,EAAIJ,OAAQD,GAAK,EAC3BK,EAAIL,IAAMD,EAAIa,KAAKP,EAAIL,SAG1B,GAAIG,EAASY,UAAYZ,IAAalB,GAAOkB,IAAavC,EAE/DmC,EAAIa,KAAKT,QACJ,GAAsB,EAAlBA,EAASF,QAAcE,EAAS,GAAGY,SAE5C,IAAKf,EAAI,EAAGA,EAAIG,EAASF,OAAQD,GAAK,EACpCD,EAAIa,KAAKT,EAASH,IAIxB,OAAO,IAAIF,EAAKC,GAOlB,SAASiB,EAAOjB,GAEd,IADA,IAAIkB,EAAc,GACTjB,EAAI,EAAGA,EAAID,EAAIE,OAAQD,GAAK,GACE,IAAjCiB,EAAYR,QAAQV,EAAIC,KAAciB,EAAYL,KAAKb,EAAIC,IAEjE,OAAOiB,EATTf,EAAEgB,GAAKpB,EAAKqB,UACZjB,EAAEkB,MAAQtB,EACVI,EAAEJ,KAAOA,EAkqBT,IAAIuB,EAAU,CACZC,SAxpBF,SAAkBC,GAGhB,QAAyB,IAAdA,EACT,OAAO5D,KAGT,IADA,IAAI6D,EAAUD,EAAUT,MAAM,KACrBd,EAAI,EAAGA,EAAIwB,EAAQvB,OAAQD,GAAK,EACvC,IAAK,IAAIyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,OACX,IARhB9D,KAQS8D,SAAqD,IAR9D9D,KAQ6C8D,GAAGC,WARhD/D,KAQoF8D,GAAGC,UAAUC,IAAIH,EAAQxB,IAG1H,OAAOrC,MA6oBPiE,YA3oBF,SAAqBL,GAInB,IAHA,IAEIC,EAAUD,EAAUT,MAAM,KACrBd,EAAI,EAAGA,EAAIwB,EAAQvB,OAAQD,GAAK,EACvC,IAAK,IAAIyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,OACX,IALhB9D,KAKS8D,SAAqD,IAL9D9D,KAK6C8D,GAAGC,WALhD/D,KAKoF8D,GAAGC,UAAUG,OAAOL,EAAQxB,IAG7H,OAAOrC,MAmoBPmE,SAjoBF,SAAkBP,GAChB,QAAK5D,KAAK,IACHA,KAAK,GAAG+D,UAAUK,SAASR,IAgoBlCS,YA9nBF,SAAqBT,GAInB,IAHA,IAEIC,EAAUD,EAAUT,MAAM,KACrBd,EAAI,EAAGA,EAAIwB,EAAQvB,OAAQD,GAAK,EACvC,IAAK,IAAIyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,OACX,IALhB9D,KAKS8D,SAAqD,IAL9D9D,KAK6C8D,GAAGC,WALhD/D,KAKoF8D,GAAGC,UAAUO,OAAOT,EAAQxB,IAG7H,OAAOrC,MAsnBPuE,KApnBF,SAAcC,EAAOC,GACnB,IAAIC,EAAcC,UAGlB,GAAyB,IAArBA,UAAUrC,QAAiC,iBAAVkC,EAEnC,OAAIxE,KAAK,GAAaA,KAAK,GAAG4E,aAAaJ,QAC3C,EAIF,IAAK,IAAInC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACpC,GAA2B,IAAvBqC,EAAYpC,OAVLtC,KAYFqC,GAAGnB,aAAasD,EAAOC,QAI9B,IAAK,IAAII,KAAYL,EAhBZxE,KAiBAqC,GAAGwC,GAAYL,EAAMK,GAjBrB7E,KAkBAqC,GAAGnB,aAAa2D,EAAUL,EAAMK,IAI7C,OAAO7E,MA6lBP8E,WA1lBF,SAAoBP,GAGlB,IAFA,IAESlC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAFzBrC,KAGJqC,GAAG0C,gBAAgBR,GAE5B,OAAOvE,MAqlBPgF,KAnlBF,SAAcC,EAAKR,GACjB,IAEIS,EACJ,QAAqB,IAAVT,EAAX,CAkBA,IAAK,IAAIpC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,GACpC6C,EAtBWlF,KAsBCqC,IACJ8C,yBAA0BD,EAAGC,uBAAyB,IAC9DD,EAAGC,uBAAuBF,GAAOR,EAEnC,OAAOzE,KApBL,GAFAkF,EAAKlF,KAAK,GAEF,CACN,GAAIkF,EAAGC,wBAA2BF,KAAOC,EAAGC,uBAC1C,OAAOD,EAAGC,uBAAuBF,GAGnC,IAAIG,EAAUF,EAAGN,aAAc,QAAUK,GACzC,OAAIG,QAGJ,IAokBJC,UArjBF,SAAmBA,GAGjB,IAFA,IAEShD,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAAG,CACvC,IAAIiD,EAHOtF,KAGUqC,GAAGpB,MACxBqE,EAAQC,gBAAkBF,EAC1BC,EAAQD,UAAYA,EAEtB,OAAOrF,MA8iBPwF,WA5iBF,SAAoBC,GAGM,iBAAbA,IACTA,GAAsB,MAExB,IAAK,IAAIpD,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAAG,CACvC,IAAIiD,EANOtF,KAMUqC,GAAGpB,MACxBqE,EAAQI,yBAA2BD,EACnCH,EAAQK,mBAAqBF,EAE/B,OAAOzF,MAkiBP4F,GA/hBF,WAKE,IAJA,IACIC,EAEAC,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GACzC,IAAIC,EAAYF,EAAK,GACjBG,EAAiBH,EAAK,GACtBI,EAAWJ,EAAK,GAChBK,EAAUL,EAAK,GAOnB,SAASM,EAAgBC,GACvB,IAAIC,EAASD,EAAEC,OACf,GAAKA,EAAL,CACA,IAAIC,EAAYF,EAAEC,OAAOE,eAAiB,GAI1C,GAHID,EAAUzD,QAAQuD,GAAK,GACzBE,EAAUE,QAAQJ,GAEhB9D,EAAE+D,GAAQI,GAAGT,GAAmBC,EAASS,MAAML,EAAQC,QAGzD,IADA,IAAIK,EAAUrE,EAAE+D,GAAQM,UACfC,EAAI,EAAGA,EAAID,EAAQtE,OAAQuE,GAAK,EACnCtE,EAAEqE,EAAQC,IAAIH,GAAGT,IAAmBC,EAASS,MAAMC,EAAQC,GAAIN,IAIzE,SAASO,EAAYT,GACnB,IAAIE,EAAYF,GAAKA,EAAEC,QAASD,EAAEC,OAAOE,eAAsB,GAC3DD,EAAUzD,QAAQuD,GAAK,GACzBE,EAAUE,QAAQJ,GAEpBH,EAASS,MAAM3G,KAAMuG,GA1BA,mBAAZT,EAAK,KACEE,GAAfH,EAASC,GAAyB,GAAII,EAAWL,EAAO,GAAIM,EAAUN,EAAO,GAC9EI,OAAiBc,GAEdZ,IAAWA,GAAU,GA0B1B,IAFA,IACIrC,EADAkD,EAAShB,EAAU7C,MAAM,KAEpBd,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAAG,CACvC,IAAI6C,EAxCOlF,KAwCKqC,GAChB,GAAK4D,EAaH,IAAKnC,EAAI,EAAGA,EAAIkD,EAAO1E,OAAQwB,GAAK,EAAG,CACrC,IAAImD,EAAUD,EAAOlD,GAChBoB,EAAGgC,oBAAqBhC,EAAGgC,kBAAoB,IAC/ChC,EAAGgC,kBAAkBD,KAAY/B,EAAGgC,kBAAkBD,GAAW,IACtE/B,EAAGgC,kBAAkBD,GAAShE,KAAK,CACjCiD,SAAUA,EACViB,cAAef,IAEjBlB,EAAG9E,iBAAiB6G,EAASb,EAAiBD,QApBhD,IAAKrC,EAAI,EAAGA,EAAIkD,EAAO1E,OAAQwB,GAAK,EAAG,CACrC,IAAIsD,EAAQJ,EAAOlD,GACdoB,EAAGmC,gBAAiBnC,EAAGmC,cAAgB,IACvCnC,EAAGmC,cAAcD,KAAUlC,EAAGmC,cAAcD,GAAS,IAC1DlC,EAAGmC,cAAcD,GAAOnE,KAAK,CAC3BiD,SAAUA,EACViB,cAAeL,IAEjB5B,EAAG9E,iBAAiBgH,EAAON,EAAaX,IAgB9C,OAAOnG,MA6dPsH,IA3dF,WAKE,IAJA,IACIzB,EAEAC,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GACzC,IAAIC,EAAYF,EAAK,GACjBG,EAAiBH,EAAK,GACtBI,EAAWJ,EAAK,GAChBK,EAAUL,EAAK,GACI,mBAAZA,EAAK,KACEE,GAAfH,EAASC,GAAyB,GAAII,EAAWL,EAAO,GAAIM,EAAUN,EAAO,GAC9EI,OAAiBc,GAEdZ,IAAWA,GAAU,GAG1B,IADA,IAAIa,EAAShB,EAAU7C,MAAM,KACpBd,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAEtC,IADA,IAAI+E,EAAQJ,EAAO3E,GACVyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,EAAG,CACvC,IAAIoB,EAnBKlF,KAmBO8D,GACZyD,OAAW,EAMf,IALKtB,GAAkBf,EAAGmC,cACxBE,EAAWrC,EAAGmC,cAAcD,GACnBnB,GAAkBf,EAAGgC,oBAC9BK,EAAWrC,EAAGgC,kBAAkBE,IAE9BG,GAAYA,EAASjF,OACvB,IAAK,IAAIuE,EAAIU,EAASjF,OAAS,EAAQ,GAALuE,EAAQA,GAAK,EAAG,CAChD,IAAIW,EAAUD,EAASV,GACnBX,GAAYsB,EAAQtB,WAAaA,GACnChB,EAAG7E,oBAAoB+G,EAAOI,EAAQL,cAAehB,GACrDoB,EAASE,OAAOZ,EAAG,IACTX,IACVhB,EAAG7E,oBAAoB+G,EAAOI,EAAQL,cAAehB,GACrDoB,EAASE,OAAOZ,EAAG,KAM7B,OAAO7G,MAmbP0H,QAjbF,WAGE,IAFA,IACI5B,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAIzC,IAFA,IAAIiB,EAASlB,EAAK,GAAG3C,MAAM,KACvBoD,EAAYT,EAAK,GACZzD,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAEtC,IADA,IAAI+E,EAAQJ,EAAO3E,GACVyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,EAAG,CACvC,IAAIoB,EATKlF,KASO8D,GACZ6D,OAAM,EACV,IACEA,EAAM,IAAIrG,EAAIK,YAAYyF,EAAO,CAC/BQ,OAAQrB,EACRsB,SAAS,EACTC,YAAY,IAEd,MAAOzB,IACPsB,EAAM1H,EAAIW,YAAY,UAClBC,UAAUuG,GAAO,GAAM,GAC3BO,EAAIC,OAASrB,EAGfrB,EAAGsB,cAAgBV,EAAKiC,OAAO,SAAU/C,EAAMgD,GAAa,OAAmB,EAAZA,IACnE9C,EAAG+C,cAAcN,GACjBzC,EAAGsB,cAAgB,UACZtB,EAAGsB,cAGd,OAAOxG,MAoZPkI,cAlZF,SAAuBC,GACrB,IAEI9F,EAFA2E,EAAS,CAAC,sBAAuB,iBACjCoB,EAAMpI,KAEV,SAASqI,EAAahC,GAEpB,GAAIA,EAAEC,SAAWtG,KAEjB,IADAmI,EAASG,KAAKtI,KAAMqG,GACfhE,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAClC+F,EAAId,IAAIN,EAAO3E,GAAIgG,GAGvB,GAAIF,EACF,IAAK9F,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAClC+F,EAAIxC,GAAGoB,EAAO3E,GAAIgG,GAGtB,OAAOrI,MAkYPuI,WAhYF,SAAoBC,GAClB,GAAkB,EAAdxI,KAAKsC,OAAY,CACnB,GAAIkG,EAAgB,CAElB,IAAIC,EAASzI,KAAKyI,SAClB,OAAOzI,KAAK,GAAG0I,YAAcC,WAAWF,EAAO5G,iBAAiB,iBAAmB8G,WAAWF,EAAO5G,iBAAiB,gBAExH,OAAO7B,KAAK,GAAG0I,YAEjB,OAAO,MAwXPE,YAtXF,SAAqBJ,GACnB,GAAkB,EAAdxI,KAAKsC,OAAY,CACnB,GAAIkG,EAAgB,CAElB,IAAIC,EAASzI,KAAKyI,SAClB,OAAOzI,KAAK,GAAG6I,aAAeF,WAAWF,EAAO5G,iBAAiB,eAAiB8G,WAAWF,EAAO5G,iBAAiB,kBAEvH,OAAO7B,KAAK,GAAG6I,aAEjB,OAAO,MA8WPC,OA5WF,WACE,GAAkB,EAAd9I,KAAKsC,OAAY,CACnB,IAAI4C,EAAKlF,KAAK,GACV+I,EAAM7D,EAAG8D,wBACT7I,EAAOF,EAAIE,KACX8I,EAAY/D,EAAG+D,WAAa9I,EAAK8I,WAAa,EAC9CC,EAAahE,EAAGgE,YAAc/I,EAAK+I,YAAc,EACjDC,EAAYjE,IAAO5D,EAAMA,EAAI8H,QAAUlE,EAAGiE,UAC1CE,EAAanE,IAAO5D,EAAMA,EAAIgI,QAAUpE,EAAGmE,WAC/C,MAAO,CACLE,IAAMR,EAAIQ,IAAMJ,EAAaF,EAC7BO,KAAOT,EAAIS,KAAOH,EAAcH,GAIpC,OAAO,MA8VPO,IAxVF,SAAaC,EAAOjF,GAClB,IAEIpC,EACJ,GAAyB,IAArBsC,UAAUrC,OAAc,CAC1B,GAAqB,iBAAVoH,EAEJ,CACL,IAAKrH,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEhC,IAAK,IAAIsH,KAAQD,EATV1J,KAUEqC,GAAGpB,MAAM0I,GAAQD,EAAMC,GAGlC,OAAO3J,KARP,GAAIA,KAAK,GAAM,OAAOsB,EAAIM,iBAAiB5B,KAAK,GAAI,MAAM6B,iBAAiB6H,GAW/E,GAAyB,IAArB/E,UAAUrC,QAAiC,iBAAVoH,EAAoB,CACvD,IAAKrH,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAjBvBrC,KAkBFqC,GAAGpB,MAAMyI,GAASjF,EAE3B,OAAOzE,KAET,OAAOA,MAkUP4J,KA/TF,SAAczB,GAIZ,IAAKA,EAAY,OAAOnI,KAExB,IAAK,IAAIqC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEpC,IAA+C,IAA3C8F,EAASG,KAPFtI,KAOcqC,GAAIA,EAPlBrC,KAO4BqC,IAErC,OATSrC,KAab,OAAOA,MAkTP4C,KA/SF,SAAcA,GAGZ,QAAoB,IAATA,EACT,OAAO5C,KAAK,GAAKA,KAAK,GAAGgD,eAAY+D,EAGvC,IAAK,IAAI1E,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EANzBrC,KAOJqC,GAAGW,UAAYJ,EAExB,OAAO5C,MAsSP6J,KAnSF,SAAcA,GAGZ,QAAoB,IAATA,EACT,OAAI7J,KAAK,GACAA,KAAK,GAAG8J,YAAYjH,OAEtB,KAGT,IAAK,IAAIR,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EATzBrC,KAUJqC,GAAGyH,YAAcD,EAE1B,OAAO7J,MAuRP0G,GArRF,SAAYlE,GACV,IACIuH,EACA1H,EAFA6C,EAAKlF,KAAK,GAGd,IAAKkF,QAA0B,IAAb1C,EAA4B,OAAO,EACrD,GAAwB,iBAAbA,EAAuB,CAChC,GAAI0C,EAAG8E,QAAW,OAAO9E,EAAG8E,QAAQxH,GAC/B,GAAI0C,EAAG+E,sBAAyB,OAAO/E,EAAG+E,sBAAsBzH,GAChE,GAAI0C,EAAGgF,kBAAqB,OAAOhF,EAAGgF,kBAAkB1H,GAG7D,IADAuH,EAAcxH,EAAEC,GACXH,EAAI,EAAGA,EAAI0H,EAAYzH,OAAQD,GAAK,EACvC,GAAI0H,EAAY1H,KAAO6C,EAAM,OAAO,EAEtC,OAAO,EACF,GAAI1C,IAAavC,EAAO,OAAOiF,IAAOjF,EACxC,GAAIuC,IAAalB,EAAO,OAAO4D,IAAO5D,EAE3C,GAAIkB,EAASY,UAAYZ,aAAoBL,EAAM,CAEjD,IADA4H,EAAcvH,EAASY,SAAW,CAACZ,GAAYA,EAC1CH,EAAI,EAAGA,EAAI0H,EAAYzH,OAAQD,GAAK,EACvC,GAAI0H,EAAY1H,KAAO6C,EAAM,OAAO,EAEtC,OAAO,EAET,OAAO,GA6PPiF,MA3PF,WACE,IACI9H,EADA+H,EAAQpK,KAAK,GAEjB,GAAIoK,EAAO,CAGT,IAFA/H,EAAI,EAEuC,QAAnC+H,EAAQA,EAAMC,kBACG,IAAnBD,EAAMhH,WAAkBf,GAAK,GAEnC,OAAOA,IAmPTiI,GA9OF,SAAYH,GACV,QAAqB,IAAVA,EAAyB,OAAOnK,KAC3C,IACIuK,EADAjI,EAAStC,KAAKsC,OAElB,OACS,IAAIH,EADDG,EAAS,EAAjB6H,EACc,GAEdA,EAAQ,GACVI,EAAcjI,EAAS6H,GACL,EAAqB,GACvB,CAACnK,KAAKuK,IAER,CAACvK,KAAKmK,MAmOtBK,OAjOF,WAGE,IAFA,IAIIC,EAHA3E,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAIzC,IAAK,IAAIc,EAAI,EAAGA,EAAIf,EAAKxD,OAAQuE,GAAK,EAAG,CACvC4D,EAAW3E,EAAKe,GAChB,IAAK,IAAIxE,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACpC,GAAwB,iBAAboI,EAAuB,CAChC,IAAIC,EAAUzK,EAAIa,cAAc,OAEhC,IADA4J,EAAQ1H,UAAYyH,EACbC,EAAQC,YAZR3K,KAaEqC,GAAGuI,YAAYF,EAAQC,iBAE3B,GAAIF,aAAoBtI,EAC7B,IAAK,IAAI2B,EAAI,EAAGA,EAAI2G,EAASnI,OAAQwB,GAAK,EAhBnC9D,KAiBEqC,GAAGuI,YAAYH,EAAS3G,SAjB1B9D,KAoBAqC,GAAGuI,YAAYH,GAK5B,OAAOzK,MAwMP6K,QAtMF,SAAiBJ,GACf,IAEIpI,EACAyB,EAHAgH,EAAS9K,KAIb,IAAKqC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAChC,GAAwB,iBAAboI,EAAuB,CAChC,IAAIC,EAAUzK,EAAIa,cAAc,OAEhC,IADA4J,EAAQ1H,UAAYyH,EACf3G,EAAI4G,EAAQ1J,WAAWsB,OAAS,EAAQ,GAALwB,EAAQA,GAAK,EACnDgH,EAAOzI,GAAG0I,aAAaL,EAAQ1J,WAAW8C,GAAIgH,EAAOzI,GAAGrB,WAAW,SAEhE,GAAIyJ,aAAoBtI,EAC7B,IAAK2B,EAAI,EAAGA,EAAI2G,EAASnI,OAAQwB,GAAK,EACpCgH,EAAOzI,GAAG0I,aAAaN,EAAS3G,GAAIgH,EAAOzI,GAAGrB,WAAW,SAG3D8J,EAAOzI,GAAG0I,aAAaN,EAAUK,EAAOzI,GAAGrB,WAAW,IAG1D,OAAOhB,MAmLPgL,KAjLF,SAAcxI,GACZ,OAAkB,EAAdxC,KAAKsC,OACHE,EACExC,KAAK,GAAGiL,oBAAsB1I,EAAEvC,KAAK,GAAGiL,oBAAoBvE,GAAGlE,GAC1D,IAAIL,EAAK,CAACnC,KAAK,GAAGiL,qBAEpB,IAAI9I,EAAK,IAGdnC,KAAK,GAAGiL,mBAA6B,IAAI9I,EAAK,CAACnC,KAAK,GAAGiL,qBACpD,IAAI9I,EAAK,IAEX,IAAIA,EAAK,KAsKhB+I,QApKF,SAAiB1I,GACf,IAAI2I,EAAU,GACVjG,EAAKlF,KAAK,GACd,IAAKkF,EAAM,OAAO,IAAI/C,EAAK,IAC3B,KAAO+C,EAAG+F,oBAAoB,CAC5B,IAAID,EAAO9F,EAAG+F,mBACVzI,EACED,EAAEyI,GAAMtE,GAAGlE,IAAa2I,EAAQlI,KAAK+H,GAClCG,EAAQlI,KAAK+H,GACtB9F,EAAK8F,EAEP,OAAO,IAAI7I,EAAKgJ,IA0JhBC,KAxJF,SAAc5I,GACZ,GAAkB,EAAdxC,KAAKsC,OAAY,CACnB,IAAI4C,EAAKlF,KAAK,GACd,OAAIwC,EACE0C,EAAGmG,wBAA0B9I,EAAE2C,EAAGmG,wBAAwB3E,GAAGlE,GACxD,IAAIL,EAAK,CAAC+C,EAAGmG,yBAEf,IAAIlJ,EAAK,IAGd+C,EAAGmG,uBAAiC,IAAIlJ,EAAK,CAAC+C,EAAGmG,yBAC9C,IAAIlJ,EAAK,IAElB,OAAO,IAAIA,EAAK,KA4IhBmJ,QA1IF,SAAiB9I,GACf,IAAI+I,EAAU,GACVrG,EAAKlF,KAAK,GACd,IAAKkF,EAAM,OAAO,IAAI/C,EAAK,IAC3B,KAAO+C,EAAGmG,wBAAwB,CAChC,IAAID,EAAOlG,EAAGmG,uBACV7I,EACED,EAAE6I,GAAM1E,GAAGlE,IAAa+I,EAAQtI,KAAKmI,GAClCG,EAAQtI,KAAKmI,GACtBlG,EAAKkG,EAEP,OAAO,IAAIjJ,EAAKoJ,IAgIhBC,OA9HF,SAAgBhJ,GAId,IAHA,IAEIoE,EAAU,GACLvE,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACP,OAJlBrC,KAIAqC,GAAGoJ,aACRjJ,EACED,EANGvC,KAMMqC,GAAGoJ,YAAY/E,GAAGlE,IAAaoE,EAAQ3D,KAN7CjD,KAMyDqC,GAAGoJ,YAEnE7E,EAAQ3D,KARDjD,KAQaqC,GAAGoJ,aAI7B,OAAOlJ,EAAEc,EAAOuD,KAkHhBA,QAhHF,SAAiBpE,GAIf,IAHA,IAEIoE,EAAU,GACLvE,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEpC,IADA,IAAImJ,EAJOxL,KAISqC,GAAGoJ,WAChBD,GACDhJ,EACED,EAAEiJ,GAAQ9E,GAAGlE,IAAaoE,EAAQ3D,KAAKuI,GAE3C5E,EAAQ3D,KAAKuI,GAEfA,EAASA,EAAOC,WAGpB,OAAOlJ,EAAEc,EAAOuD,KAkGhB8E,QAhGF,SAAiBlJ,GACf,IAAIkJ,EAAU1L,KACd,YAAwB,IAAbwC,EACF,IAAIL,EAAK,KAEbuJ,EAAQhF,GAAGlE,KACdkJ,EAAUA,EAAQ9E,QAAQpE,GAAU8H,GAAG,IAElCoB,IAyFPC,KAvFF,SAAcnJ,GAIZ,IAHA,IAEIoJ,EAAgB,GACXvJ,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEpC,IADA,IAAIwJ,EAJO7L,KAIQqC,GAAG3B,iBAAiB8B,GAC9BsB,EAAI,EAAGA,EAAI+H,EAAMvJ,OAAQwB,GAAK,EACrC8H,EAAc3I,KAAK4I,EAAM/H,IAG7B,OAAO,IAAI3B,EAAKyJ,IA8EhB7K,SA5EF,SAAkByB,GAIhB,IAHA,IAEIzB,EAAW,GACNsB,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAGpC,IAFA,IAAIrB,EAJOhB,KAIaqC,GAAGrB,WAElB8C,EAAI,EAAGA,EAAI9C,EAAWsB,OAAQwB,GAAK,EACrCtB,EAEiC,IAA3BxB,EAAW8C,GAAGV,UAAkBb,EAAEvB,EAAW8C,IAAI4C,GAAGlE,IAC7DzB,EAASkC,KAAKjC,EAAW8C,IAFM,IAA3B9C,EAAW8C,GAAGV,UAAkBrC,EAASkC,KAAKjC,EAAW8C,IAMnE,OAAO,IAAI3B,EAAKkB,EAAOtC,KA8DvBmD,OA5DF,WAGE,IAFA,IAES7B,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAFzBrC,KAGAqC,GAAGoJ,YAHHzL,KAGwBqC,GAAGoJ,WAAWK,YAHtC9L,KAGyDqC,IAEtE,OAAOrC,MAuDPgE,IArDF,WAEE,IADA,IAAI8B,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAEzC,IACI1D,EACAyB,EACJ,IAAKzB,EAAI,EAAGA,EAAIyD,EAAKxD,OAAQD,GAAK,EAAG,CACnC,IAAI0J,EAAQxJ,EAAEuD,EAAKzD,IACnB,IAAKyB,EAAI,EAAGA,EAAIiI,EAAMzJ,OAAQwB,GAAK,EAL3B9D,KAAAA,KAMEsC,QAAUyJ,EAAMjI,GANlB9D,KAOFsC,QAAU,EAGlB,OAVUtC,MAkDVyI,OAhXF,WACE,OAAIzI,KAAK,GAAasB,EAAIM,iBAAiB5B,KAAK,GAAI,MAC7C,KAiXTgM,OAAOC,KAAKvI,GAASwI,QAAQ,SAAUC,GACrC5J,EAAEgB,GAAG4I,GAAczI,EAAQyI,KAG7B,IAkIUlL,EAJAA,EAVJmL,EApHFC,EAAQ,CACVC,YAAa,SAAqBC,GAChC,IAAIC,EAASD,EACbP,OAAOC,KAAKO,GAAQN,QAAQ,SAAUjH,GACpC,IACEuH,EAAOvH,GAAO,KACd,MAAOoB,IAGT,WACSmG,EAAOvH,GACd,MAAOoB,QAKboG,SAAU,SAAkBtE,EAAUuE,GAGpC,YAFe,IAAVA,IAAmBA,EAAQ,GAEzBzK,WAAWkG,EAAUuE,IAE9BC,IAAK,WACH,OAAO5K,KAAK4K,OAEdC,aAAc,SAAsB1H,EAAI2H,GAGtC,IAAIC,EACAC,EACAC,OAJU,IAATH,IAAkBA,EAAO,KAM9B,IAAII,EAAW3L,EAAIM,iBAAiBsD,EAAI,MA+BxC,OA7BI5D,EAAI4L,iBAE+B,GADrCH,EAAeE,EAAS5H,WAAa4H,EAAS1H,iBAC7BpC,MAAM,KAAKb,SAC1ByK,EAAeA,EAAa5J,MAAM,MAAMgK,IAAI,SAAUC,GAAK,OAAOA,EAAEC,QAAQ,IAAK,OAASC,KAAK,OAIjGN,EAAkB,IAAI1L,EAAI4L,gBAAiC,SAAjBH,EAA0B,GAAKA,IAGzED,GADAE,EAAkBC,EAASM,cAAgBN,EAASO,YAAcP,EAASQ,aAAeR,EAASS,aAAeT,EAAS5H,WAAa4H,EAASpL,iBAAiB,aAAawL,QAAQ,aAAc,uBAC5KM,WAAWxK,MAAM,KAG/B,MAAT0J,IAEyBE,EAAvBzL,EAAI4L,gBAAkCF,EAAgBY,IAE/B,KAAlBd,EAAOxK,OAAgCqG,WAAWmE,EAAO,KAE5CnE,WAAWmE,EAAO,KAE7B,MAATD,IAEyBE,EAAvBzL,EAAI4L,gBAAkCF,EAAgBa,IAE/B,KAAlBf,EAAOxK,OAAgCqG,WAAWmE,EAAO,KAE5CnE,WAAWmE,EAAO,KAEnCC,GAAgB,GAEzBe,cAAe,SAAuBC,GACpC,IAEI1L,EACA2L,EACAC,EACA3L,EALA4L,EAAQ,GACRC,EAAaJ,GAAOzM,EAAIF,SAASgN,KAKrC,GAA0B,iBAAfD,GAA2BA,EAAW7L,OAK/C,IAFAA,GADA0L,GADAG,GAAwC,EAA3BA,EAAWrL,QAAQ,KAAYqL,EAAWd,QAAQ,QAAS,IAAM,IAC1DlK,MAAM,KAAK4E,OAAO,SAAUsG,GAAc,MAAsB,KAAfA,KACrD/L,OAEXD,EAAI,EAAGA,EAAIC,EAAQD,GAAK,EAC3B4L,EAAQD,EAAO3L,GAAGgL,QAAQ,QAAS,IAAIlK,MAAM,KAC7C+K,EAAMI,mBAAmBL,EAAM,UAA2B,IAAbA,EAAM,QAAqBlH,EAAYuH,mBAAmBL,EAAM,KAAO,GAGxH,OAAOC,GAETK,SAAU,SAAkBC,GAC1B,MAAoB,iBAANA,GAAwB,OAANA,GAAcA,EAAEC,aAAeD,EAAEC,cAAgBzC,QAEnF0C,OAAQ,WAEN,IADA,IAAI5I,EAAO,GAAI6I,EAAQhK,UAAUrC,OACzBqM,KAAU7I,EAAM6I,GAAUhK,UAAWgK,GAG7C,IADA,IAAIC,EAAK5C,OAAOlG,EAAK,IACZzD,EAAI,EAAGA,EAAIyD,EAAKxD,OAAQD,GAAK,EAAG,CACvC,IAAIwM,EAAa/I,EAAKzD,GACtB,GAAIwM,MAAAA,EAEF,IADA,IAAIC,EAAY9C,OAAOC,KAAKD,OAAO6C,IAC1BE,EAAY,EAAGhJ,EAAM+I,EAAUxM,OAAQyM,EAAYhJ,EAAKgJ,GAAa,EAAG,CAC/E,IAAIC,EAAUF,EAAUC,GACpBE,EAAOjD,OAAOkD,yBAAyBL,EAAYG,QAC1CjI,IAATkI,GAAsBA,EAAKE,aACzB9C,EAAMkC,SAASK,EAAGI,KAAa3C,EAAMkC,SAASM,EAAWG,IAC3D3C,EAAMqC,OAAOE,EAAGI,GAAUH,EAAWG,KAC3B3C,EAAMkC,SAASK,EAAGI,KAAa3C,EAAMkC,SAASM,EAAWG,KACnEJ,EAAGI,GAAW,GACd3C,EAAMqC,OAAOE,EAAGI,GAAUH,EAAWG,KAErCJ,EAAGI,GAAWH,EAAWG,KAMnC,OAAOJ,IAIPQ,GACEhD,EAAUnM,EAAIa,cAAc,OACzB,CACLuO,MAAQ/N,EAAIgO,YAAqC,IAAxBhO,EAAIgO,UAAUD,UAC1B,iBAAkB/N,GAASA,EAAIiO,eAAiBtP,aAAeqB,EAAIiO,eAGhFC,iBAAkBlO,EAAIE,UAAUiO,iBAAkBnO,EAAIoO,cACtDC,wBAAyBrO,EAAIE,UAAUoO,iBAEvCpK,YACMvE,EAAQmL,EAAQnL,MACZ,eAAgBA,GAAS,qBAAsBA,GAAS,kBAAmBA,GAErF4O,aAAevO,EAAIgO,YAA+C,IAAlChO,EAAIgO,UAAUQ,kBACxC7O,EAAQmL,EAAQnL,MACZ,sBAAuBA,GAAS,mBAAoBA,GAAS,iBAAkBA,GAAS,kBAAmBA,GAAS,gBAAiBA,GAG/I8O,QAAU,WAGR,IAFA,IAAI9O,EAAQmL,EAAQnL,MAChBwH,EAAS,yKAA2KtF,MAAM,KACrLd,EAAI,EAAGA,EAAIoG,EAAOnG,OAAQD,GAAK,EACtC,GAAIoG,EAAOpG,KAAMpB,EAAS,OAAO,EAEnC,OAAO,EANA,GAST+O,SACU,qBAAsB1O,GAAO,2BAA4BA,EAGnE2O,gBAAkB,WAChB,IAAIC,GAAkB,EACtB,IACE,IAAIC,EAAOnE,OAAOoE,eAAe,GAAI,UAAW,CAE9CC,IAAK,WACHH,GAAkB,KAGtB5O,EAAIlB,iBAAiB,sBAAuB,KAAM+P,GAClD,MAAO9J,IAGT,OAAO6J,EAbQ,GAgBjBI,SACS,mBAAoBhP,IAK7BiP,EAAc,SAAqBvC,QACrB,IAAXA,IAAoBA,EAAS,IAElC,IAAIwC,EAAOxQ,KACXwQ,EAAKxC,OAASA,EAGdwC,EAAKC,gBAAkB,GAEnBD,EAAKxC,QAAUwC,EAAKxC,OAAOpI,IAC7BoG,OAAOC,KAAKuE,EAAKxC,OAAOpI,IAAIsG,QAAQ,SAAUwE,GAC5CF,EAAK5K,GAAG8K,EAAWF,EAAKxC,OAAOpI,GAAG8K,OAKpCC,EAAkB,CAAEC,WAAY,CAAEC,cAAc,IAEpDN,EAAY/M,UAAUoC,GAAK,SAAaoB,EAAQQ,EAASsJ,GACvD,IAAIN,EAAOxQ,KACX,GAAuB,mBAAZwH,EAA0B,OAAOgJ,EAC5C,IAAIO,EAASD,EAAW,UAAY,OAKpC,OAJA9J,EAAO7D,MAAM,KAAK+I,QAAQ,SAAU9E,GAC7BoJ,EAAKC,gBAAgBrJ,KAAUoJ,EAAKC,gBAAgBrJ,GAAS,IAClEoJ,EAAKC,gBAAgBrJ,GAAO2J,GAAQvJ,KAE/BgJ,GAGTD,EAAY/M,UAAUwN,KAAO,SAAehK,EAAQQ,EAASsJ,GAC3D,IAAIN,EAAOxQ,KACX,GAAuB,mBAAZwH,EAA0B,OAAOgJ,EAQ5C,OAAOA,EAAK5K,GAAGoB,EAPf,SAASiK,IAEL,IADA,IAAInL,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAE3CyB,EAAQb,MAAM6J,EAAM1K,GACpB0K,EAAKlJ,IAAIN,EAAQiK,IAEiBH,IAGtCP,EAAY/M,UAAU8D,IAAM,SAAcN,EAAQQ,GAChD,IAAIgJ,EAAOxQ,KACX,OAAKwQ,EAAKC,iBACVzJ,EAAO7D,MAAM,KAAK+I,QAAQ,SAAU9E,QACX,IAAZI,EACTgJ,EAAKC,gBAAgBrJ,GAAS,GAE9BoJ,EAAKC,gBAAgBrJ,GAAO8E,QAAQ,SAAUgF,EAAc/G,GACtD+G,IAAiB1J,GACnBgJ,EAAKC,gBAAgBrJ,GAAOK,OAAO0C,EAAO,OAK3CqG,GAGTD,EAAY/M,UAAU2N,KAAO,WAEzB,IADA,IAAIrL,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAE3C,IAEIiB,EACAhC,EACAvC,EAJA+N,EAAOxQ,KACX,OAAKwQ,EAAKC,kBAIa,iBAAZ3K,EAAK,IAAmBsL,MAAMC,QAAQvL,EAAK,KACpDkB,EAASlB,EAAK,GACdd,EAAOc,EAAKwL,MAAM,EAAGxL,EAAKxD,QAC1BG,EAAU+N,IAEVxJ,EAASlB,EAAK,GAAGkB,OACjBhC,EAAOc,EAAK,GAAGd,KACfvC,EAAUqD,EAAK,GAAGrD,SAAW+N,IAEbY,MAAMC,QAAQrK,GAAUA,EAASA,EAAO7D,MAAM,MACpD+I,QAAQ,SAAU9E,GAC5B,GAAIoJ,EAAKC,iBAAmBD,EAAKC,gBAAgBrJ,GAAQ,CACvD,IAAIG,EAAW,GACfiJ,EAAKC,gBAAgBrJ,GAAO8E,QAAQ,SAAUgF,GAC5C3J,EAAStE,KAAKiO,KAEhB3J,EAAS2E,QAAQ,SAAUgF,GACzBA,EAAavK,MAAMlE,EAASuC,SAI3BwL,GAGTD,EAAY/M,UAAU+N,iBAAmB,SAA2BC,GAClE,IAAIC,EAAWzR,KACVyR,EAASC,SACd1F,OAAOC,KAAKwF,EAASC,SAASxF,QAAQ,SAAUyF,GAC9C,IAAI/R,EAAS6R,EAASC,QAAQC,GAE1B/R,EAAOoO,QACT3B,EAAMqC,OAAO8C,EAAgB5R,EAAOoO,WAK1CuC,EAAY/M,UAAUoO,WAAa,SAAqBC,QAC7B,IAAlBA,IAA2BA,EAAgB,IAElD,IAAIJ,EAAWzR,KACVyR,EAASC,SACd1F,OAAOC,KAAKwF,EAASC,SAASxF,QAAQ,SAAUyF,GAC9C,IAAI/R,EAAS6R,EAASC,QAAQC,GAC1BG,EAAeD,EAAcF,IAAe,GAE5C/R,EAAO6R,UACTzF,OAAOC,KAAKrM,EAAO6R,UAAUvF,QAAQ,SAAU6F,GAC7C,IAAIC,EAAapS,EAAO6R,SAASM,GAE/BN,EAASM,GADe,mBAAfC,EACkBA,EAAWC,KAAKR,GAEhBO,IAK7BpS,EAAOgG,IAAM6L,EAAS7L,IACxBoG,OAAOC,KAAKrM,EAAOgG,IAAIsG,QAAQ,SAAUgG,GACvCT,EAAS7L,GAAGsM,EAAiBtS,EAAOgG,GAAGsM,MAKvCtS,EAAOuS,QACTvS,EAAOuS,OAAOF,KAAKR,EAAnB7R,CAA6BkS,MAKnCnB,EAAgBC,WAAWwB,IAAM,SAAUxB,GAC7B5Q,KACDqS,KADCrS,KAENqS,IAAIzB,IAGZL,EAAY+B,cAAgB,SAAwB1S,GAEhD,IADA,IAAIoO,EAAS,GAAIjI,EAAMpB,UAAUrC,OAAS,EAC1B,EAARyD,KAAYiI,EAAQjI,GAAQpB,UAAWoB,EAAM,GAEvD,IAAItC,EAAQzD,KACPyD,EAAMD,UAAUkO,UAAWjO,EAAMD,UAAUkO,QAAU,IAC1D,IAAIa,EAAO3S,EAAO2S,MAAWvG,OAAOC,KAAKxI,EAAMD,UAAUkO,SAAe,OAAI,IAAOrF,EAAMM,MAkBzF,OAjBAlJ,EAAMD,UAAUkO,QAAQa,GAAQ3S,GAErB4S,OACTxG,OAAOC,KAAKrM,EAAO4S,OAAOtG,QAAQ,SAAUjH,GAC1CxB,EAAMD,UAAUyB,GAAOrF,EAAO4S,MAAMvN,KAIpCrF,EAAO6S,QACTzG,OAAOC,KAAKrM,EAAO6S,QAAQvG,QAAQ,SAAUjH,GAC3CxB,EAAMwB,GAAOrF,EAAO6S,OAAOxN,KAI3BrF,EAAO8S,SACT9S,EAAO8S,QAAQ/L,MAAMlD,EAAOuK,GAEvBvK,GAGT8M,EAAY8B,IAAM,SAAczS,GAE5B,IADA,IAAIoO,EAAS,GAAIjI,EAAMpB,UAAUrC,OAAS,EAC1B,EAARyD,KAAYiI,EAAQjI,GAAQpB,UAAWoB,EAAM,GAEvD,IAAItC,EAAQzD,KACZ,OAAIoR,MAAMC,QAAQzR,IAChBA,EAAOsM,QAAQ,SAAUyG,GAAK,OAAOlP,EAAM6O,cAAcK,KAClDlP,GAEFA,EAAM6O,cAAc3L,MAAMlD,EAAO,CAAE7D,GAASgT,OAAQ5E,KAG7DhC,OAAO6G,iBAAkBtC,EAAaI,GAwiBtC,IAAImC,EAAS,CACXC,WAviBF,WACE,IACIC,EACAC,EAFAC,EAASlT,KAGTmT,EAAMD,EAAOC,IAEfH,OADiC,IAAxBE,EAAOlF,OAAOgF,MACfE,EAAOlF,OAAOgF,MAEdG,EAAI,GAAGC,YAGfH,OADkC,IAAzBC,EAAOlF,OAAOiF,OACdC,EAAOlF,OAAOiF,OAEdE,EAAI,GAAGE,aAEH,IAAVL,GAAeE,EAAOI,gBAA+B,IAAXL,GAAgBC,EAAOK,eAKtEP,EAAQA,EAAQQ,SAASL,EAAI1J,IAAI,gBAAiB,IAAM+J,SAASL,EAAI1J,IAAI,iBAAkB,IAC3FwJ,EAASA,EAASO,SAASL,EAAI1J,IAAI,eAAgB,IAAM+J,SAASL,EAAI1J,IAAI,kBAAmB,IAE7F4C,EAAMqC,OAAOwE,EAAQ,CACnBF,MAAOA,EACPC,OAAQA,EACRQ,KAAMP,EAAOI,eAAiBN,EAAQC,MA8gBxCS,aA1gBF,WACE,IAAIR,EAASlT,KACTgO,EAASkF,EAAOlF,OAEhB2F,EAAaT,EAAOS,WACpBC,EAAaV,EAAOO,KACpBI,EAAMX,EAAOY,aACbC,EAAWb,EAAOa,SAClBC,EAAYd,EAAOe,SAAWjG,EAAOiG,QAAQC,QAC7CC,EAAuBH,EAAYd,EAAOe,QAAQG,OAAO9R,OAAS4Q,EAAOkB,OAAO9R,OAChF8R,EAAST,EAAW5S,SAAU,IAAOmS,EAAOlF,OAAiB,YAC7DqG,EAAeL,EAAYd,EAAOe,QAAQG,OAAO9R,OAAS8R,EAAO9R,OACjEgS,EAAW,GACXC,EAAa,GACbC,EAAkB,GAElBC,EAAezG,EAAO0G,mBACE,mBAAjBD,IACTA,EAAezG,EAAO0G,mBAAmBpM,KAAK4K,IAGhD,IAAIyB,EAAc3G,EAAO4G,kBACE,mBAAhBD,IACTA,EAAc3G,EAAO4G,kBAAkBtM,KAAK4K,IAG9C,IAAI2B,EAAyB3B,EAAOoB,SAAShS,OACzCwS,EAA2B5B,EAAOoB,SAAShS,OAE3CyS,EAAe/G,EAAO+G,aACtBC,GAAiBP,EACjBQ,EAAgB,EAChB9K,EAAQ,EACZ,QAA0B,IAAfyJ,EAAX,CAaA,IAAIsB,EAaAC,EAvBwB,iBAAjBJ,GAA0D,GAA7BA,EAAajS,QAAQ,OAC3DiS,EAAgBpM,WAAWoM,EAAa1H,QAAQ,IAAK,KAAO,IAAOuG,GAGrEV,EAAOkC,aAAeL,EAGlBlB,EAAOO,EAAO3K,IAAI,CAAE4L,WAAY,GAAIC,UAAW,KAC5ClB,EAAO3K,IAAI,CAAE8L,YAAa,GAAIC,aAAc,KAGtB,EAAzBxH,EAAOyH,kBAEPP,EADEQ,KAAKC,MAAMtB,EAAerG,EAAOyH,mBAAqBpB,EAAenB,EAAOlF,OAAOyH,gBAC5DpB,EAEAqB,KAAKE,KAAKvB,EAAerG,EAAOyH,iBAAmBzH,EAAOyH,gBAExD,SAAzBzH,EAAO6H,eAA2D,QAA/B7H,EAAO8H,sBAC5CZ,EAAyBQ,KAAKK,IAAIb,EAAwBlH,EAAO6H,cAAgB7H,EAAOyH,mBAS5F,IAHA,IA8GIO,EA9GAP,EAAkBzH,EAAOyH,gBACzBQ,EAAef,EAAyBO,EACxCS,EAAiBD,GAAiBjI,EAAOyH,gBAAkBQ,EAAgB5B,GACtEhS,EAAI,EAAGA,EAAIgS,EAAchS,GAAK,EAAG,CACxC8S,EAAY,EACZ,IAAIgB,EAAQ/B,EAAO9J,GAAGjI,GACtB,GAA6B,EAAzB2L,EAAOyH,gBAAqB,CAE9B,IAAIW,OAAqB,EACrBC,OAAS,EACTC,OAAM,EACyB,WAA/BtI,EAAO8H,qBAETQ,EAAMjU,GADNgU,EAASX,KAAKC,MAAMtT,EAAIoT,IACJA,GACPS,EAATG,GAA4BA,IAAWH,GAAkBI,IAAQb,EAAkB,IAE1EA,IADXa,GAAO,KAELA,EAAM,EACND,GAAU,GAGdD,EAAqBC,EAAWC,EAAMpB,EAA0BO,EAChEU,EACG1M,IAAI,CACH8M,4BAA6BH,EAC7BI,yBAA0BJ,EAC1BK,iBAAkBL,EAClBM,gBAAiBN,EACjBO,MAAOP,KAIXC,EAAShU,GADTiU,EAAMZ,KAAKC,MAAMtT,EAAI4T,IACDA,EAEtBE,EACG1M,IACE,WAAayJ,EAAOI,eAAiB,MAAQ,QACrC,IAARgD,GAAatI,EAAO+G,cAAoB/G,EAAmB,aAAI,MAEjEzJ,KAAK,qBAAsB8R,GAC3B9R,KAAK,kBAAmB+R,GAE7B,GAA6B,SAAzBH,EAAM1M,IAAI,WAAd,CAEA,GAA6B,SAAzBuE,EAAO6H,cAA0B,CACnC,IAAIe,EAActV,EAAIM,iBAAiBuU,EAAM,GAAI,MAC7CU,EAAmBV,EAAM,GAAGlV,MAAMoE,UAClCyR,EAAyBX,EAAM,GAAGlV,MAAMsE,gBACxCsR,IACFV,EAAM,GAAGlV,MAAMoE,UAAY,QAEzByR,IACFX,EAAM,GAAGlV,MAAMsE,gBAAkB,QAGjC4P,EADEjC,EAAOI,eACG6C,EAAM,GAAGnN,wBAAwBgK,MACzCrK,WAAWiO,EAAY/U,iBAAiB,gBACxC8G,WAAWiO,EAAY/U,iBAAiB,iBAEhCsU,EAAM,GAAGnN,wBAAwBiK,OACzCtK,WAAWiO,EAAY/U,iBAAiB,eACxC8G,WAAWiO,EAAY/U,iBAAiB,kBAE1CgV,IACFV,EAAM,GAAGlV,MAAMoE,UAAYwR,GAEzBC,IACFX,EAAM,GAAGlV,MAAMsE,gBAAkBuR,GAE/B9I,EAAO+I,eAAgB5B,EAAYO,KAAKC,MAAMR,SAElDA,GAAavB,GAAe5F,EAAO6H,cAAgB,GAAKd,GAAiB/G,EAAO6H,cAC5E7H,EAAO+I,eAAgB5B,EAAYO,KAAKC,MAAMR,IAE9Cf,EAAO/R,KACL6Q,EAAOI,eACTc,EAAO/R,GAAGpB,MAAM+R,MAAQmC,EAAY,KAEpCf,EAAO/R,GAAGpB,MAAMgS,OAASkC,EAAY,MAIvCf,EAAO/R,KACT+R,EAAO/R,GAAG2U,gBAAkB7B,GAE9BX,EAAgBvR,KAAKkS,GAGjBnH,EAAOiJ,gBACTjC,EAAgBA,EAAiBG,EAAY,EAAMF,EAAgB,EAAKF,EAClD,IAAlBE,GAA6B,IAAN5S,IAAW2S,EAAgBA,EAAiBpB,EAAa,EAAKmB,GAC/E,IAAN1S,IAAW2S,EAAgBA,EAAiBpB,EAAa,EAAKmB,GAC9DW,KAAKwB,IAAIlC,GAAiB,OAAYA,EAAgB,GACtDhH,EAAO+I,eAAgB/B,EAAgBU,KAAKC,MAAMX,IAClD,EAAUhH,EAAOmJ,gBAAmB,GAAK7C,EAASrR,KAAK+R,GAC3DT,EAAWtR,KAAK+R,KAEZhH,EAAO+I,eAAgB/B,EAAgBU,KAAKC,MAAMX,IAClD,EAAUhH,EAAOmJ,gBAAmB,GAAK7C,EAASrR,KAAK+R,GAC3DT,EAAWtR,KAAK+R,GAChBA,EAAgBA,EAAgBG,EAAYJ,GAG9C7B,EAAOkC,aAAeD,EAAYJ,EAElCE,EAAgBE,EAEhBhL,GAAS,GAcX,GAZA+I,EAAOkC,YAAcM,KAAKK,IAAI7C,EAAOkC,YAAaxB,GAAce,EAI9Dd,GAAOE,IAA+B,UAAlB/F,EAAOoJ,QAAwC,cAAlBpJ,EAAOoJ,SACxDzD,EAAWlK,IAAI,CAAEuJ,MAASE,EAAOkC,YAAcpH,EAAO+G,aAAgB,OAEnE3F,EAAQW,UAAW/B,EAAOqJ,iBACzBnE,EAAOI,eAAkBK,EAAWlK,IAAI,CAAEuJ,MAASE,EAAOkC,YAAcpH,EAAO+G,aAAgB,OAC5FpB,EAAWlK,IAAI,CAAEwJ,OAAUC,EAAOkC,YAAcpH,EAAO+G,aAAgB,QAGnD,EAAzB/G,EAAOyH,kBACTvC,EAAOkC,aAAeD,EAAYnH,EAAO+G,cAAgBG,EACzDhC,EAAOkC,YAAcM,KAAKE,KAAK1C,EAAOkC,YAAcpH,EAAOyH,iBAAmBzH,EAAO+G,aACjF7B,EAAOI,eAAkBK,EAAWlK,IAAI,CAAEuJ,MAASE,EAAOkC,YAAcpH,EAAO+G,aAAgB,OAC5FpB,EAAWlK,IAAI,CAAEwJ,OAAUC,EAAOkC,YAAcpH,EAAO+G,aAAgB,OAC1E/G,EAAOiJ,gBAAgB,CACzBjB,EAAgB,GAChB,IAAK,IAAIsB,EAAM,EAAGA,EAAMhD,EAAShS,OAAQgV,GAAO,EAAG,CACjD,IAAIC,EAAiBjD,EAASgD,GAC1BtJ,EAAO+I,eAAgBQ,EAAiB7B,KAAKC,MAAM4B,IACnDjD,EAASgD,GAAOpE,EAAOkC,YAAcd,EAAS,IAAM0B,EAAc/S,KAAKsU,GAE7EjD,EAAW0B,EAKf,IAAKhI,EAAOiJ,eAAgB,CAC1BjB,EAAgB,GAChB,IAAK,IAAIwB,EAAM,EAAGA,EAAMlD,EAAShS,OAAQkV,GAAO,EAAG,CACjD,IAAIC,EAAmBnD,EAASkD,GAC5BxJ,EAAO+I,eAAgBU,EAAmB/B,KAAKC,MAAM8B,IACrDnD,EAASkD,IAAQtE,EAAOkC,YAAcxB,GACxCoC,EAAc/S,KAAKwU,GAGvBnD,EAAW0B,EACmF,EAA1FN,KAAKC,MAAMzC,EAAOkC,YAAcxB,GAAc8B,KAAKC,MAAMrB,EAASA,EAAShS,OAAS,KACtFgS,EAASrR,KAAKiQ,EAAOkC,YAAcxB,GAGf,IAApBU,EAAShS,SAAgBgS,EAAW,CAAC,IAEb,IAAxBtG,EAAO+G,eACL7B,EAAOI,eACLO,EAAOO,EAAO3K,IAAI,CAAE4L,WAAaN,EAAe,OAC7CX,EAAO3K,IAAI,CAAE8L,YAAcR,EAAe,OAC1CX,EAAO3K,IAAI,CAAE+L,aAAeT,EAAe,QAGtD1I,EAAMqC,OAAOwE,EAAQ,CACnBkB,OAAQA,EACRE,SAAUA,EACVC,WAAYA,EACZC,gBAAiBA,IAGfH,IAAiBF,GACnBjB,EAAO/B,KAAK,sBAEVmD,EAAShS,SAAWuS,IAClB3B,EAAOlF,OAAO0J,eAAiBxE,EAAOyE,gBAC1CzE,EAAO/B,KAAK,yBAEVoD,EAAWjS,SAAWwS,GACxB5B,EAAO/B,KAAK,2BAGVnD,EAAO4J,qBAAuB5J,EAAO6J,wBACvC3E,EAAO4E,uBA2RTC,iBAvRF,SAA2BC,GACzB,IAGI3V,EAHA6Q,EAASlT,KACTiY,EAAe,GACfC,EAAY,EAQhB,GANqB,iBAAVF,EACT9E,EAAOiF,cAAcH,IACF,IAAVA,GACT9E,EAAOiF,cAAcjF,EAAOlF,OAAOgK,OAGD,SAAhC9E,EAAOlF,OAAO6H,eAA0D,EAA9B3C,EAAOlF,OAAO6H,cAC1D,IAAKxT,EAAI,EAAGA,EAAIqT,KAAKE,KAAK1C,EAAOlF,OAAO6H,eAAgBxT,GAAK,EAAG,CAC9D,IAAI8H,EAAQ+I,EAAOkF,YAAc/V,EACjC,GAAI8H,EAAQ+I,EAAOkB,OAAO9R,OAAU,MACpC2V,EAAahV,KAAKiQ,EAAOkB,OAAO9J,GAAGH,GAAO,SAG5C8N,EAAahV,KAAKiQ,EAAOkB,OAAO9J,GAAG4I,EAAOkF,aAAa,IAIzD,IAAK/V,EAAI,EAAGA,EAAI4V,EAAa3V,OAAQD,GAAK,EACxC,QAA+B,IAApB4V,EAAa5V,GAAoB,CAC1C,IAAI4Q,EAASgF,EAAa5V,GAAGwG,aAC7BqP,EAAqBA,EAATjF,EAAqBA,EAASiF,EAK1CA,GAAahF,EAAOS,WAAWlK,IAAI,SAAWyO,EAAY,OA0P9DJ,mBAvPF,WAGE,IAFA,IACI1D,EADSpU,KACOoU,OACX/R,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EACtC+R,EAAO/R,GAAGgW,kBAHCrY,KAG0BsT,eAAiBc,EAAO/R,GAAGiW,WAAalE,EAAO/R,GAAGkW,WAoPzFC,qBAhPF,SAA+BC,QACV,IAAdA,IAAuBA,EAAazY,MAAQA,KAAKyY,WAAc,GAEpE,IAAIvF,EAASlT,KACTgO,EAASkF,EAAOlF,OAEhBoG,EAASlB,EAAOkB,OAChBP,EAAMX,EAAOY,aAEjB,GAAsB,IAAlBM,EAAO9R,OAAX,MAC2C,IAAhC8R,EAAO,GAAGiE,mBAAqCnF,EAAO4E,qBAEjE,IAAIY,GAAgBD,EAChB5E,IAAO6E,EAAeD,GAG1BrE,EAAOnQ,YAAY+J,EAAO2K,mBAE1B,IAAK,IAAItW,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAAG,CACzC,IAAI8T,EAAQ/B,EAAO/R,GACfuW,GACDF,GAAgB1K,EAAOiJ,eAAiB/D,EAAO2F,eAAiB,GAAM1C,EAAMkC,oBAC1ElC,EAAMa,gBAAkBhJ,EAAO+G,cACpC,GAAI/G,EAAO6J,sBAAuB,CAChC,IAAIiB,IAAgBJ,EAAevC,EAAMkC,mBACrCU,EAAaD,EAAc5F,EAAOsB,gBAAgBnS,IACtB,GAAfyW,GAAoBA,EAAc5F,EAAOO,MAC/B,EAAbsF,GAAkBA,GAAc7F,EAAOO,MACvCqF,GAAe,GAAKC,GAAc7F,EAAOO,OAErDW,EAAO9J,GAAGjI,GAAGsB,SAASqK,EAAO2K,mBAGjCxC,EAAM6C,SAAWnF,GAAO+E,EAAgBA,KAgN1CK,eA5MF,SAAyBR,QACJ,IAAdA,IAAuBA,EAAazY,MAAQA,KAAKyY,WAAc,GAEpE,IAAIvF,EAASlT,KACTgO,EAASkF,EAAOlF,OAEhBkL,EAAiBhG,EAAOiG,eAAiBjG,EAAO2F,eAChDG,EAAW9F,EAAO8F,SAClBI,EAAclG,EAAOkG,YACrBC,EAAQnG,EAAOmG,MACfC,EAAeF,EACfG,EAASF,EACU,IAAnBH,EAGFG,EADAD,IADAJ,EAAW,IAKXI,GADAJ,GAAYP,EAAYvF,EAAO2F,gBAAkB,IACvB,EAC1BQ,EAAoB,GAAZL,GAEV3M,EAAMqC,OAAOwE,EAAQ,CACnB8F,SAAUA,EACVI,YAAaA,EACbC,MAAOA,KAGLrL,EAAO4J,qBAAuB5J,EAAO6J,wBAAyB3E,EAAOsF,qBAAqBC,GAE1FW,IAAgBE,GAClBpG,EAAO/B,KAAK,yBAEVkI,IAAUE,GACZrG,EAAO/B,KAAK,oBAETmI,IAAiBF,GAAiBG,IAAWF,IAChDnG,EAAO/B,KAAK,YAGd+B,EAAO/B,KAAK,WAAY6H,IAsKxBQ,oBAnKF,WACE,IAWIC,EAXAvG,EAASlT,KAEToU,EAASlB,EAAOkB,OAChBpG,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WACpByE,EAAclF,EAAOkF,YACrBsB,EAAYxG,EAAOwG,UACnB1F,EAAYd,EAAOe,SAAWjG,EAAOiG,QAAQC,QAEjDE,EAAOnQ,YAAc+J,EAAuB,iBAAI,IAAOA,EAAqB,eAAI,IAAOA,EAAqB,eAAI,IAAOA,EAAgC,0BAAI,IAAOA,EAA8B,wBAAI,IAAOA,EAA8B,0BAIvOyL,EADEzF,EACYd,EAAOS,WAAWhI,KAAM,IAAOqC,EAAiB,WAAI,6BAAgCoK,EAAc,MAElGhE,EAAO9J,GAAG8N,IAIdzU,SAASqK,EAAO2L,kBAExB3L,EAAO4L,OAELH,EAAYtV,SAAS6J,EAAO6L,qBAC9BlG,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAiC0L,EAAY,MAC7H/V,SAASqK,EAAO8L,2BAEnBnG,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAgC0L,EAAY,MACvH/V,SAASqK,EAAO8L,4BAIvB,IAAIC,EAAYN,EAAYvO,QAAS,IAAO8C,EAAiB,YAAI1D,GAAG,GAAG3G,SAASqK,EAAOgM,gBACnFhM,EAAO4L,MAA6B,IAArBG,EAAUzX,SAC3ByX,EAAY3F,EAAO9J,GAAG,IACZ3G,SAASqK,EAAOgM,gBAG5B,IAAIC,EAAYR,EAAYnO,QAAS,IAAO0C,EAAiB,YAAI1D,GAAG,GAAG3G,SAASqK,EAAOkM,gBACnFlM,EAAO4L,MAA6B,IAArBK,EAAU3X,SAC3B2X,EAAY7F,EAAO9J,IAAI,IACb3G,SAASqK,EAAOkM,gBAExBlM,EAAO4L,OAELG,EAAU5V,SAAS6J,EAAO6L,qBAC5BlG,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAkC+L,EAAUxV,KAAK,2BAA8B,MAC/JZ,SAASqK,EAAOmM,yBAEnBxG,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAiC+L,EAAUxV,KAAK,2BAA8B,MACzJZ,SAASqK,EAAOmM,yBAEjBF,EAAU9V,SAAS6J,EAAO6L,qBAC5BlG,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAkCiM,EAAU1V,KAAK,2BAA8B,MAC/JZ,SAASqK,EAAOoM,yBAEnBzG,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAiCiM,EAAU1V,KAAK,2BAA8B,MACzJZ,SAASqK,EAAOoM,2BAoGvBC,kBA/FF,SAA4BC,GAC1B,IASIC,EATArH,EAASlT,KACTyY,EAAYvF,EAAOY,aAAeZ,EAAOuF,WAAavF,EAAOuF,UAC7DlE,EAAarB,EAAOqB,WACpBD,EAAWpB,EAAOoB,SAClBtG,EAASkF,EAAOlF,OAChBwM,EAAgBtH,EAAOkF,YACvBqC,EAAoBvH,EAAOwG,UAC3BgB,EAAoBxH,EAAOqH,UAC3BnC,EAAckC,EAElB,QAA2B,IAAhBlC,EAA6B,CACtC,IAAK,IAAI/V,EAAI,EAAGA,EAAIkS,EAAWjS,OAAQD,GAAK,OACT,IAAtBkS,EAAWlS,EAAI,GACpBoW,GAAalE,EAAWlS,IAAMoW,EAAYlE,EAAWlS,EAAI,IAAOkS,EAAWlS,EAAI,GAAKkS,EAAWlS,IAAM,EACvG+V,EAAc/V,EACLoW,GAAalE,EAAWlS,IAAMoW,EAAYlE,EAAWlS,EAAI,KAClE+V,EAAc/V,EAAI,GAEXoW,GAAalE,EAAWlS,KACjC+V,EAAc/V,GAId2L,EAAO2M,sBACLvC,EAAc,QAA4B,IAAhBA,KAA+BA,EAAc,GAS/E,IALEmC,EADiC,GAA/BjG,EAASxR,QAAQ2V,GACPnE,EAASxR,QAAQ2V,GAEjB/C,KAAKC,MAAMyC,EAAcpK,EAAOmJ,kBAE7B7C,EAAShS,SAAUiY,EAAYjG,EAAShS,OAAS,GAC9D8V,IAAgBoC,EAApB,CASA,IAAId,EAAYlG,SAASN,EAAOkB,OAAO9J,GAAG8N,GAAa7T,KAAK,4BAA8B6T,EAAa,IAEvG/L,EAAMqC,OAAOwE,EAAQ,CACnBqH,UAAWA,EACXb,UAAWA,EACXc,cAAeA,EACfpC,YAAaA,IAEflF,EAAO/B,KAAK,qBACZ+B,EAAO/B,KAAK,mBACRsJ,IAAsBf,GACxBxG,EAAO/B,KAAK,mBAEd+B,EAAO/B,KAAK,oBArBNoJ,IAAcG,IAChBxH,EAAOqH,UAAYA,EACnBrH,EAAO/B,KAAK,qBA2DhByJ,mBArCF,SAA6BvU,GAC3B,IAAI6M,EAASlT,KACTgO,EAASkF,EAAOlF,OAChBmI,EAAQ5T,EAAE8D,EAAEC,QAAQoF,QAAS,IAAOsC,EAAiB,YAAI,GACzD6M,GAAa,EACjB,GAAI1E,EACF,IAAK,IAAI9T,EAAI,EAAGA,EAAI6Q,EAAOkB,OAAO9R,OAAQD,GAAK,EACzC6Q,EAAOkB,OAAO/R,KAAO8T,IAAS0E,GAAa,GAInD,IAAI1E,IAAS0E,EAUX,OAFA3H,EAAO4H,kBAAe/T,OACtBmM,EAAO6H,kBAAehU,GARtBmM,EAAO4H,aAAe3E,EAClBjD,EAAOe,SAAWf,EAAOlF,OAAOiG,QAAQC,QAC1ChB,EAAO6H,aAAevH,SAASjR,EAAE4T,GAAO5R,KAAK,2BAA4B,IAEzE2O,EAAO6H,aAAexY,EAAE4T,GAAOhM,QAO/B6D,EAAOgN,0BAA+CjU,IAAxBmM,EAAO6H,cAA8B7H,EAAO6H,eAAiB7H,EAAOkF,aACpGlF,EAAO8H,wBAuFX,IAAIvC,EAAY,CACd7L,aAxEF,SAAuBC,QACP,IAATA,IAAkBA,EAAO7M,KAAKsT,eAAiB,IAAM,KAE1D,IAEItF,EAFShO,KAEOgO,OAChB6F,EAHS7T,KAGI8T,aACb2E,EAJSzY,KAIUyY,UACnB9E,EALS3T,KAKW2T,WAExB,GAAI3F,EAAOiN,iBACT,OAAOpH,GAAO4E,EAAYA,EAG5B,IAAIyC,EAAmB7O,EAAMO,aAAa+G,EAAW,GAAI9G,GAGzD,OAFIgH,IAAOqH,GAAoBA,GAExBA,GAAoB,GAwD3BC,aArDF,SAAuB1C,EAAW2C,GAChC,IAAIlI,EAASlT,KACT6T,EAAMX,EAAOY,aACb9F,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WACpBqF,EAAW9F,EAAO8F,SAClBqC,EAAI,EACJC,EAAI,EAGJpI,EAAOI,eACT+H,EAAIxH,GAAO4E,EAAYA,EAEvB6C,EAAI7C,EAGFzK,EAAO+I,eACTsE,EAAI3F,KAAKC,MAAM0F,GACfC,EAAI5F,KAAKC,MAAM2F,IAGZtN,EAAOiN,mBACN7L,EAAQS,aAAgB8D,EAAWtO,UAAW,eAAiBgW,EAAI,OAASC,EAAI,YAC7E3H,EAAWtO,UAAW,aAAegW,EAAI,OAASC,EAAI,QAE/DpI,EAAOqI,kBAAoBrI,EAAOuF,UAClCvF,EAAOuF,UAAYvF,EAAOI,eAAiB+H,EAAIC,EAI/C,IAAIpC,EAAiBhG,EAAOiG,eAAiBjG,EAAO2F,gBAC7B,IAAnBK,EACY,GAECT,EAAYvF,EAAO2F,gBAAkB,KAElCG,GAClB9F,EAAO+F,eAAeR,GAGxBvF,EAAO/B,KAAK,eAAgB+B,EAAOuF,UAAW2C,IAc9CvC,aAXF,WACE,OAAS7Y,KAAKsU,SAAS,IAWvB6E,aARF,WACE,OAASnZ,KAAKsU,SAAStU,KAAKsU,SAAShS,OAAS,KAoFhD,IAAIkZ,EAAe,CACjBrD,cA3EF,SAAwB1S,EAAU2V,GACnBpb,KAEN2T,WAAWnO,WAAWC,GAFhBzF,KAINmR,KAAK,gBAAiB1L,EAAU2V,IAuEvCK,gBApEF,SAA0BC,EAAcC,QAChB,IAAjBD,IAA0BA,GAAe,GAE9C,IAAIxI,EAASlT,KACToY,EAAclF,EAAOkF,YACrBpK,EAASkF,EAAOlF,OAChBwM,EAAgBtH,EAAOsH,cACvBxM,EAAO4N,YACT1I,EAAO6E,mBAGT,IAAI8D,EAAMF,EASV,GARKE,IACgCA,EAAjBrB,EAAdpC,EAAqC,OAChCA,EAAcoC,EAAuB,OACjC,SAGftH,EAAO/B,KAAK,mBAERuK,GAAgBtD,IAAgBoC,EAAe,CACjD,GAAY,UAARqB,EAEF,YADA3I,EAAO/B,KAAK,6BAGd+B,EAAO/B,KAAK,8BACA,SAAR0K,EACF3I,EAAO/B,KAAK,4BAEZ+B,EAAO/B,KAAK,8BAwChBjJ,cAnCF,SAA0BwT,EAAcC,QAChB,IAAjBD,IAA0BA,GAAe,GAE9C,IAAIxI,EAASlT,KACToY,EAAclF,EAAOkF,YACrBoC,EAAgBtH,EAAOsH,cAC3BtH,EAAO4I,WAAY,EACnB5I,EAAOiF,cAAc,GAErB,IAAI0D,EAAMF,EASV,GARKE,IACgCA,EAAjBrB,EAAdpC,EAAqC,OAChCA,EAAcoC,EAAuB,OACjC,SAGftH,EAAO/B,KAAK,iBAERuK,GAAgBtD,IAAgBoC,EAAe,CACjD,GAAY,UAARqB,EAEF,YADA3I,EAAO/B,KAAK,2BAGd+B,EAAO/B,KAAK,4BACA,SAAR0K,EACF3I,EAAO/B,KAAK,0BAEZ+B,EAAO/B,KAAK,6BA2QlB,IAAIgF,EAAQ,CACV4F,QAjQF,SAAkB5R,EAAO6N,EAAO0D,EAAcM,QAC7B,IAAV7R,IAAmBA,EAAQ,QACjB,IAAV6N,IAAmBA,EAAQhY,KAAKgO,OAAOgK,YACtB,IAAjB0D,IAA0BA,GAAe,GAE9C,IAAIxI,EAASlT,KACTic,EAAa9R,EACb8R,EAAa,IAAKA,EAAa,GAEnC,IAAIjO,EAASkF,EAAOlF,OAChBsG,EAAWpB,EAAOoB,SAClBC,EAAarB,EAAOqB,WACpBiG,EAAgBtH,EAAOsH,cACvBpC,EAAclF,EAAOkF,YACrBvE,EAAMX,EAAOY,aACjB,GAAIZ,EAAO4I,WAAa9N,EAAOkO,+BAC7B,OAAO,EAGT,IAAI3B,EAAY7E,KAAKC,MAAMsG,EAAajO,EAAOmJ,gBAC3CoD,GAAajG,EAAShS,SAAUiY,EAAYjG,EAAShS,OAAS,IAE7D8V,GAAepK,EAAOmO,cAAgB,MAAQ3B,GAAiB,IAAMkB,GACxExI,EAAO/B,KAAK,0BAGd,IAuBIwK,EAvBAlD,GAAanE,EAASiG,GAM1B,GAHArH,EAAO+F,eAAeR,GAGlBzK,EAAO2M,oBACT,IAAK,IAAItY,EAAI,EAAGA,EAAIkS,EAAWjS,OAAQD,GAAK,GACrCqT,KAAKC,MAAkB,IAAZ8C,IAAoB/C,KAAKC,MAAsB,IAAhBpB,EAAWlS,MACxD4Z,EAAa5Z,GAKnB,GAAI6Q,EAAOkJ,aAAeH,IAAe7D,EAAa,CACpD,IAAKlF,EAAOmJ,gBAAkB5D,EAAYvF,EAAOuF,WAAaA,EAAYvF,EAAO2F,eAC/E,OAAO,EAET,IAAK3F,EAAOoJ,gBAAkB7D,EAAYvF,EAAOuF,WAAaA,EAAYvF,EAAOiG,iBAC1Ef,GAAe,KAAO6D,EAAc,OAAO,EAWpD,OANgCN,EAAfvD,EAAb6D,EAAwC,OACnCA,EAAa7D,EAA2B,OAC9B,QAIdvE,IAAQ4E,IAAcvF,EAAOuF,YAAgB5E,GAAO4E,IAAcvF,EAAOuF,WAC5EvF,EAAOmH,kBAAkB4B,GAErBjO,EAAO4N,YACT1I,EAAO6E,mBAET7E,EAAOsG,sBACe,UAAlBxL,EAAOoJ,QACTlE,EAAOiI,aAAa1C,GAEJ,UAAdkD,IACFzI,EAAOuI,gBAAgBC,EAAcC,GACrCzI,EAAOhL,cAAcwT,EAAcC,KAE9B,IAGK,IAAV3D,GAAgB5I,EAAQ5J,YAS1B0N,EAAOiF,cAAcH,GACrB9E,EAAOiI,aAAa1C,GACpBvF,EAAOmH,kBAAkB4B,GACzB/I,EAAOsG,sBACPtG,EAAO/B,KAAK,wBAAyB6G,EAAOgE,GAC5C9I,EAAOuI,gBAAgBC,EAAcC,GAChCzI,EAAO4I,YACV5I,EAAO4I,WAAY,EACd5I,EAAOqJ,gCACVrJ,EAAOqJ,8BAAgC,SAAuBlW,GACvD6M,IAAUA,EAAOsJ,WAClBnW,EAAEC,SAAWtG,OACjBkT,EAAOS,WAAW,GAAGtT,oBAAoB,gBAAiB6S,EAAOqJ,+BACjErJ,EAAOS,WAAW,GAAGtT,oBAAoB,sBAAuB6S,EAAOqJ,+BACvErJ,EAAOqJ,8BAAgC,YAChCrJ,EAAOqJ,8BACdrJ,EAAOhL,cAAcwT,EAAcC,MAGvCzI,EAAOS,WAAW,GAAGvT,iBAAiB,gBAAiB8S,EAAOqJ,+BAC9DrJ,EAAOS,WAAW,GAAGvT,iBAAiB,sBAAuB8S,EAAOqJ,kCA5BtErJ,EAAOiF,cAAc,GACrBjF,EAAOiI,aAAa1C,GACpBvF,EAAOmH,kBAAkB4B,GACzB/I,EAAOsG,sBACPtG,EAAO/B,KAAK,wBAAyB6G,EAAOgE,GAC5C9I,EAAOuI,gBAAgBC,EAAcC,GACrCzI,EAAOhL,cAAcwT,EAAcC,KA0B9B,IAwJPc,YArJF,SAAsBtS,EAAO6N,EAAO0D,EAAcM,QACjC,IAAV7R,IAAmBA,EAAQ,QACjB,IAAV6N,IAAmBA,EAAQhY,KAAKgO,OAAOgK,YACtB,IAAjB0D,IAA0BA,GAAe,GAE9C,IACIgB,EAAWvS,EAKf,OANanK,KAEFgO,OAAO4L,OAChB8C,GAHW1c,KAGQ2c,cAHR3c,KAMC+b,QAAQW,EAAU1E,EAAO0D,EAAcM,IA2IrDY,UAvIF,SAAoB5E,EAAO0D,EAAcM,QACxB,IAAVhE,IAAmBA,EAAQhY,KAAKgO,OAAOgK,YACtB,IAAjB0D,IAA0BA,GAAe,GAE9C,IAAIxI,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB8N,EAAY5I,EAAO4I,UACvB,OAAI9N,EAAO4L,MACLkC,IACJ5I,EAAO2J,UAEP3J,EAAO4J,YAAc5J,EAAOS,WAAW,GAAGzK,WACnCgK,EAAO6I,QAAQ7I,EAAOkF,YAAcpK,EAAOmJ,eAAgBa,EAAO0D,EAAcM,IAElF9I,EAAO6I,QAAQ7I,EAAOkF,YAAcpK,EAAOmJ,eAAgBa,EAAO0D,EAAcM,IA0HvFe,UAtHF,SAAoB/E,EAAO0D,EAAcM,QACxB,IAAVhE,IAAmBA,EAAQhY,KAAKgO,OAAOgK,YACtB,IAAjB0D,IAA0BA,GAAe,GAE9C,IAAIxI,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB8N,EAAY5I,EAAO4I,UACnBxH,EAAWpB,EAAOoB,SAClBC,EAAarB,EAAOqB,WACpBT,EAAeZ,EAAOY,aAE1B,GAAI9F,EAAO4L,KAAM,CACf,GAAIkC,EAAa,OAAO,EACxB5I,EAAO2J,UAEP3J,EAAO4J,YAAc5J,EAAOS,WAAW,GAAGzK,WAG5C,SAAS8T,EAAUC,GACjB,OAAIA,EAAM,GAAavH,KAAKC,MAAMD,KAAKwB,IAAI+F,IACpCvH,KAAKC,MAAMsH,GAEpB,IAMIC,EANAC,EAAsBH,EALVlJ,EAAeZ,EAAOuF,WAAavF,EAAOuF,WAMtD2E,EAAqB9I,EAASnH,IAAI,SAAU8P,GAAO,OAAOD,EAAUC,KAIpEI,GAHuB9I,EAAWpH,IAAI,SAAU8P,GAAO,OAAOD,EAAUC,KAE1D3I,EAAS8I,EAAmBta,QAAQqa,IACvC7I,EAAS8I,EAAmBta,QAAQqa,GAAuB,IAM1E,YAJwB,IAAbE,IACTH,EAAY3I,EAAWzR,QAAQua,IACf,IAAKH,EAAYhK,EAAOkF,YAAc,GAEjDlF,EAAO6I,QAAQmB,EAAWlF,EAAO0D,EAAcM,IAsFtDsB,WAlFF,SAAqBtF,EAAO0D,EAAcM,GAKxC,YAJe,IAAVhE,IAAmBA,EAAQhY,KAAKgO,OAAOgK,YACtB,IAAjB0D,IAA0BA,GAAe,GAEjC1b,KACC+b,QADD/b,KACgBoY,YAAaJ,EAAO0D,EAAcM,IA8E/DuB,eA1EF,SAAyBvF,EAAO0D,EAAcM,QAC7B,IAAVhE,IAAmBA,EAAQhY,KAAKgO,OAAOgK,YACtB,IAAjB0D,IAA0BA,GAAe,GAE9C,IAAIxI,EAASlT,KACTmK,EAAQ+I,EAAOkF,YACfmC,EAAY7E,KAAKC,MAAMxL,EAAQ+I,EAAOlF,OAAOmJ,gBAEjD,GAAIoD,EAAYrH,EAAOoB,SAAShS,OAAS,EAAG,CAC1C,IAAImW,EAAYvF,EAAOY,aAAeZ,EAAOuF,WAAavF,EAAOuF,UAE7D+E,EAActK,EAAOoB,SAASiG,IACnBrH,EAAOoB,SAASiG,EAAY,GAECiD,GAAe,EAAtD/E,EAAY+E,IACfrT,EAAQ+I,EAAOlF,OAAOmJ,gBAI1B,OAAOjE,EAAO6I,QAAQ5R,EAAO6N,EAAO0D,EAAcM,IAwDlDhB,oBArDF,WACE,IAMItB,EANAxG,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WAEpBkC,EAAyC,SAAzB7H,EAAO6H,cAA2B3C,EAAOuK,uBAAyBzP,EAAO6H,cACzF6H,EAAexK,EAAO6H,aAE1B,GAAI/M,EAAO4L,KAAM,CACf,GAAI1G,EAAO4I,UAAa,OACxBpC,EAAYlG,SAASjR,EAAE2Q,EAAO4H,cAAcvW,KAAK,2BAA4B,IACzEyJ,EAAOiJ,eAENyG,EAAexK,EAAOyJ,aAAgB9G,EAAgB,GACnD6H,EAAgBxK,EAAOkB,OAAO9R,OAAS4Q,EAAOyJ,aAAiB9G,EAAgB,GAEnF3C,EAAO2J,UACPa,EAAe/J,EACZ5S,SAAU,IAAOiN,EAAiB,WAAI,6BAAgC0L,EAAY,WAAe1L,EAA0B,oBAAI,KAC/H1D,GAAG,GACHH,QAEHkC,EAAMI,SAAS,WACbyG,EAAO6I,QAAQ2B,MAGjBxK,EAAO6I,QAAQ2B,GAERA,EAAexK,EAAOkB,OAAO9R,OAASuT,GAC/C3C,EAAO2J,UACPa,EAAe/J,EACZ5S,SAAU,IAAOiN,EAAiB,WAAI,6BAAgC0L,EAAY,WAAe1L,EAA0B,oBAAI,KAC/H1D,GAAG,GACHH,QAEHkC,EAAMI,SAAS,WACbyG,EAAO6I,QAAQ2B,MAGjBxK,EAAO6I,QAAQ2B,QAGjBxK,EAAO6I,QAAQ2B,KA0GnB,IAAI9D,EAAO,CACT+D,WA7FF,WACE,IAAIzK,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WAExBA,EAAW5S,SAAU,IAAOiN,EAAiB,WAAI,IAAOA,EAA0B,qBAAI9J,SAEtF,IAAIkQ,EAAST,EAAW5S,SAAU,IAAOiN,EAAiB,YAE1D,GAAIA,EAAO4P,uBAAwB,CACjC,IAAIC,EAAiB7P,EAAOmJ,eAAkB/C,EAAO9R,OAAS0L,EAAOmJ,eACrE,GAAI0G,IAAmB7P,EAAOmJ,eAAgB,CAC5C,IAAK,IAAI9U,EAAI,EAAGA,EAAIwb,EAAgBxb,GAAK,EAAG,CAC1C,IAAIyb,EAAYvb,EAAEtC,EAAIa,cAAc,QAAQ6C,SAAWqK,EAAiB,WAAI,IAAOA,EAAsB,iBACzG2F,EAAWnJ,OAAOsT,GAEpB1J,EAAST,EAAW5S,SAAU,IAAOiN,EAAiB,aAI7B,SAAzBA,EAAO6H,eAA6B7H,EAAO2O,eAAgB3O,EAAO2O,aAAevI,EAAO9R,QAE5F4Q,EAAOyJ,aAAenJ,SAASxF,EAAO2O,cAAgB3O,EAAO6H,cAAe,IAC5E3C,EAAOyJ,cAAgB3O,EAAO+P,qBAC1B7K,EAAOyJ,aAAevI,EAAO9R,SAC/B4Q,EAAOyJ,aAAevI,EAAO9R,QAG/B,IAAI0b,EAAgB,GAChBC,EAAe,GACnB7J,EAAOxK,KAAK,SAAUO,EAAOjF,GAC3B,IAAIiR,EAAQ5T,EAAE2C,GACViF,EAAQ+I,EAAOyJ,cAAgBsB,EAAahb,KAAKiC,GACjDiF,EAAQiK,EAAO9R,QAAU6H,GAASiK,EAAO9R,OAAS4Q,EAAOyJ,cAAgBqB,EAAc/a,KAAKiC,GAChGiR,EAAM5R,KAAK,0BAA2B4F,KAExC,IAAK,IAAImN,EAAM,EAAGA,EAAM2G,EAAa3b,OAAQgV,GAAO,EAClD3D,EAAWnJ,OAAOjI,EAAE0b,EAAa3G,GAAK4G,WAAU,IAAOva,SAASqK,EAAO6L,sBAEzE,IAAK,IAAIrC,EAAMwG,EAAc1b,OAAS,EAAU,GAAPkV,EAAUA,GAAO,EACxD7D,EAAW9I,QAAQtI,EAAEyb,EAAcxG,GAAK0G,WAAU,IAAOva,SAASqK,EAAO6L,uBAsD3EgD,QAlDF,WACE,IASIH,EATAxJ,EAASlT,KACTgO,EAASkF,EAAOlF,OAChBoK,EAAclF,EAAOkF,YACrBhE,EAASlB,EAAOkB,OAChBuI,EAAezJ,EAAOyJ,aACtBL,EAAiBpJ,EAAOoJ,eACxBD,EAAiBnJ,EAAOmJ,eACxB/H,EAAWpB,EAAOoB,SAClBT,EAAMX,EAAOY,aAEjBZ,EAAOoJ,gBAAiB,EACxBpJ,EAAOmJ,gBAAiB,EAExB,IACI8B,GADiB7J,EAAS8D,GACHlF,EAAOtG,eAI9BwL,EAAcuE,GAChBD,EAAYtI,EAAO9R,OAAyB,EAAfqa,EAAqBvE,EAClDsE,GAAYC,EACOzJ,EAAO6I,QAAQW,EAAU,GAAG,GAAO,IACzB,IAATyB,GAClBjL,EAAOiI,cAActH,GAAOX,EAAOuF,UAAYvF,EAAOuF,WAAa0F,KAElC,SAAzBnQ,EAAO6H,eAA0D,EAAf8G,GAAfvE,GAAqCA,GAAehE,EAAO9R,OAASqa,KAEjHD,GAAYtI,EAAO9R,OAAS8V,EAAcuE,EAC1CD,GAAYC,EACSzJ,EAAO6I,QAAQW,EAAU,GAAG,GAAO,IACzB,IAATyB,GACpBjL,EAAOiI,cAActH,GAAOX,EAAOuF,UAAYvF,EAAOuF,WAAa0F,IAGvEjL,EAAOoJ,eAAiBA,EACxBpJ,EAAOmJ,eAAiBA,GAexB+B,YAZF,WACE,IACIzK,EADS3T,KACW2T,WACpB3F,EAFShO,KAEOgO,OAChBoG,EAHSpU,KAGOoU,OACpBT,EAAW5S,SAAU,IAAOiN,EAAiB,WAAI,IAAOA,EAA0B,qBAAI9J,SACtFkQ,EAAOtP,WAAW,6BAyBpB,IAAIuZ,EAAa,CACfC,cAjBF,SAAwBC,GAEtB,KAAInP,EAAQC,QADCrP,KACgBgO,OAAOwQ,eADvBxe,KACgDgO,OAAO0J,eADvD1X,KAC+Eye,UAA5F,CACA,IAAIvZ,EAFSlF,KAEGkF,GAChBA,EAAGjE,MAAMyd,OAAS,OAClBxZ,EAAGjE,MAAMyd,OAASH,EAAS,mBAAqB,eAChDrZ,EAAGjE,MAAMyd,OAASH,EAAS,eAAiB,YAC5CrZ,EAAGjE,MAAMyd,OAASH,EAAS,WAAa,SAWxCI,gBARF,WAEMvP,EAAQC,OADCrP,KACgBgO,OAAO0J,eADvB1X,KAC+Cye,WAD/Cze,KAENkF,GAAGjE,MAAMyd,OAAS,MAqK3B,IAAIE,EAAe,CACjBC,YA9JF,SAAsBzK,GACpB,IAAIlB,EAASlT,KACT2T,EAAaT,EAAOS,WACpB3F,EAASkF,EAAOlF,OAIpB,GAHIA,EAAO4L,MACT1G,EAAOkL,cAEa,iBAAXhK,GAAuB,WAAYA,EAC5C,IAAK,IAAI/R,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAClC+R,EAAO/R,IAAMsR,EAAWnJ,OAAO4J,EAAO/R,SAG5CsR,EAAWnJ,OAAO4J,GAEhBpG,EAAO4L,MACT1G,EAAOyK,aAEH3P,EAAOgC,UAAYZ,EAAQY,UAC/BkD,EAAOJ,UA6ITgM,aAzIF,SAAuB1K,GACrB,IAAIlB,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WACpByE,EAAclF,EAAOkF,YAErBpK,EAAO4L,MACT1G,EAAOkL,cAET,IAAI9D,EAAiBlC,EAAc,EACnC,GAAsB,iBAAXhE,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAI/R,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAClC+R,EAAO/R,IAAMsR,EAAW9I,QAAQuJ,EAAO/R,IAE7CiY,EAAiBlC,EAAchE,EAAO9R,YAEtCqR,EAAW9I,QAAQuJ,GAEjBpG,EAAO4L,MACT1G,EAAOyK,aAEH3P,EAAOgC,UAAYZ,EAAQY,UAC/BkD,EAAOJ,SAETI,EAAO6I,QAAQzB,EAAgB,GAAG,IAkHlCyE,SA/GF,SAAmB5U,EAAOiK,GACxB,IAAIlB,EAASlT,KACT2T,EAAaT,EAAOS,WACpB3F,EAASkF,EAAOlF,OAEhBgR,EADc9L,EAAOkF,YAErBpK,EAAO4L,OACToF,GAAqB9L,EAAOyJ,aAC5BzJ,EAAOkL,cACPlL,EAAOkB,OAAST,EAAW5S,SAAU,IAAOiN,EAAiB,aAE/D,IAAIiR,EAAa/L,EAAOkB,OAAO9R,OAC/B,GAAI6H,GAAS,EACX+I,EAAO4L,aAAa1K,QAGtB,GAAa6K,GAAT9U,EACF+I,EAAO2L,YAAYzK,OADrB,CAOA,IAHA,IAAIkG,EAAqCnQ,EAApB6U,EAA4BA,EAAoB,EAAIA,EAErEE,EAAe,GACV7c,EAAI4c,EAAa,EAAQ9U,GAAL9H,EAAYA,GAAK,EAAG,CAC/C,IAAI8c,EAAejM,EAAOkB,OAAO9J,GAAGjI,GACpC8c,EAAajb,SACbgb,EAAazY,QAAQ0Y,GAGvB,GAAsB,iBAAX/K,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAIkD,EAAM,EAAGA,EAAMlD,EAAO9R,OAAQgV,GAAO,EACxClD,EAAOkD,IAAQ3D,EAAWnJ,OAAO4J,EAAOkD,IAE9CgD,EAAqCnQ,EAApB6U,EAA4BA,EAAoB5K,EAAO9R,OAAS0c,OAEjFrL,EAAWnJ,OAAO4J,GAGpB,IAAK,IAAIoD,EAAM,EAAGA,EAAM0H,EAAa5c,OAAQkV,GAAO,EAClD7D,EAAWnJ,OAAO0U,EAAa1H,IAG7BxJ,EAAO4L,MACT1G,EAAOyK,aAEH3P,EAAOgC,UAAYZ,EAAQY,UAC/BkD,EAAOJ,SAEL9E,EAAO4L,KACT1G,EAAO6I,QAAQzB,EAAiBpH,EAAOyJ,aAAc,GAAG,GAExDzJ,EAAO6I,QAAQzB,EAAgB,GAAG,KA6DpC8E,YAzDF,SAAsBC,GACpB,IAAInM,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WAGpBqL,EAFc9L,EAAOkF,YAGrBpK,EAAO4L,OACToF,GAAqB9L,EAAOyJ,aAC5BzJ,EAAOkL,cACPlL,EAAOkB,OAAST,EAAW5S,SAAU,IAAOiN,EAAiB,aAE/D,IACIsR,EADAhF,EAAiB0E,EAGrB,GAA6B,iBAAlBK,GAA8B,WAAYA,EAAe,CAClE,IAAK,IAAIhd,EAAI,EAAGA,EAAIgd,EAAc/c,OAAQD,GAAK,EAC7Cid,EAAgBD,EAAchd,GAC1B6Q,EAAOkB,OAAOkL,IAAkBpM,EAAOkB,OAAO9J,GAAGgV,GAAepb,SAChEob,EAAgBhF,IAAkBA,GAAkB,GAE1DA,EAAiB5E,KAAKK,IAAIuE,EAAgB,QAE1CgF,EAAgBD,EACZnM,EAAOkB,OAAOkL,IAAkBpM,EAAOkB,OAAO9J,GAAGgV,GAAepb,SAChEob,EAAgBhF,IAAkBA,GAAkB,GACxDA,EAAiB5E,KAAKK,IAAIuE,EAAgB,GAGxCtM,EAAO4L,MACT1G,EAAOyK,aAGH3P,EAAOgC,UAAYZ,EAAQY,UAC/BkD,EAAOJ,SAEL9E,EAAO4L,KACT1G,EAAO6I,QAAQzB,EAAiBpH,EAAOyJ,aAAc,GAAG,GAExDzJ,EAAO6I,QAAQzB,EAAgB,GAAG,IAmBpCiF,gBAfF,WAIE,IAHA,IAEIF,EAAgB,GACXhd,EAAI,EAAGA,EAHHrC,KAGcoU,OAAO9R,OAAQD,GAAK,EAC7Cgd,EAAcpc,KAAKZ,GAJRrC,KAMNof,YAAYC,KAWjBG,EAAU,WACZ,IAAIC,EAAKne,EAAIE,UAAUC,UAEnBie,EAAS,CACXC,KAAK,EACLC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,QAAS7e,EAAI6e,SAAW7e,EAAI8e,SAC5BA,SAAU9e,EAAI6e,SAAW7e,EAAI8e,UAG3BL,EAAUN,EAAGvc,MAAM,qCACnB0c,EAAUH,EAAGvc,MAAM,+BACnBgd,EAAOT,EAAGvc,MAAM,wBAChB+c,EAAOR,EAAGvc,MAAM,2BAChB8c,GAAUE,GAAQT,EAAGvc,MAAM,8BA+C/B,GA3CI6c,IACFL,EAAOW,GAAK,UACZX,EAAOY,UAAYP,EAAQ,GAC3BL,EAAOK,SAAU,GAGfH,IAAYG,IACdL,EAAOW,GAAK,UACZX,EAAOY,UAAYV,EAAQ,GAC3BF,EAAOE,SAAU,EACjBF,EAAOG,cAAsD,GAAtCJ,EAAGc,cAAczd,QAAQ,YAE9Cod,GAAQF,GAAUC,KACpBP,EAAOW,GAAK,MACZX,EAAOC,KAAM,GAGXK,IAAWC,IACbP,EAAOY,UAAYN,EAAO,GAAG3S,QAAQ,KAAM,KAC3CqS,EAAOM,QAAS,GAEdE,IACFR,EAAOY,UAAYJ,EAAK,GAAG7S,QAAQ,KAAM,KACzCqS,EAAOQ,MAAO,GAEZD,IACFP,EAAOY,UAAYL,EAAK,GAAKA,EAAK,GAAG5S,QAAQ,KAAM,KAAO,KAC1DqS,EAAOM,QAAS,GAGdN,EAAOC,KAAOD,EAAOY,WAAuC,GAA1Bb,EAAG3c,QAAQ,aACR,OAAnC4c,EAAOY,UAAUnd,MAAM,KAAK,KAC9Buc,EAAOY,UAAYb,EAAGc,cAAcpd,MAAM,YAAY,GAAGA,MAAM,KAAK,IAKxEuc,EAAOI,UAAYJ,EAAOW,IAAMX,EAAOE,SAAWF,EAAOc,SAGzDd,EAAOc,SAAWR,GAAUE,GAAQD,IAASR,EAAGvc,MAAM,8BAGlDwc,EAAOW,IAAoB,QAAdX,EAAOW,GAAc,CACpC,IAAII,EAAef,EAAOY,UAAUnd,MAAM,KACtCud,EAAezgB,EAAIQ,cAAc,yBACrCif,EAAOiB,WAAajB,EAAOc,UACrBP,GAAQD,KACU,EAAlBS,EAAa,IAAW,EAA2B,GAAL,EAAlBA,EAAa,GAAoC,EAAJ,EAAlBA,EAAa,KACrEC,GAA8E,GAA9DA,EAAa9b,aAAa,WAAW9B,QAAQ,cAOpE,OAHA4c,EAAOkB,WAAatf,EAAIuf,kBAAoB,EAGrCnB,EAhFI,GAmnBb,SAASoB,IACP,IAAI5N,EAASlT,KAETgO,EAASkF,EAAOlF,OAChB9I,EAAKgO,EAAOhO,GAEhB,IAAIA,GAAyB,IAAnBA,EAAGwD,YAAb,CAGIsF,EAAO+S,aACT7N,EAAO8N,gBAIT,IAAI3E,EAAiBnJ,EAAOmJ,eACxBC,EAAiBpJ,EAAOoJ,eACxBhI,EAAWpB,EAAOoB,SAStB,GANApB,EAAOmJ,gBAAiB,EACxBnJ,EAAOoJ,gBAAiB,EAExBpJ,EAAOH,aACPG,EAAOQ,eAEH1F,EAAOiT,SAAU,CACnB,IAAIC,EAAexL,KAAKyL,IAAIzL,KAAKK,IAAI7C,EAAOuF,UAAWvF,EAAOiG,gBAAiBjG,EAAO2F,gBACtF3F,EAAOiI,aAAa+F,GACpBhO,EAAOmH,oBACPnH,EAAOsG,sBAEHxL,EAAO4N,YACT1I,EAAO6E,wBAGT7E,EAAOsG,uBACuB,SAAzBxL,EAAO6H,eAAmD,EAAvB7H,EAAO6H,gBAAsB3C,EAAOmG,QAAUnG,EAAOlF,OAAOiJ,eAClG/D,EAAO6I,QAAQ7I,EAAOkB,OAAO9R,OAAS,EAAG,GAAG,GAAO,GAEnD4Q,EAAO6I,QAAQ7I,EAAOkF,YAAa,GAAG,GAAO,GAIjDlF,EAAOoJ,eAAiBA,EACxBpJ,EAAOmJ,eAAiBA,EAEpBnJ,EAAOlF,OAAO0J,eAAiBpD,IAAapB,EAAOoB,UACrDpB,EAAOyE,iBAsGX,IAAI3Q,EAAS,CACXoa,aAxFF,WACE,IAAIlO,EAASlT,KACTgO,EAASkF,EAAOlF,OAChBqT,EAAcnO,EAAOmO,YACrBnc,EAAKgO,EAAOhO,GACZoc,EAAYpO,EAAOoO,UAGrBpO,EAAOqO,aAtmBX,SAAuBna,GACrB,IAAI8L,EAASlT,KACTgF,EAAOkO,EAAOsO,gBACdxT,EAASkF,EAAOlF,OAChByT,EAAUvO,EAAOuO,QACrB,IAAIvO,EAAO4I,YAAa9N,EAAOkO,+BAA/B,CAGA,IAAI7V,EAAIe,EAGR,GAFIf,EAAEqb,gBAAiBrb,EAAIA,EAAEqb,eAC7B1c,EAAK2c,aAA0B,eAAXtb,EAAEub,MACjB5c,EAAK2c,gBAAgB,UAAWtb,IAAiB,IAAZA,EAAEwb,UACxC7c,EAAK8c,YAAa9c,EAAK+c,SAC3B,GAAI/T,EAAOgU,WAAazf,EAAE8D,EAAEC,QAAQoF,QAAQsC,EAAOiU,kBAAoBjU,EAAOiU,kBAAqB,IAAOjU,EAAqB,gBAAI,GACjIkF,EAAOgP,YAAa,OAGtB,IAAIlU,EAAOmU,cACJ5f,EAAE8D,GAAGqF,QAAQsC,EAAOmU,cAAc,GADzC,CAIAV,EAAQW,SAAsB,eAAX/b,EAAEub,KAAwBvb,EAAEgc,cAAc,GAAGC,MAAQjc,EAAEic,MAC1Eb,EAAQc,SAAsB,eAAXlc,EAAEub,KAAwBvb,EAAEgc,cAAc,GAAGG,MAAQnc,EAAEmc,MAC1E,IAAIC,EAAShB,EAAQW,SACjBM,EAASjB,EAAQc,SAIjBI,EAAqB3U,EAAO2U,oBAAsB3U,EAAO4U,sBACzDC,EAAqB7U,EAAO6U,oBAAsB7U,EAAO8U,sBAC7D,IACEH,KACKF,GAAUI,GACXJ,GAAUnhB,EAAIU,OAAOgR,MAAQ6P,GAHnC,CAuBA,GAfAxW,EAAMqC,OAAO1J,EAAM,CACjB8c,WAAW,EACXC,SAAS,EACTgB,qBAAqB,EACrBC,iBAAajc,EACbkc,iBAAalc,IAGf0a,EAAQgB,OAASA,EACjBhB,EAAQiB,OAASA,EACjB1d,EAAKke,eAAiB7W,EAAMM,MAC5BuG,EAAOgP,YAAa,EACpBhP,EAAOH,aACPG,EAAOiQ,oBAAiBpc,EACD,EAAnBiH,EAAOoV,YAAiBpe,EAAKqe,oBAAqB,GACvC,eAAXhd,EAAEub,KAAuB,CAC3B,IAAI0B,GAAiB,EACjB/gB,EAAE8D,EAAEC,QAAQI,GAAG1B,EAAKue,gBAAiBD,GAAiB,GAExDrjB,EAAIK,eACDiC,EAAEtC,EAAIK,eAAeoG,GAAG1B,EAAKue,eAC7BtjB,EAAIK,gBAAkB+F,EAAEC,QAE3BrG,EAAIK,cAAcC,OAEhB+iB,GAAkBpQ,EAAOsQ,gBAC3Bnd,EAAEid,iBAGNpQ,EAAO/B,KAAK,aAAc9K,OAmiBW4L,KAAKiB,GACxCA,EAAOuQ,YAjiBX,SAAsBrc,GACpB,IAAI8L,EAASlT,KACTgF,EAAOkO,EAAOsO,gBACdxT,EAASkF,EAAOlF,OAChByT,EAAUvO,EAAOuO,QACjB5N,EAAMX,EAAOY,aACbzN,EAAIe,EAER,GADIf,EAAEqb,gBAAiBrb,EAAIA,EAAEqb,eACxB1c,EAAK8c,WAMV,IAAI9c,EAAK2c,cAA2B,cAAXtb,EAAEub,KAA3B,CACA,IAAIU,EAAmB,cAAXjc,EAAEub,KAAuBvb,EAAEgc,cAAc,GAAGC,MAAQjc,EAAEic,MAC9DE,EAAmB,cAAXnc,EAAEub,KAAuBvb,EAAEgc,cAAc,GAAGG,MAAQnc,EAAEmc,MAClE,GAAInc,EAAEqd,wBAGJ,OAFAjC,EAAQgB,OAASH,OACjBb,EAAQiB,OAASF,GAGnB,IAAKtP,EAAOsQ,eAYV,OAVAtQ,EAAOgP,YAAa,OAChBld,EAAK8c,YACPzV,EAAMqC,OAAO+S,EAAS,CACpBgB,OAAQH,EACRI,OAAQF,EACRJ,SAAUE,EACVC,SAAUC,IAEZxd,EAAKke,eAAiB7W,EAAMM,QAIhC,GAAI3H,EAAK2c,cAAgB3T,EAAO2V,sBAAwB3V,EAAO4L,KAC7D,GAAI1G,EAAOK,cAET,GACGiP,EAAQf,EAAQiB,QAAUxP,EAAOuF,WAAavF,EAAOiG,gBAClDqJ,EAAQf,EAAQiB,QAAUxP,EAAOuF,WAAavF,EAAO2F,eAIzD,OAFA7T,EAAK8c,WAAY,OACjB9c,EAAK+c,SAAU,QAGZ,GACJO,EAAQb,EAAQgB,QAAUvP,EAAOuF,WAAavF,EAAOiG,gBAClDmJ,EAAQb,EAAQgB,QAAUvP,EAAOuF,WAAavF,EAAO2F,eAEzD,OAGJ,GAAI7T,EAAK2c,cAAgB1hB,EAAIK,eACvB+F,EAAEC,SAAWrG,EAAIK,eAAiBiC,EAAE8D,EAAEC,QAAQI,GAAG1B,EAAKue,cAGxD,OAFAve,EAAK+c,SAAU,OACf7O,EAAOgP,YAAa,GAOxB,GAHIld,EAAK+d,qBACP7P,EAAO/B,KAAK,YAAa9K,KAEvBA,EAAEgc,eAA0C,EAAzBhc,EAAEgc,cAAc/f,QAAvC,CAEAmf,EAAQW,SAAWE,EACnBb,EAAQc,SAAWC,EAEnB,IAKMoB,EALFC,EAAQpC,EAAQW,SAAWX,EAAQgB,OACnCqB,EAAQrC,EAAQc,SAAWd,EAAQiB,OACvC,KAAIxP,EAAOlF,OAAOoV,WAAa1N,KAAKqO,KAAMrO,KAAKsO,IAAKH,EAAO,GAAQnO,KAAKsO,IAAKF,EAAO,IAAQ5Q,EAAOlF,OAAOoV,WAsB1G,QApBgC,IAArBpe,EAAKge,cAET9P,EAAOI,gBAAkBmO,EAAQc,WAAad,EAAQiB,QAAYxP,EAAOK,cAAgBkO,EAAQW,WAAaX,EAAQgB,OACzHzd,EAAKge,aAAc,EAGsB,IAApCa,EAAQA,EAAUC,EAAQA,IAC7BF,EAA6D,IAA/ClO,KAAKuO,MAAMvO,KAAKwB,IAAI4M,GAAQpO,KAAKwB,IAAI2M,IAAiBnO,KAAKwO,GACzElf,EAAKge,YAAc9P,EAAOI,eAAiBsQ,EAAa5V,EAAO4V,WAAc,GAAKA,EAAa5V,EAAO4V,aAIxG5e,EAAKge,aACP9P,EAAO/B,KAAK,oBAAqB9K,QAEH,IAArBrB,EAAKie,cACVxB,EAAQW,WAAaX,EAAQgB,QAAUhB,EAAQc,WAAad,EAAQiB,SACtE1d,EAAKie,aAAc,IAGnBje,EAAKge,YACPhe,EAAK8c,WAAY,OAGnB,GAAK9c,EAAKie,YAAV,CAGA/P,EAAOgP,YAAa,EACpB7b,EAAEid,iBACEtV,EAAOmW,2BAA6BnW,EAAOoW,QAC7C/d,EAAEge,kBAGCrf,EAAK+c,UACJ/T,EAAO4L,MACT1G,EAAO2J,UAET7X,EAAKsf,eAAiBpR,EAAOtG,eAC7BsG,EAAOiF,cAAc,GACjBjF,EAAO4I,WACT5I,EAAOS,WAAWjM,QAAQ,qCAE5B1C,EAAKuf,qBAAsB,GAEvBvW,EAAOqQ,aAAyC,IAA1BnL,EAAOmJ,iBAAqD,IAA1BnJ,EAAOoJ,gBACjEpJ,EAAOoL,eAAc,GAEvBpL,EAAO/B,KAAK,kBAAmB9K,IAEjC6M,EAAO/B,KAAK,aAAc9K,GAC1BrB,EAAK+c,SAAU,EAEf,IAAI5D,EAAOjL,EAAOI,eAAiBuQ,EAAQC,EAC3CrC,EAAQtD,KAAOA,EAEfA,GAAQnQ,EAAOwW,WACX3Q,IAAOsK,GAAQA,GAEnBjL,EAAOiQ,eAAwB,EAAPhF,EAAW,OAAS,OAC5CnZ,EAAKkW,iBAAmBiD,EAAOnZ,EAAKsf,eAEpC,IAAIG,GAAsB,EACtBC,EAAkB1W,EAAO0W,gBA0B7B,GAzBI1W,EAAO2V,sBACTe,EAAkB,GAER,EAAPvG,GAAYnZ,EAAKkW,iBAAmBhI,EAAO2F,gBAC9C4L,GAAsB,EAClBzW,EAAO2W,aAAc3f,EAAKkW,iBAAoBhI,EAAO2F,eAAiB,EAAMnD,KAAKsO,KAAO9Q,EAAO2F,eAAiB7T,EAAKsf,eAAiBnG,EAAOuG,KACxIvG,EAAO,GAAKnZ,EAAKkW,iBAAmBhI,EAAOiG,iBACpDsL,GAAsB,EAClBzW,EAAO2W,aAAc3f,EAAKkW,iBAAoBhI,EAAOiG,eAAiB,EAAMzD,KAAKsO,IAAM9Q,EAAOiG,eAAiBnU,EAAKsf,eAAiBnG,EAAOuG,KAG9ID,IACFpe,EAAEqd,yBAA0B,IAIzBxQ,EAAOmJ,gBAA4C,SAA1BnJ,EAAOiQ,gBAA6Bne,EAAKkW,iBAAmBlW,EAAKsf,iBAC7Ftf,EAAKkW,iBAAmBlW,EAAKsf,iBAE1BpR,EAAOoJ,gBAA4C,SAA1BpJ,EAAOiQ,gBAA6Bne,EAAKkW,iBAAmBlW,EAAKsf,iBAC7Ftf,EAAKkW,iBAAmBlW,EAAKsf,gBAKR,EAAnBtW,EAAOoV,UAAe,CACxB,KAAI1N,KAAKwB,IAAIiH,GAAQnQ,EAAOoV,WAAape,EAAKqe,oBAW5C,YADAre,EAAKkW,iBAAmBlW,EAAKsf,gBAT7B,IAAKtf,EAAKqe,mBAMR,OALAre,EAAKqe,oBAAqB,EAC1B5B,EAAQgB,OAAShB,EAAQW,SACzBX,EAAQiB,OAASjB,EAAQc,SACzBvd,EAAKkW,iBAAmBlW,EAAKsf,oBAC7B7C,EAAQtD,KAAOjL,EAAOI,eAAiBmO,EAAQW,SAAWX,EAAQgB,OAAShB,EAAQc,SAAWd,EAAQiB,QASvG1U,EAAO4W,gBAGR5W,EAAOiT,UAAYjT,EAAO4J,qBAAuB5J,EAAO6J,yBAC1D3E,EAAOmH,oBACPnH,EAAOsG,uBAELxL,EAAOiT,WAEsB,IAA3Bjc,EAAK6f,WAAWviB,QAClB0C,EAAK6f,WAAW5hB,KAAK,CACnB6hB,SAAUrD,EAAQvO,EAAOI,eAAiB,SAAW,UACrDyR,KAAM/f,EAAKke,iBAGfle,EAAK6f,WAAW5hB,KAAK,CACnB6hB,SAAUrD,EAAQvO,EAAOI,eAAiB,WAAa,YACvDyR,KAAM1Y,EAAMM,SAIhBuG,EAAO+F,eAAejU,EAAKkW,kBAE3BhI,EAAOiI,aAAanW,EAAKkW,2BA/LnBlW,EAAKie,aAAeje,EAAKge,aAC3B9P,EAAO/B,KAAK,oBAAqB9K,IAuhBF4L,KAAKiB,GACtCA,EAAO8R,WAvVX,SAAqB5d,GACnB,IAAI8L,EAASlT,KACTgF,EAAOkO,EAAOsO,gBAEdxT,EAASkF,EAAOlF,OAChByT,EAAUvO,EAAOuO,QACjB5N,EAAMX,EAAOY,aACbH,EAAaT,EAAOS,WACpBY,EAAarB,EAAOqB,WACpBD,EAAWpB,EAAOoB,SAClBjO,EAAIe,EAMR,GALIf,EAAEqb,gBAAiBrb,EAAIA,EAAEqb,eACzB1c,EAAK+d,qBACP7P,EAAO/B,KAAK,WAAY9K,GAE1BrB,EAAK+d,qBAAsB,GACtB/d,EAAK8c,UAMR,OALI9c,EAAK+c,SAAW/T,EAAOqQ,YACzBnL,EAAOoL,eAAc,GAEvBtZ,EAAK+c,SAAU,OACf/c,EAAKie,aAAc,GAIjBjV,EAAOqQ,YAAcrZ,EAAK+c,SAAW/c,EAAK8c,aAAwC,IAA1B5O,EAAOmJ,iBAAqD,IAA1BnJ,EAAOoJ,iBACnGpJ,EAAOoL,eAAc,GAIvB,IAmCI2G,EAnCAC,EAAe7Y,EAAMM,MACrBwY,EAAWD,EAAelgB,EAAKke,eAwBnC,GArBIhQ,EAAOgP,aACThP,EAAO0H,mBAAmBvU,GAC1B6M,EAAO/B,KAAK,MAAO9K,GACf8e,EAAW,KAA6C,IAArCD,EAAelgB,EAAKogB,gBACrCpgB,EAAKqgB,cAAgBnjB,aAAa8C,EAAKqgB,cAC3CrgB,EAAKqgB,aAAehZ,EAAMI,SAAS,WAC5ByG,IAAUA,EAAOsJ,WACtBtJ,EAAO/B,KAAK,QAAS9K,IACpB,MAED8e,EAAW,KAAQD,EAAelgB,EAAKogB,cAAiB,MACtDpgB,EAAKqgB,cAAgBnjB,aAAa8C,EAAKqgB,cAC3CnS,EAAO/B,KAAK,YAAa9K,KAI7BrB,EAAKogB,cAAgB/Y,EAAMM,MAC3BN,EAAMI,SAAS,WACRyG,EAAOsJ,YAAatJ,EAAOgP,YAAa,MAG1Cld,EAAK8c,YAAc9c,EAAK+c,UAAY7O,EAAOiQ,gBAAmC,IAAjB1B,EAAQtD,MAAcnZ,EAAKkW,mBAAqBlW,EAAKsf,eAIrH,OAHAtf,EAAK8c,WAAY,EACjB9c,EAAK+c,SAAU,OACf/c,EAAKie,aAAc,GAcrB,GAXAje,EAAK8c,WAAY,EACjB9c,EAAK+c,SAAU,EACf/c,EAAKie,aAAc,EAIjBgC,EADEjX,EAAO4W,aACI/Q,EAAMX,EAAOuF,WAAavF,EAAOuF,WAEhCzT,EAAKkW,iBAGjBlN,EAAOiT,SAAX,CACE,GAAIgE,GAAc/R,EAAO2F,eAEvB,YADA3F,EAAO6I,QAAQ7I,EAAOkF,aAGxB,GAAI6M,GAAc/R,EAAOiG,eAMvB,YALIjG,EAAOkB,OAAO9R,OAASgS,EAAShS,OAClC4Q,EAAO6I,QAAQzH,EAAShS,OAAS,GAEjC4Q,EAAO6I,QAAQ7I,EAAOkB,OAAO9R,OAAS,IAK1C,GAAI0L,EAAOsX,iBAAkB,CAC3B,GAA6B,EAAzBtgB,EAAK6f,WAAWviB,OAAY,CAC9B,IAAIijB,EAAgBvgB,EAAK6f,WAAWW,MAChCC,EAAgBzgB,EAAK6f,WAAWW,MAEhCE,EAAWH,EAAcT,SAAWW,EAAcX,SAClDC,EAAOQ,EAAcR,KAAOU,EAAcV,KAC9C7R,EAAOyS,SAAWD,EAAWX,EAC7B7R,EAAOyS,UAAY,EACfjQ,KAAKwB,IAAIhE,EAAOyS,UAAY3X,EAAO4X,0BACrC1S,EAAOyS,SAAW,IAIT,IAAPZ,GAAmD,IAApC1Y,EAAMM,MAAQ4Y,EAAcR,QAC7C7R,EAAOyS,SAAW,QAGpBzS,EAAOyS,SAAW,EAEpBzS,EAAOyS,UAAY3X,EAAO6X,8BAE1B7gB,EAAK6f,WAAWviB,OAAS,EACzB,IAAIwjB,EAAmB,IAAO9X,EAAO+X,sBACjCC,EAAmB9S,EAAOyS,SAAWG,EAErCG,EAAc/S,EAAOuF,UAAYuN,EACjCnS,IAAOoS,GAAeA,GAE1B,IACIC,EAEAC,EAHAC,GAAW,EAEXC,EAA2C,GAA5B3Q,KAAKwB,IAAIhE,EAAOyS,UAAiB3X,EAAOsY,4BAE3D,GAAIL,EAAc/S,EAAOiG,eACnBnL,EAAOuY,wBACLN,EAAc/S,EAAOiG,gBAAkBkN,IACzCJ,EAAc/S,EAAOiG,eAAiBkN,GAExCH,EAAsBhT,EAAOiG,eAC7BiN,GAAW,EACXphB,EAAKuf,qBAAsB,GAE3B0B,EAAc/S,EAAOiG,eAEnBnL,EAAO4L,MAAQ5L,EAAOiJ,iBAAkBkP,GAAe,QACtD,GAAIF,EAAc/S,EAAO2F,eAC1B7K,EAAOuY,wBACLN,EAAc/S,EAAO2F,eAAiBwN,IACxCJ,EAAc/S,EAAO2F,eAAiBwN,GAExCH,EAAsBhT,EAAO2F,eAC7BuN,GAAW,EACXphB,EAAKuf,qBAAsB,GAE3B0B,EAAc/S,EAAO2F,eAEnB7K,EAAO4L,MAAQ5L,EAAOiJ,iBAAkBkP,GAAe,QACtD,GAAInY,EAAOwY,eAAgB,CAEhC,IADA,IAAIzM,EACKjW,EAAI,EAAGA,EAAIwQ,EAAShS,OAAQwB,GAAK,EACxC,GAAIwQ,EAASxQ,IAAMmiB,EAAa,CAC9BlM,EAAYjW,EACZ,MASJmiB,IAJEA,EADEvQ,KAAKwB,IAAI5C,EAASyF,GAAakM,GAAevQ,KAAKwB,IAAI5C,EAASyF,EAAY,GAAKkM,IAA0C,SAA1B/S,EAAOiQ,eAC5F7O,EAASyF,GAETzF,EAASyF,EAAY,IAUvC,GANIoM,GACFjT,EAAOlC,KAAK,gBAAiB,WAC3BkC,EAAO2J,YAIa,IAApB3J,EAAOyS,SAEPG,EADEjS,EACiB6B,KAAKwB,MAAM+O,EAAc/S,EAAOuF,WAAavF,EAAOyS,UAEpDjQ,KAAKwB,KAAK+O,EAAc/S,EAAOuF,WAAavF,EAAOyS,eAEnE,GAAI3X,EAAOwY,eAEhB,YADAtT,EAAOqK,iBAILvP,EAAOuY,wBAA0BH,GACnClT,EAAO+F,eAAeiN,GACtBhT,EAAOiF,cAAc2N,GACrB5S,EAAOiI,aAAa8K,GACpB/S,EAAOuI,iBAAgB,EAAMvI,EAAOiQ,gBACpCjQ,EAAO4I,WAAY,EACnBnI,EAAWzL,cAAc,WAClBgL,IAAUA,EAAOsJ,WAAcxX,EAAKuf,sBACzCrR,EAAO/B,KAAK,kBAEZ+B,EAAOiF,cAAcnK,EAAOgK,OAC5B9E,EAAOiI,aAAa+K,GACpBvS,EAAWzL,cAAc,WAClBgL,IAAUA,EAAOsJ,WACtBtJ,EAAOhL,sBAGFgL,EAAOyS,UAChBzS,EAAO+F,eAAegN,GACtB/S,EAAOiF,cAAc2N,GACrB5S,EAAOiI,aAAa8K,GACpB/S,EAAOuI,iBAAgB,EAAMvI,EAAOiQ,gBAC/BjQ,EAAO4I,YACV5I,EAAO4I,WAAY,EACnBnI,EAAWzL,cAAc,WAClBgL,IAAUA,EAAOsJ,WACtBtJ,EAAOhL,oBAIXgL,EAAO+F,eAAegN,GAGxB/S,EAAOmH,oBACPnH,EAAOsG,2BACF,GAAIxL,EAAOwY,eAEhB,YADAtT,EAAOqK,mBAIJvP,EAAOsX,kBAAoBH,GAAYnX,EAAOyY,gBACjDvT,EAAO+F,iBACP/F,EAAOmH,oBACPnH,EAAOsG,2BAnJX,CA2JA,IAFA,IAAIkN,EAAY,EACZC,EAAYzT,EAAOsB,gBAAgB,GAC9BnS,EAAI,EAAGA,EAAIkS,EAAWjS,OAAQD,GAAK2L,EAAOmJ,oBACI,IAA1C5C,EAAWlS,EAAI2L,EAAOmJ,gBAC3B8N,GAAc1Q,EAAWlS,IAAM4iB,EAAa1Q,EAAWlS,EAAI2L,EAAOmJ,kBAEpEwP,EAAYpS,GADZmS,EAAYrkB,GACe2L,EAAOmJ,gBAAkB5C,EAAWlS,IAExD4iB,GAAc1Q,EAAWlS,KAClCqkB,EAAYrkB,EACZskB,EAAYpS,EAAWA,EAAWjS,OAAS,GAAKiS,EAAWA,EAAWjS,OAAS,IAKnF,IAAIskB,GAAS3B,EAAa1Q,EAAWmS,IAAcC,EAEnD,GAAIxB,EAAWnX,EAAOyY,aAAc,CAElC,IAAKzY,EAAO6Y,WAEV,YADA3T,EAAO6I,QAAQ7I,EAAOkF,aAGM,SAA1BlF,EAAOiQ,iBACLyD,GAAS5Y,EAAO8Y,gBAAmB5T,EAAO6I,QAAQ2K,EAAY1Y,EAAOmJ,gBAClEjE,EAAO6I,QAAQ2K,IAEM,SAA1BxT,EAAOiQ,iBACLyD,EAAS,EAAI5Y,EAAO8Y,gBAAoB5T,EAAO6I,QAAQ2K,EAAY1Y,EAAOmJ,gBACvEjE,EAAO6I,QAAQ2K,QAEnB,CAEL,IAAK1Y,EAAO+Y,YAEV,YADA7T,EAAO6I,QAAQ7I,EAAOkF,aAGM,SAA1BlF,EAAOiQ,gBACTjQ,EAAO6I,QAAQ2K,EAAY1Y,EAAOmJ,gBAEN,SAA1BjE,EAAOiQ,gBACTjQ,EAAO6I,QAAQ2K,MA6EczU,KAAKiB,GAGtCA,EAAO8T,QAxBT,SAAkB3gB,GACHrG,KACDkiB,aADCliB,KAEAgO,OAAOiZ,eAAiB5gB,EAAEid,iBAF1BtjB,KAGAgO,OAAOkZ,0BAHPlnB,KAG0C8b,YACnDzV,EAAEge,kBACFhe,EAAE8gB,8BAkBmBlV,KAAKiB,GAE9B,IAAI5M,EAAsC,cAA7B0H,EAAOoZ,kBAAoCliB,EAAKoc,EACzDnb,IAAY6H,EAAOoW,OAIrB,GAAKhV,EAAQC,QAAUD,EAAQI,gBAAiBJ,EAAQO,sBAIjD,CACL,GAAIP,EAAQC,MAAO,CACjB,IAAIY,IAAwC,eAAtBoR,EAAYgG,QAA0BjY,EAAQa,kBAAmBjC,EAAOsZ,mBAAmB,CAAEC,SAAS,EAAMphB,SAAS,GAC3IG,EAAOlG,iBAAiBihB,EAAYgG,MAAOnU,EAAOqO,aAActR,GAChE3J,EAAOlG,iBAAiBihB,EAAYmG,KAAMtU,EAAOuQ,YAAarU,EAAQa,gBAAkB,CAAEsX,SAAS,EAAOphB,QAASA,GAAYA,GAC/HG,EAAOlG,iBAAiBihB,EAAYoG,IAAKvU,EAAO8R,WAAY/U,IAEzDjC,EAAOwQ,gBAAkBgB,EAAOG,MAAQH,EAAOI,SAAa5R,EAAOwQ,gBAAkBpP,EAAQC,OAASmQ,EAAOG,OAChHrZ,EAAOlG,iBAAiB,YAAa8S,EAAOqO,cAAc,GAC1DthB,EAAIG,iBAAiB,YAAa8S,EAAOuQ,YAAatd,GACtDlG,EAAIG,iBAAiB,UAAW8S,EAAO8R,YAAY,SAbrD1e,EAAOlG,iBAAiBihB,EAAYgG,MAAOnU,EAAOqO,cAAc,GAChEthB,EAAIG,iBAAiBihB,EAAYmG,KAAMtU,EAAOuQ,YAAatd,GAC3DlG,EAAIG,iBAAiBihB,EAAYoG,IAAKvU,EAAO8R,YAAY,IAevDhX,EAAOiZ,eAAiBjZ,EAAOkZ,2BACjC5gB,EAAOlG,iBAAiB,QAAS8S,EAAO8T,SAAS,GAKrD9T,EAAOtN,GAAI4Z,EAAOG,KAAOH,EAAOI,QAAU,0CAA4C,wBAA0BkB,GAAU,IA6C1H4G,aA1CF,WACE,IAAIxU,EAASlT,KAETgO,EAASkF,EAAOlF,OAChBqT,EAAcnO,EAAOmO,YACrBnc,EAAKgO,EAAOhO,GACZoc,EAAYpO,EAAOoO,UAEnBhb,EAAsC,cAA7B0H,EAAOoZ,kBAAoCliB,EAAKoc,EACzDnb,IAAY6H,EAAOoW,OAIrB,GAAKhV,EAAQC,QAAUD,EAAQI,gBAAiBJ,EAAQO,sBAIjD,CACL,GAAIP,EAAQC,MAAO,CACjB,IAAIY,IAAwC,iBAAtBoR,EAAYgG,QAA4BjY,EAAQa,kBAAmBjC,EAAOsZ,mBAAmB,CAAEC,SAAS,EAAMphB,SAAS,GAC7IG,EAAOjG,oBAAoBghB,EAAYgG,MAAOnU,EAAOqO,aAActR,GACnE3J,EAAOjG,oBAAoBghB,EAAYmG,KAAMtU,EAAOuQ,YAAatd,GACjEG,EAAOjG,oBAAoBghB,EAAYoG,IAAKvU,EAAO8R,WAAY/U,IAE5DjC,EAAOwQ,gBAAkBgB,EAAOG,MAAQH,EAAOI,SAAa5R,EAAOwQ,gBAAkBpP,EAAQC,OAASmQ,EAAOG,OAChHrZ,EAAOjG,oBAAoB,YAAa6S,EAAOqO,cAAc,GAC7DthB,EAAII,oBAAoB,YAAa6S,EAAOuQ,YAAatd,GACzDlG,EAAII,oBAAoB,UAAW6S,EAAO8R,YAAY,SAbxD1e,EAAOjG,oBAAoBghB,EAAYgG,MAAOnU,EAAOqO,cAAc,GACnEthB,EAAII,oBAAoBghB,EAAYmG,KAAMtU,EAAOuQ,YAAatd,GAC9DlG,EAAII,oBAAoBghB,EAAYoG,IAAKvU,EAAO8R,YAAY,IAe1DhX,EAAOiZ,eAAiBjZ,EAAOkZ,2BACjC5gB,EAAOjG,oBAAoB,QAAS6S,EAAO8T,SAAS,GAKxD9T,EAAO5L,IAAKkY,EAAOG,KAAOH,EAAOI,QAAU,0CAA4C,wBAA0BkB,KA4DnH,IAIQrB,EAJJsB,EAAc,CAAEC,cApDpB,WACE,IAAI9N,EAASlT,KACToY,EAAclF,EAAOkF,YACrBgE,EAAclJ,EAAOkJ,YACrBO,EAAezJ,EAAOyJ,kBAAoC,IAAjBA,IAA0BA,EAAe,GACtF,IAAI3O,EAASkF,EAAOlF,OAChB+S,EAAc/S,EAAO+S,YACzB,GAAKA,KAAgBA,GAAmD,IAApC/U,OAAOC,KAAK8U,GAAaze,QAA7D,CAEA,IAAIqlB,EAAazU,EAAO0U,cAAc7G,GACtC,GAAI4G,GAAczU,EAAO2U,oBAAsBF,EAAY,CACzD,IAAIG,EAAoBH,KAAc5G,EAAcA,EAAY4G,GAAczU,EAAO6U,eACjFC,EAAcha,EAAO4L,MAASkO,EAAkBjS,gBAAkB7H,EAAO6H,cAE7ExJ,EAAMqC,OAAOwE,EAAOlF,OAAQ8Z,GAE5Bzb,EAAMqC,OAAOwE,EAAQ,CACnBsQ,eAAgBtQ,EAAOlF,OAAOwV,eAC9BnH,eAAgBnJ,EAAOlF,OAAOqO,eAC9BC,eAAgBpJ,EAAOlF,OAAOsO,iBAGhCpJ,EAAO2U,kBAAoBF,EAEvBK,GAAe5L,IACjBlJ,EAAOkL,cACPlL,EAAOyK,aACPzK,EAAOQ,eACPR,EAAO6I,QAAS3D,EAAcuE,EAAgBzJ,EAAOyJ,aAAc,GAAG,IAExEzJ,EAAO/B,KAAK,aAAc2W,MAsBoBF,cAlBlD,SAAwB7G,GAEtB,GAAKA,EAAL,CACA,IAAI4G,GAAa,EACbM,EAAS,GACbjc,OAAOC,KAAK8U,GAAa7U,QAAQ,SAAUgc,GACzCD,EAAOhlB,KAAKilB,KAEdD,EAAOE,KAAK,SAAU/a,EAAGgb,GAAK,OAAO5U,SAASpG,EAAG,IAAMoG,SAAS4U,EAAG,MACnE,IAAK,IAAI/lB,EAAI,EAAGA,EAAI4lB,EAAO3lB,OAAQD,GAAK,EAAG,CACzC,IAAI6lB,EAAQD,EAAO5lB,GACf6lB,GAAS5mB,EAAI+mB,aAAeV,IAC9BA,EAAaO,GAGjB,OAAOP,GAAc,SAKnBW,EAKK,CACLC,OAAQjnB,EAAIE,UAAUC,UAAUyB,MAAM,eAAiB5B,EAAIE,UAAUC,UAAUyB,MAAM,SACrFslB,UALI/I,EAAKne,EAAIE,UAAUC,UAAU8e,cACD,GAAxBd,EAAG3c,QAAQ,WAAkB2c,EAAG3c,QAAQ,UAAY,GAAK2c,EAAG3c,QAAQ,WAAa,GAKzF2lB,YAAa,+CAA+CC,KAAKpnB,EAAIE,UAAUC,YAmInF,IAEIknB,EAAW,CACbC,MAAM,EACNjN,UAAW,aACXyL,kBAAmB,YACnBjL,aAAc,EACdnE,MAAO,IAEPkE,gCAAgC,EAGhCyG,oBAAoB,EACpBE,mBAAoB,GAGpB5B,UAAU,EACVqE,kBAAkB,EAClBS,sBAAuB,EACvBQ,wBAAwB,EACxBD,4BAA6B,EAC7BT,8BAA+B,EAC/BW,gBAAgB,EAChBZ,wBAAyB,IAGzBhK,YAAY,EAGZvE,gBAAgB,EAGhB4D,kBAAkB,EAGlB7D,OAAQ,QAGR2J,iBAAaha,EAGbgO,aAAc,EACdc,cAAe,EACfJ,gBAAiB,EACjBK,oBAAqB,SACrBqB,eAAgB,EAChBF,gBAAgB,EAChBvC,mBAAoB,EACpBE,kBAAmB,EACnB+F,qBAAqB,EAGrBjD,eAAe,EAGfX,cAAc,EAGdyN,WAAY,EACZZ,WAAY,GACZpF,eAAe,EACfuI,aAAa,EACbF,YAAY,EACZC,gBAAiB,GACjBL,aAAc,IACd7B,cAAc,EACdpB,gBAAgB,EAChBJ,UAAW,EACXe,0BAA0B,EAC1BR,qBAAqB,EAGrBkF,mBAAmB,EAGnBlE,YAAY,EACZD,gBAAiB,IAGjB9M,qBAAqB,EACrBC,uBAAuB,EAGvBwG,YAAY,EAGZ4I,eAAe,EACfC,0BAA0B,EAC1BlM,qBAAqB,EAGrB8N,eAAe,EACfC,qBAAqB,EAGrBnP,MAAM,EACNmE,qBAAsB,EACtBpB,aAAc,KACdiB,wBAAwB,EAGxBtB,gBAAgB,EAChBD,gBAAgB,EAChB8F,aAAc,KACdH,WAAW,EACXgH,eAAgB,oBAChB/G,kBAAmB,KAGnBqF,kBAAkB,EAGlB2B,uBAAwB,oBACxBC,WAAY,eACZC,gBAAiB,+BACjBxP,iBAAkB,sBAClBG,0BAA2B,gCAC3BnB,kBAAmB,uBACnBkB,oBAAqB,yBACrBG,eAAgB,oBAChBG,wBAAyB,8BACzBD,eAAgB,oBAChBE,wBAAyB,8BACzBgP,aAAc,iBAGdC,oBAAoB,GAGlBC,EAAa,CACfxW,OAAQA,EACR2F,UAAWA,EACXjT,WAAYgW,EACZrF,MAAOA,EACPyD,KAAMA,EACNyE,WAAYA,EACZO,aAAcA,EACd5X,OAAQA,EACR+Z,YAAaA,EACbpJ,cA3IoB,CAAEA,cAjBxB,WACE,IAAIzE,EAASlT,KACTupB,EAAYrW,EAAOuL,SAEvBvL,EAAOuL,SAAsC,IAA3BvL,EAAOoB,SAAShS,OAClC4Q,EAAOmJ,gBAAkBnJ,EAAOuL,SAChCvL,EAAOoJ,gBAAkBpJ,EAAOuL,SAG5B8K,IAAcrW,EAAOuL,UAAYvL,EAAO/B,KAAK+B,EAAOuL,SAAW,OAAS,UAExE8K,GAAaA,IAAcrW,EAAOuL,WACpCvL,EAAOmG,OAAQ,EACfnG,EAAOsW,WAAW1W,YAgJpBjP,QAxNY,CAAE4lB,WAnDhB,WACE,IACIC,EADS1pB,KACW0pB,WACpB1b,EAFShO,KAEOgO,OAChB6F,EAHS7T,KAGI6T,IACbV,EAJSnT,KAIImT,IACbwW,EAAW,GAEfA,EAAS1mB,KAAK+K,EAAO2N,WAEjB3N,EAAOiT,UACT0I,EAAS1mB,KAAK,aAEXmM,EAAQW,SACX4Z,EAAS1mB,KAAK,cAEZ+K,EAAO4N,YACT+N,EAAS1mB,KAAK,cAEZ4Q,GACF8V,EAAS1mB,KAAK,OAEa,EAAzB+K,EAAOyH,iBACTkU,EAAS1mB,KAAK,YAEZuc,EAAOI,SACT+J,EAAS1mB,KAAK,WAEZuc,EAAOG,KACTgK,EAAS1mB,KAAK,OAGZqlB,EAAQC,OAASnZ,EAAQI,eAAiBJ,EAAQO,wBACpDga,EAAS1mB,KAAM,OAAU+K,EAAgB,WAG3C2b,EAASzd,QAAQ,SAAU0d,GACzBF,EAAWzmB,KAAK+K,EAAOib,uBAAyBW,KAGlDzW,EAAIxP,SAAS+lB,EAAWpc,KAAK,OAWSuc,cARxC,WACE,IACI1W,EADSnT,KACImT,IACbuW,EAFS1pB,KAEW0pB,WAExBvW,EAAIlP,YAAYylB,EAAWpc,KAAK,QA4NhCwc,OAnKW,CACXC,UArDF,SAAoBC,EAASC,EAAKC,EAAQC,EAAOC,EAAkBjiB,GACjE,IAAIkiB,EACJ,SAASC,IACHniB,GAAYA,IAEb6hB,EAAQO,UAAaH,EAmBxBE,IAlBIL,IACFI,EAAQ,IAAI/oB,EAAIQ,OACV0oB,OAASF,EACfD,EAAMI,QAAUH,EACZH,IACFE,EAAMF,MAAQA,GAEZD,IACFG,EAAMH,OAASA,GAEbD,IACFI,EAAMJ,IAAMA,IAGdK,KAkCJxB,cA1BF,WACE,IAAI5V,EAASlT,KAEb,SAASsqB,IACH,MAAOpX,GAA8CA,IAAUA,EAAOsJ,iBAC9CzV,IAAxBmM,EAAOwX,eAA8BxX,EAAOwX,cAAgB,GAC5DxX,EAAOwX,eAAiBxX,EAAOyX,aAAaroB,SAC1C4Q,EAAOlF,OAAO+a,qBAAuB7V,EAAOJ,SAChDI,EAAO/B,KAAK,iBANhB+B,EAAOyX,aAAezX,EAAOC,IAAIxH,KAAK,OAStC,IAAK,IAAItJ,EAAI,EAAGA,EAAI6Q,EAAOyX,aAAaroB,OAAQD,GAAK,EAAG,CACtD,IAAI2nB,EAAU9W,EAAOyX,aAAatoB,GAClC6Q,EAAO6W,UACLC,EACAA,EAAQY,YAAcZ,EAAQplB,aAAa,OAC3ColB,EAAQE,QAAUF,EAAQplB,aAAa,UACvColB,EAAQG,OAASH,EAAQplB,aAAa,UACtC,EACA0lB,OA2KFO,EAAmB,GAEnB9qB,EAAU,SAAU+qB,GACtB,SAAS/qB,IAIP,IAHA,IAAI8F,EAIAX,EACA8I,EAHAlI,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAGrB,IAAhBD,EAAKxD,QAAgBwD,EAAK,GAAG2I,aAAe3I,EAAK,GAAG2I,cAAgBzC,OACtEgC,EAASlI,EAAK,IAEEZ,GAAfW,EAASC,GAAkB,GAAIkI,EAASnI,EAAO,IAE7CmI,IAAUA,EAAS,IAExBA,EAAS3B,EAAMqC,OAAO,GAAIV,GACtB9I,IAAO8I,EAAO9I,KAAM8I,EAAO9I,GAAKA,GAEpC4lB,EAAexiB,KAAKtI,KAAMgO,GAE1BhC,OAAOC,KAAKqd,GAAYpd,QAAQ,SAAU6e,GACxC/e,OAAOC,KAAKqd,EAAWyB,IAAiB7e,QAAQ,SAAU8e,GACnDjrB,EAAOyD,UAAUwnB,KACpBjrB,EAAOyD,UAAUwnB,GAAe1B,EAAWyB,GAAgBC,QAMjE,IAAI9X,EAASlT,UACiB,IAAnBkT,EAAOxB,UAChBwB,EAAOxB,QAAU,IAEnB1F,OAAOC,KAAKiH,EAAOxB,SAASxF,QAAQ,SAAUyF,GAC5C,IAAI/R,EAASsT,EAAOxB,QAAQC,GAC5B,GAAI/R,EAAOoO,OAAQ,CACjB,IAAIid,EAAkBjf,OAAOC,KAAKrM,EAAOoO,QAAQ,GAC7C8D,EAAelS,EAAOoO,OAAOid,GACjC,GAA4B,iBAAjBnZ,EAA6B,OACxC,KAAMmZ,KAAmBjd,GAAU,YAAa8D,GAAiB,QACjC,IAA5B9D,EAAOid,KACTjd,EAAOid,GAAmB,CAAE/W,SAAS,IAGF,iBAA5BlG,EAAOid,IACT,YAAajd,EAAOid,KAEzBjd,EAAOid,GAAiB/W,SAAU,GAE/BlG,EAAOid,KAAoBjd,EAAOid,GAAmB,CAAE/W,SAAS,OAKzE,IAAIgX,EAAe7e,EAAMqC,OAAO,GAAIia,GACpCzV,EAAO3B,iBAAiB2Z,GAGxBhY,EAAOlF,OAAS3B,EAAMqC,OAAO,GAAIwc,EAAcL,EAAkB7c,GACjEkF,EAAO6U,eAAiB1b,EAAMqC,OAAO,GAAIwE,EAAOlF,QAChDkF,EAAOiY,aAAe9e,EAAMqC,OAAO,GAAIV,GAMvC,IAAImF,GAHJD,EAAO3Q,EAAIA,GAGC2Q,EAAOlF,OAAO9I,IAG1B,GAFAA,EAAKiO,EAAI,GAET,CAIA,GAAiB,EAAbA,EAAI7Q,OAAY,CAClB,IAAI8oB,EAAU,GAKd,OAJAjY,EAAIvJ,KAAK,SAAUO,EAAOkhB,GACxB,IAAIC,EAAYjf,EAAMqC,OAAO,GAAIV,EAAQ,CAAE9I,GAAImmB,IAC/CD,EAAQnoB,KAAK,IAAIlD,EAAOurB,MAEnBF,EAGTlmB,EAAGgO,OAASA,EACZC,EAAInO,KAAK,SAAUkO,GAGnB,IAmDQ7D,EACAyQ,EApDJnM,EAAaR,EAAIpS,SAAU,IAAOmS,EAAOlF,OAAmB,cAwHhE,OArHA3B,EAAMqC,OAAOwE,EAAQ,CACnBC,IAAKA,EACLjO,GAAIA,EACJyO,WAAYA,EACZ2N,UAAW3N,EAAW,GAGtB+V,WAAY,GAGZtV,OAAQ7R,IACRgS,WAAY,GACZD,SAAU,GACVE,gBAAiB,GAGjBlB,aAAc,WACZ,MAAmC,eAA5BJ,EAAOlF,OAAO2N,WAEvBpI,WAAY,WACV,MAAmC,aAA5BL,EAAOlF,OAAO2N,WAGvB9H,IAA+B,QAAzB3O,EAAG2W,IAAI0E,eAAoD,QAAzBpN,EAAI1J,IAAI,aAChDqK,aAA0C,eAA5BZ,EAAOlF,OAAO2N,YAAwD,QAAzBzW,EAAG2W,IAAI0E,eAAoD,QAAzBpN,EAAI1J,IAAI,cACrGsK,SAAwC,gBAA9BJ,EAAWlK,IAAI,WAGzB2O,YAAa,EACbsB,UAAW,EAGXN,aAAa,EACbC,OAAO,EAGPZ,UAAW,EACX8C,kBAAmB,EACnBvC,SAAU,EACV2M,SAAU,EACV7J,WAAW,EAGXO,eAAgBnJ,EAAOlF,OAAOqO,eAC9BC,eAAgBpJ,EAAOlF,OAAOsO,eAG9B+E,aACMhS,EAAQ,CAAC,aAAc,YAAa,YACpCyQ,EAAU,CAAC,YAAa,YAAa,WACrC1Q,EAAQI,cACVsQ,EAAU,CAAC,cAAe,cAAe,aAChC1Q,EAAQO,wBACjBmQ,EAAU,CAAC,gBAAiB,gBAAiB,gBAE/C5M,EAAOqY,iBAAmB,CACxBlE,MAAOhY,EAAM,GACbmY,KAAMnY,EAAM,GACZoY,IAAKpY,EAAM,IAEb6D,EAAOsY,mBAAqB,CAC1BnE,MAAOvH,EAAQ,GACf0H,KAAM1H,EAAQ,GACd2H,IAAK3H,EAAQ,IAER1Q,EAAQC,QAAU6D,EAAOlF,OAAOwQ,cAAgBtL,EAAOqY,iBAAmBrY,EAAOsY,oBAE1FhK,gBAAiB,CACfM,eAAW/a,EACXgb,aAAShb,EACTgc,yBAAqBhc,EACrBmc,oBAAgBnc,EAChBic,iBAAajc,EACbmU,sBAAkBnU,EAClBud,oBAAgBvd,EAChBsc,wBAAoBtc,EAEpBwc,aAAc,iDAEd6B,cAAe/Y,EAAMM,MACrB0Y,kBAActe,EAEd8d,WAAY,GACZN,yBAAqBxd,EACrB4a,kBAAc5a,EACdkc,iBAAalc,GAIfmb,YAAY,EAGZsB,eAAgBtQ,EAAOlF,OAAOwV,eAE9B/B,QAAS,CACPgB,OAAQ,EACRC,OAAQ,EACRN,SAAU,EACVG,SAAU,EACVpE,KAAM,GAIRwM,aAAc,GACdD,aAAc,IAKhBxX,EAAOtB,aAGHsB,EAAOlF,OAAO4a,MAChB1V,EAAO0V,OAIF1V,GAGJ4X,IAAiB/qB,EAAO0rB,UAAYX,GAIzC,IAAIna,EAAkB,CAAEka,iBAAkB,CAAEha,cAAc,GAAO8X,SAAU,CAAE9X,cAAc,GAAOpN,MAAO,CAAEoN,cAAc,GAAOtO,EAAG,CAAEsO,cAAc,IA4NnJ,QA/NA9Q,EAAOyD,UAAYwI,OAAOmG,OAAQ2Y,GAAkBA,EAAetnB,YAClDiL,YAAc1O,GAIxByD,UAAUia,qBAAuB,WACtC,IAAIvK,EAASlT,KACTgO,EAASkF,EAAOlF,OAChBoG,EAASlB,EAAOkB,OAChBG,EAAarB,EAAOqB,WACpBX,EAAaV,EAAOO,KACpB2E,EAAclF,EAAOkF,YACrBsT,EAAM,EACV,GAAI1d,EAAOiJ,eAAgB,CAGzB,IAFA,IACI0U,EADAxW,EAAYf,EAAOgE,GAAapB,gBAE3B3U,EAAI+V,EAAc,EAAG/V,EAAI+R,EAAO9R,OAAQD,GAAK,EAChD+R,EAAO/R,KAAOspB,IAEhBD,GAAO,EACS9X,GAFhBuB,GAAaf,EAAO/R,GAAG2U,mBAEO2U,GAAY,IAG9C,IAAK,IAAIrU,EAAMc,EAAc,EAAU,GAAPd,EAAUA,GAAO,EAC3ClD,EAAOkD,KAASqU,IAElBD,GAAO,EACS9X,GAFhBuB,GAAaf,EAAOkD,GAAKN,mBAEK2U,GAAY,SAI9C,IAAK,IAAInU,EAAMY,EAAc,EAAGZ,EAAMpD,EAAO9R,OAAQkV,GAAO,EACtDjD,EAAWiD,GAAOjD,EAAW6D,GAAexE,IAC9C8X,GAAO,GAIb,OAAOA,GAGT3rB,EAAOyD,UAAUsP,OAAS,WACxB,IAAII,EAASlT,KACb,GAAKkT,IAAUA,EAAOsJ,UAAtB,CACA,IAAIlI,EAAWpB,EAAOoB,SAClBtG,EAASkF,EAAOlF,OAEhBA,EAAO+S,aACT7N,EAAO8N,gBAET9N,EAAOH,aACPG,EAAOQ,eACPR,EAAO+F,iBACP/F,EAAOsG,sBAUHtG,EAAOlF,OAAOiT,UAChB9F,IACIjI,EAAOlF,OAAO4N,YAChB1I,EAAO6E,sBAG4B,SAAhC7E,EAAOlF,OAAO6H,eAA0D,EAA9B3C,EAAOlF,OAAO6H,gBAAsB3C,EAAOmG,QAAUnG,EAAOlF,OAAOiJ,eACnG/D,EAAO6I,QAAQ7I,EAAOkB,OAAO9R,OAAS,EAAG,GAAG,GAAO,GAEnD4Q,EAAO6I,QAAQ7I,EAAOkF,YAAa,GAAG,GAAO,KAG1D+C,IAGAnN,EAAO0J,eAAiBpD,IAAapB,EAAOoB,UAC9CpB,EAAOyE,gBAETzE,EAAO/B,KAAK,UA1BZ,SAASgK,IACP,IAAIyQ,EAAiB1Y,EAAOY,cAAmC,EAApBZ,EAAOuF,UAAiBvF,EAAOuF,UACtEyI,EAAexL,KAAKyL,IAAIzL,KAAKK,IAAI6V,EAAgB1Y,EAAOiG,gBAAiBjG,EAAO2F,gBACpF3F,EAAOiI,aAAa+F,GACpBhO,EAAOmH,oBACPnH,EAAOsG,wBAwBXzZ,EAAOyD,UAAUolB,KAAO,WACtB,IAAI1V,EAASlT,KACTkT,EAAOkJ,cAEXlJ,EAAO/B,KAAK,cAGR+B,EAAOlF,OAAO+S,aAChB7N,EAAO8N,gBAIT9N,EAAOuW,aAGHvW,EAAOlF,OAAO4L,MAChB1G,EAAOyK,aAITzK,EAAOH,aAGPG,EAAOQ,eAEHR,EAAOlF,OAAO0J,eAChBxE,EAAOyE,gBAILzE,EAAOlF,OAAOqQ,YAChBnL,EAAOoL,gBAGLpL,EAAOlF,OAAO8a,eAChB5V,EAAO4V,gBAIL5V,EAAOlF,OAAO4L,KAChB1G,EAAO6I,QAAQ7I,EAAOlF,OAAOmO,aAAejJ,EAAOyJ,aAAc,EAAGzJ,EAAOlF,OAAOqb,oBAElFnW,EAAO6I,QAAQ7I,EAAOlF,OAAOmO,aAAc,EAAGjJ,EAAOlF,OAAOqb,oBAI9DnW,EAAOkO,eAGPlO,EAAOkJ,aAAc,EAGrBlJ,EAAO/B,KAAK,UAGdpR,EAAOyD,UAAUqoB,QAAU,SAAkBC,EAAgBC,QACnC,IAAnBD,IAA4BA,GAAiB,QAC7B,IAAhBC,IAAyBA,GAAc,GAE5C,IAAI7Y,EAASlT,KACTgO,EAASkF,EAAOlF,OAChBmF,EAAMD,EAAOC,IACbQ,EAAaT,EAAOS,WACpBS,EAASlB,EAAOkB,OAEpB,YAA6B,IAAlBlB,EAAOlF,QAA0BkF,EAAOsJ,YAInDtJ,EAAO/B,KAAK,iBAGZ+B,EAAOkJ,aAAc,EAGrBlJ,EAAOwU,eAGH1Z,EAAO4L,MACT1G,EAAOkL,cAIL2N,IACF7Y,EAAO2W,gBACP1W,EAAIrO,WAAW,SACf6O,EAAW7O,WAAW,SAClBsP,GAAUA,EAAO9R,QACnB8R,EACGnQ,YAAY,CACX+J,EAAO2K,kBACP3K,EAAO2L,iBACP3L,EAAOgM,eACPhM,EAAOkM,gBAAiB5M,KAAK,MAC9BxI,WAAW,SACXA,WAAW,2BACXA,WAAW,sBACXA,WAAW,oBAIlBoO,EAAO/B,KAAK,WAGZnF,OAAOC,KAAKiH,EAAOzC,iBAAiBvE,QAAQ,SAAUwE,GACpDwC,EAAO5L,IAAIoJ,MAGU,IAAnBob,IACF5Y,EAAOC,IAAI,GAAGD,OAAS,KACvBA,EAAOC,IAAInO,KAAK,SAAU,MAC1BqH,EAAMC,YAAY4G,IAEpBA,EAAOsJ,WAAY,GA/CV,MAoDXzc,EAAOisB,eAAiB,SAAyBC,GAC/C5f,EAAMqC,OAAOmc,EAAkBoB,IAGjCtb,EAAgBka,iBAAiBxa,IAAM,WACrC,OAAOwa,GAGTla,EAAgBgY,SAAStY,IAAM,WAC7B,OAAOsY,GAGThY,EAAgBlN,MAAM4M,IAAM,WAC1B,OAAOya,GAGTna,EAAgBpO,EAAE8N,IAAM,WACtB,OAAO9N,GAGTyJ,OAAO6G,iBAAkB9S,EAAQ4Q,GAE1B5Q,EAjbI,CAkbXwQ,GAEE2b,EAAW,CACb3Z,KAAM,SACNC,MAAO,CACLkN,OAAQF,GAEV/M,OAAQ,CACNiN,OAAQF,IAIR2M,EAAY,CACd5Z,KAAM,UACNC,MAAO,CACL4Z,QAAShd,GAEXqD,OAAQ,CACN2Z,QAAShd,IAITid,EAAY,CACd9Z,KAAM,UACNC,MAAO,CACL8Z,QAAShE,GAEX7V,OAAQ,CACN6Z,QAAShE,IAITiE,EAAS,CACXha,KAAM,SACNJ,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBsZ,OAAQ,CACNC,cAAe,WACRvZ,IAAUA,EAAOsJ,WAActJ,EAAOkJ,cAC3ClJ,EAAO/B,KAAK,gBACZ+B,EAAO/B,KAAK,YAEdub,yBAA0B,WACnBxZ,IAAUA,EAAOsJ,WAActJ,EAAOkJ,aAC3ClJ,EAAO/B,KAAK,0BAKpBvL,GAAI,CACFgjB,KAAM,WAGJtnB,EAAIlB,iBAAiB,SAFRJ,KAEyBwsB,OAAOC,eAG7CnrB,EAAIlB,iBAAiB,oBALRJ,KAKoCwsB,OAAOE,2BAE1Db,QAAS,WAEPvqB,EAAIjB,oBAAoB,SADXL,KAC4BwsB,OAAOC,eAChDnrB,EAAIjB,oBAAoB,oBAFXL,KAEuCwsB,OAAOE,6BAK7DC,EAAW,CACbC,KAAMtrB,EAAIurB,kBAAoBvrB,EAAIwrB,uBAClCC,OAAQ,SAAgBzmB,EAAQ0mB,QACb,IAAZA,IAAqBA,EAAU,IAEpC,IAAI9Z,EAASlT,KAGTgQ,EAAW,IADI2c,EAASC,KACI,SAAUK,GAIxC,GAAyB,IAArBA,EAAU3qB,OAAd,CAIA,IAAI4qB,EAAiB,WACnBha,EAAO/B,KAAK,iBAAkB8b,EAAU,KAGtC3rB,EAAI6rB,sBACN7rB,EAAI6rB,sBAAsBD,GAE1B5rB,EAAIW,WAAWirB,EAAgB,QAV/Bha,EAAO/B,KAAK,iBAAkB8b,EAAU,MAc5Cjd,EAASod,QAAQ9mB,EAAQ,CACvB+mB,gBAA0C,IAAvBL,EAAQK,YAAoCL,EAAQK,WACvEC,eAAwC,IAAtBN,EAAQM,WAAmCN,EAAQM,UACrEC,mBAAgD,IAA1BP,EAAQO,eAAuCP,EAAQO,gBAG/Era,EAAOlD,SAASwd,UAAUvqB,KAAK+M,IAEjC4Y,KAAM,WACJ,IAAI1V,EAASlT,KACb,GAAKoP,EAAQY,UAAakD,EAAOlF,OAAOgC,SAAxC,CACA,GAAIkD,EAAOlF,OAAOyf,eAEhB,IADA,IAAIC,EAAmBxa,EAAOC,IAAIvM,UACzBvE,EAAI,EAAGA,EAAIqrB,EAAiBprB,OAAQD,GAAK,EAChD6Q,EAAOlD,SAAS+c,OAAOW,EAAiBrrB,IAI5C6Q,EAAOlD,SAAS+c,OAAO7Z,EAAOC,IAAI,GAAI,CAAEma,WAAW,IAGnDpa,EAAOlD,SAAS+c,OAAO7Z,EAAOS,WAAW,GAAI,CAAE0Z,YAAY,MAE7DxB,QAAS,WACM7rB,KACNgQ,SAASwd,UAAUthB,QAAQ,SAAU8D,GAC1CA,EAAS2d,eAFE3tB,KAINgQ,SAASwd,UAAY,KAI5BI,EAAa,CACfrb,KAAM,WACNvE,OAAQ,CACNgC,UAAU,EACVyd,gBAAgB,GAElBtb,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBgQ,SAAU,CACR4Y,KAAM+D,EAAS/D,KAAK3W,KAHXjS,MAIT+sB,OAAQJ,EAASI,OAAO9a,KAJfjS,MAKT6rB,QAASc,EAASd,QAAQ5Z,KALjBjS,MAMTwtB,UAAW,OAIjB5nB,GAAI,CACFgjB,KAAM,WACS5oB,KACNgQ,SAAS4Y,QAElBiD,QAAS,WACM7rB,KACNgQ,SAAS6b,aAKlBgC,EAAU,CACZ/a,OAAQ,SAAgBgb,GACtB,IAAI5a,EAASlT,KACT+tB,EAAM7a,EAAOlF,OACb6H,EAAgBkY,EAAIlY,cACpBsB,EAAiB4W,EAAI5W,eACrBF,EAAiB8W,EAAI9W,eACrB+W,EAAQ9a,EAAOe,QACfga,EAAeD,EAAME,KACrBC,EAAaH,EAAMpf,GACnBwF,EAAS4Z,EAAM5Z,OACfga,EAAqBJ,EAAMzZ,WAC3B8Z,EAAcL,EAAMK,YACpBC,EAAiBN,EAAMllB,OAC3BoK,EAAOmH,oBACP,IAEIkU,EAIAC,EACAC,EAPArW,EAAclF,EAAOkF,aAAe,EAGbmW,EAAvBrb,EAAOY,aAA6B,QACpBZ,EAAOI,eAAiB,OAAS,MAIjD2D,GACFuX,EAAc9Y,KAAKC,MAAME,EAAgB,GAAKsB,EAC9CsX,EAAe/Y,KAAKC,MAAME,EAAgB,GAAKsB,IAE/CqX,EAAc3Y,GAAiBsB,EAAiB,GAChDsX,EAAetX,GAEjB,IAAI+W,EAAOxY,KAAKK,KAAKqC,GAAe,GAAKqW,EAAc,GACnD7f,EAAK8G,KAAKyL,KAAK/I,GAAe,GAAKoW,EAAapa,EAAO9R,OAAS,GAChEwG,GAAUoK,EAAOqB,WAAW2Z,IAAS,IAAMhb,EAAOqB,WAAW,IAAM,GASvE,SAASma,IACPxb,EAAOQ,eACPR,EAAO+F,iBACP/F,EAAOsG,sBACHtG,EAAOyb,MAAQzb,EAAOlF,OAAO2gB,KAAKza,SACpChB,EAAOyb,KAAKC,OAIhB,GAhBAviB,EAAMqC,OAAOwE,EAAOe,QAAS,CAC3Bia,KAAMA,EACNtf,GAAIA,EACJ9F,OAAQA,EACRyL,WAAYrB,EAAOqB,aAYjB0Z,IAAiBC,GAAQC,IAAevf,IAAOkf,EAKjD,OAJI5a,EAAOqB,aAAe6Z,GAAsBtlB,IAAWwlB,GACzDpb,EAAOkB,OAAO3K,IAAI8kB,EAAazlB,EAAS,WAE1CoK,EAAO+F,iBAGT,GAAI/F,EAAOlF,OAAOiG,QAAQ4a,eAcxB,OAbA3b,EAAOlF,OAAOiG,QAAQ4a,eAAevmB,KAAK4K,EAAQ,CAChDpK,OAAQA,EACRolB,KAAMA,EACNtf,GAAIA,EACJwF,OAAS,WAEP,IADA,IAAI0a,EAAiB,GACZzsB,EAAI6rB,EAAM7rB,GAAKuM,EAAIvM,GAAK,EAC/BysB,EAAe7rB,KAAKmR,EAAO/R,IAE7B,OAAOysB,EALD,UAQVJ,IAGF,IAAIK,EAAiB,GACjBC,EAAgB,GACpB,GAAIlB,EACF5a,EAAOS,WAAWhI,KAAM,IAAOuH,EAAOlF,OAAiB,YAAI9J,cAE3D,IAAK,IAAI7B,EAAI4rB,EAAc5rB,GAAK8rB,EAAY9rB,GAAK,GAC3CA,EAAI6rB,GAAYtf,EAAJvM,IACd6Q,EAAOS,WAAWhI,KAAM,IAAOuH,EAAOlF,OAAiB,WAAI,6BAAgC3L,EAAI,MAAQ6B,SAI7G,IAAK,IAAIoT,EAAM,EAAGA,EAAMlD,EAAO9R,OAAQgV,GAAO,EACjC4W,GAAP5W,GAAeA,GAAO1I,SACE,IAAfuf,GAA8BL,EACvCkB,EAAc/rB,KAAKqU,IAET6W,EAAN7W,GAAoB0X,EAAc/rB,KAAKqU,GACvCA,EAAM2W,GAAgBc,EAAe9rB,KAAKqU,KAIpD0X,EAAc9iB,QAAQ,SAAU/B,GAC9B+I,EAAOS,WAAWnJ,OAAO6jB,EAAYja,EAAOjK,GAAQA,MAEtD4kB,EAAe5G,KAAK,SAAU/a,EAAGgb,GAAK,OAAOhb,EAAIgb,IAAMlc,QAAQ,SAAU/B,GACvE+I,EAAOS,WAAW9I,QAAQwjB,EAAYja,EAAOjK,GAAQA,MAEvD+I,EAAOS,WAAW5S,SAAS,iBAAiB0I,IAAI8kB,EAAazlB,EAAS,MACtE4lB,KAEFL,YAAa,SAAqBlY,EAAOhM,GACvC,IAAI+I,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOiG,QAC3B,GAAIjG,EAAOihB,OAAS/b,EAAOe,QAAQgb,MAAM9kB,GACvC,OAAO+I,EAAOe,QAAQgb,MAAM9kB,GAE9B,IAAI+kB,EAAWlhB,EAAOqgB,YAClB9rB,EAAEyL,EAAOqgB,YAAY/lB,KAAK4K,EAAQiD,EAAOhM,IACzC5H,EAAG,eAAmB2Q,EAAOlF,OAAiB,WAAI,8BAAkC7D,EAAQ,KAAQgM,EAAQ,UAGhH,OAFK+Y,EAAS3qB,KAAK,4BAA8B2qB,EAAS3qB,KAAK,0BAA2B4F,GACtF6D,EAAOihB,QAAS/b,EAAOe,QAAQgb,MAAM9kB,GAAS+kB,GAC3CA,GAETrQ,YAAa,SAAqB1I,GACnBnW,KACNiU,QAAQG,OAAOnR,KAAKkT,GADdnW,KAENiU,QAAQnB,QAAO,IAExBgM,aAAc,SAAsB3I,GAClC,IAAIjD,EAASlT,KAEb,GADAkT,EAAOe,QAAQG,OAAO3N,QAAQ0P,GAC1BjD,EAAOlF,OAAOiG,QAAQgb,MAAO,CAC/B,IAAIA,EAAQ/b,EAAOe,QAAQgb,MACvBE,EAAW,GACfnjB,OAAOC,KAAKgjB,GAAO/iB,QAAQ,SAAUkjB,GACnCD,EAASC,EAAc,GAAKH,EAAMG,KAEpClc,EAAOe,QAAQgb,MAAQE,EAEzBjc,EAAOe,QAAQnB,QAAO,GACtBI,EAAO0J,UAAU,KAIjByS,EAAY,CACd9c,KAAM,UACNvE,OAAQ,CACNiG,QAAS,CACPC,SAAS,EACTE,OAAQ,GACR6a,OAAO,EACPZ,YAAa,KACbQ,eAAgB,OAGpB1c,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBe,QAAS,CACPnB,OAAQ+a,EAAQ/a,OAAOb,KAAKiB,GAC5B2L,YAAagP,EAAQhP,YAAY5M,KAAKiB,GACtC4L,aAAc+O,EAAQ/O,aAAa7M,KAAKiB,GACxCmb,YAAaR,EAAQQ,YAAYpc,KAAKiB,GACtCkB,OAAQlB,EAAOlF,OAAOiG,QAAQG,OAC9B6a,MAAO,OAIbrpB,GAAI,CACF0pB,WAAY,WACV,IAAIpc,EAASlT,KACb,GAAKkT,EAAOlF,OAAOiG,QAAQC,QAA3B,CACAhB,EAAOwW,WAAWzmB,KAAOiQ,EAAOlF,OAA6B,uBAAI,WACjE,IAAIuhB,EAAkB,CACpB3X,qBAAqB,GAEvBvL,EAAMqC,OAAOwE,EAAOlF,OAAQuhB,GAC5BljB,EAAMqC,OAAOwE,EAAO6U,eAAgBwH,GAEpCrc,EAAOe,QAAQnB,WAEjBqI,aAAc,WACCnb,KACDgO,OAAOiG,QAAQC,SADdlU,KAENiU,QAAQnB,YAKjB0c,EAAW,CACbC,OAAQ,SAAgBroB,GACtB,IAAI8L,EAASlT,KACT6T,EAAMX,EAAOY,aACbzN,EAAIe,EACJf,EAAEqb,gBAAiBrb,EAAIA,EAAEqb,eAC7B,IAAIgO,EAAKrpB,EAAEspB,SAAWtpB,EAAEupB,SAExB,IAAK1c,EAAOmJ,iBAAoBnJ,EAAOI,gBAAyB,KAAPoc,GAAexc,EAAOK,cAAuB,KAAPmc,GAC7F,OAAO,EAET,IAAKxc,EAAOoJ,iBAAoBpJ,EAAOI,gBAAyB,KAAPoc,GAAexc,EAAOK,cAAuB,KAAPmc,GAC7F,OAAO,EAET,KAAIrpB,EAAEwpB,UAAYxpB,EAAEypB,QAAUzpB,EAAE0pB,SAAW1pB,EAAE2pB,SAGzC/vB,EAAIK,eAAiBL,EAAIK,cAAcE,WAA0D,UAA7CP,EAAIK,cAAcE,SAAS+f,eAA0E,aAA7CtgB,EAAIK,cAAcE,SAAS+f,gBAA3I,CAGA,GAAIrN,EAAOlF,OAAOiiB,SAASC,iBAA0B,KAAPR,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,GAAY,CAC/F,IAAIS,GAAS,EAEb,GAAoE,EAAhEjd,EAAOC,IAAIvM,QAAS,IAAOsM,EAAOlF,OAAiB,YAAI1L,QAAsF,IAAxE4Q,EAAOC,IAAIvM,QAAS,IAAOsM,EAAOlF,OAAuB,kBAAI1L,OACpI,OAEF,IAAI8tB,EAAc9uB,EAAI+mB,WAClBgI,EAAe/uB,EAAIgvB,YACnBC,EAAerd,EAAOC,IAAIrK,SAC1B+K,IAAO0c,EAAa/mB,MAAQ0J,EAAOC,IAAI,GAAG9J,YAM9C,IALA,IAAImnB,EAAc,CAChB,CAACD,EAAa/mB,KAAM+mB,EAAahnB,KACjC,CAACgnB,EAAa/mB,KAAO0J,EAAOF,MAAOud,EAAahnB,KAChD,CAACgnB,EAAa/mB,KAAM+mB,EAAahnB,IAAM2J,EAAOD,QAC9C,CAACsd,EAAa/mB,KAAO0J,EAAOF,MAAOud,EAAahnB,IAAM2J,EAAOD,SACtD5Q,EAAI,EAAGA,EAAImuB,EAAYluB,OAAQD,GAAK,EAAG,CAC9C,IAAI6lB,EAAQsI,EAAYnuB,GAEV,GAAZ6lB,EAAM,IAAWA,EAAM,IAAMkI,GACd,GAAZlI,EAAM,IAAWA,EAAM,IAAMmI,IAEhCF,GAAS,GAGb,IAAKA,EAAU,OAEbjd,EAAOI,gBACE,KAAPoc,GAAoB,KAAPA,IACXrpB,EAAEid,eAAkBjd,EAAEid,iBACnBjd,EAAEoqB,aAAc,IAEb,KAAPf,IAAc7b,GAAgB,KAAP6b,GAAa7b,IAAQX,EAAO0J,aAC5C,KAAP8S,IAAc7b,GAAgB,KAAP6b,GAAa7b,IAAQX,EAAO6J,cAE7C,KAAP2S,GAAoB,KAAPA,IACXrpB,EAAEid,eAAkBjd,EAAEid,iBACnBjd,EAAEoqB,aAAc,GAEd,KAAPf,GAAaxc,EAAO0J,YACb,KAAP8S,GAAaxc,EAAO6J,aAE1B7J,EAAO/B,KAAK,WAAYue,KAG1BgB,OAAQ,WACO1wB,KACFiwB,SAAS/b,UACpB3R,EAAEtC,GAAK2F,GAAG,UAFG5F,KAEeiwB,SAASR,QAFxBzvB,KAGNiwB,SAAS/b,SAAU,IAE5Byc,QAAS,WACM3wB,KACDiwB,SAAS/b,UACrB3R,EAAEtC,GAAKqH,IAAI,UAFEtH,KAEgBiwB,SAASR,QAFzBzvB,KAGNiwB,SAAS/b,SAAU,KAI1B0c,EAAa,CACfre,KAAM,WACNvE,OAAQ,CACNiiB,SAAU,CACR/b,SAAS,EACTgc,gBAAgB,IAGpB/d,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBiwB,SAAU,CACR/b,SAAS,EACTwc,OAAQlB,EAASkB,OAAOze,KAJfjS,MAKT2wB,QAASnB,EAASmB,QAAQ1e,KALjBjS,MAMTyvB,OAAQD,EAASC,OAAOxd,KANfjS,UAUf4F,GAAI,CACFgjB,KAAM,WACS5oB,KACFgO,OAAOiiB,SAAS/b,SADdlU,KAEJiwB,SAASS,UAGpB7E,QAAS,WACM7rB,KACFiwB,SAAS/b,SADPlU,KAEJiwB,SAASU,aA6BxB,IAAIE,EAAa,CACfC,eAAgBzkB,EAAMM,MACtBvF,OACoD,EAA9C9F,EAAIE,UAAUC,UAAUqB,QAAQ,WAA0B,iBA1BlE,WACE,IAAI4N,EAAY,UACZqgB,EAAcrgB,KAAazQ,EAE/B,IAAK8wB,EAAa,CAChB,IAAIC,EAAU/wB,EAAIa,cAAc,OAChCkwB,EAAQ9vB,aAAawP,EAAW,WAChCqgB,EAA4C,mBAAvBC,EAAQtgB,GAc/B,OAXKqgB,GACA9wB,EAAIgxB,gBACJhxB,EAAIgxB,eAAeC,aAGuB,IAA1CjxB,EAAIgxB,eAAeC,WAAW,GAAI,MAGrCH,EAAc9wB,EAAIgxB,eAAeC,WAAW,eAAgB,QAGvDH,EAMEI,GAAqB,QAAU,aAExCnU,UAAW,SAAmB3W,GAE5B,IAII+qB,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EAkDT,MA/CI,WAAYlrB,IACdgrB,EAAKhrB,EAAEuB,QAEL,eAAgBvB,IAClBgrB,GAAMhrB,EAAEmrB,WAAa,KAEnB,gBAAiBnrB,IACnBgrB,GAAMhrB,EAAEorB,YAAc,KAEpB,gBAAiBprB,IACnB+qB,GAAM/qB,EAAEqrB,YAAc,KAIpB,SAAUrrB,GAAKA,EAAEwG,OAASxG,EAAEsrB,kBAC9BP,EAAKC,EACLA,EAAK,GAGPC,EA7BiB,GA6BZF,EACLG,EA9BiB,GA8BZF,EAED,WAAYhrB,IACdkrB,EAAKlrB,EAAEurB,QAEL,WAAYvrB,IACdirB,EAAKjrB,EAAEwrB,SAGJP,GAAMC,IAAOlrB,EAAEyrB,YACE,IAAhBzrB,EAAEyrB,WACJR,GAxCc,GAyCdC,GAzCc,KA2CdD,GA1Cc,IA2CdC,GA3Cc,MAgDdD,IAAOF,IACTA,EAAME,EAAK,GAAM,EAAI,GAEnBC,IAAOF,IACTA,EAAME,EAAK,GAAM,EAAI,GAGhB,CACLQ,MAAOX,EACPY,MAAOX,EACPY,OAAQX,EACRY,OAAQX,IAGZY,iBAAkB,WACHnyB,KACNoyB,cAAe,GAExBC,iBAAkB,WACHryB,KACNoyB,cAAe,GAExB3C,OAAQ,SAAgBroB,GACtB,IAAIf,EAAIe,EACJ8L,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOskB,WAE3B,IAAKpf,EAAOkf,eAAiBpkB,EAAOukB,eAAkB,OAAO,EAEzDlsB,EAAEqb,gBAAiBrb,EAAIA,EAAEqb,eAC7B,IAAI8Q,EAAQ,EACRC,EAAYvf,EAAOY,cAAgB,EAAI,EAEvC9O,EAAO6rB,EAAW7T,UAAU3W,GAEhC,GAAI2H,EAAO0kB,YACT,GAAIxf,EAAOI,eAAgB,CACzB,KAAIoC,KAAKwB,IAAIlS,EAAKitB,QAAUvc,KAAKwB,IAAIlS,EAAKktB,SACnC,OAAO,EADuCM,EAAQxtB,EAAKitB,OAASQ,MAEtE,CAAA,KAAI/c,KAAKwB,IAAIlS,EAAKktB,QAAUxc,KAAKwB,IAAIlS,EAAKitB,SAC1C,OAAO,EAD8CO,EAAQxtB,EAAKktB,YAGzEM,EAAQ9c,KAAKwB,IAAIlS,EAAKitB,QAAUvc,KAAKwB,IAAIlS,EAAKktB,SAAWltB,EAAKitB,OAASQ,GAAaztB,EAAKktB,OAG3F,GAAc,IAAVM,EAAe,OAAO,EAI1B,GAFIxkB,EAAO2kB,SAAUH,GAASA,GAEzBtf,EAAOlF,OAAOiT,SAaZ,CAED/N,EAAOlF,OAAO4L,MAChB1G,EAAO2J,UAET,IAAIiI,EAAW5R,EAAOtG,eAAkB4lB,EAAQxkB,EAAO4kB,YACnDtZ,EAAepG,EAAOkG,YACtBG,EAASrG,EAAOmG,MA2BpB,GAzBIyL,GAAY5R,EAAO2F,iBAAkBiM,EAAW5R,EAAO2F,gBACvDiM,GAAY5R,EAAOiG,iBAAkB2L,EAAW5R,EAAOiG,gBAE3DjG,EAAOiF,cAAc,GACrBjF,EAAOiI,aAAa2J,GACpB5R,EAAO+F,iBACP/F,EAAOmH,oBACPnH,EAAOsG,wBAEDF,GAAgBpG,EAAOkG,cAAkBG,GAAUrG,EAAOmG,QAC9DnG,EAAOsG,sBAGLtG,EAAOlF,OAAOwY,iBAChBtkB,aAAagR,EAAOof,WAAWO,SAC/B3f,EAAOof,WAAWO,QAAUxmB,EAAMI,SAAS,WACzCyG,EAAOqK,kBACN,MAGLrK,EAAO/B,KAAK,SAAU9K,GAGlB6M,EAAOlF,OAAO8kB,UAAY5f,EAAOlF,OAAO+kB,8BAAgC7f,EAAO4f,SAASE,OAExFlO,IAAa5R,EAAO2F,gBAAkBiM,IAAa5R,EAAOiG,eAAkB,OAAO,MA/C5D,CAC3B,GAAqD,GAAjD9M,EAAMM,MAAQuG,EAAOof,WAAWxB,eAClC,GAAI0B,EAAQ,EACV,GAAMtf,EAAOmG,QAASnG,EAAOlF,OAAO4L,MAAU1G,EAAO4I,WAG9C,GAAI9N,EAAOukB,eAAkB,OAAO,OAFzCrf,EAAO0J,YACP1J,EAAO/B,KAAK,SAAU9K,QAEnB,GAAM6M,EAAOkG,cAAelG,EAAOlF,OAAO4L,MAAU1G,EAAO4I,WAG3D,GAAI9N,EAAOukB,eAAkB,OAAO,OAFzCrf,EAAO6J,YACP7J,EAAO/B,KAAK,SAAU9K,GAG1B6M,EAAOof,WAAWxB,gBAAiB,IAAKxvB,EAAIS,MAAQkxB,UAwCtD,OAFI5sB,EAAEid,eAAkBjd,EAAEid,iBACnBjd,EAAEoqB,aAAc,GAChB,GAETC,OAAQ,WACN,IAAIxd,EAASlT,KACb,IAAK6wB,EAAWzpB,MAAS,OAAO,EAChC,GAAI8L,EAAOof,WAAWpe,QAAW,OAAO,EACxC,IAAI5N,EAAS4M,EAAOC,IAQpB,MAP8C,cAA1CD,EAAOlF,OAAOskB,WAAWY,eAC3B5sB,EAAS/D,EAAE2Q,EAAOlF,OAAOskB,WAAWY,eAEtC5sB,EAAOV,GAAG,aAAcsN,EAAOof,WAAWH,kBAC1C7rB,EAAOV,GAAG,aAAcsN,EAAOof,WAAWD,kBAC1C/rB,EAAOV,GAAGirB,EAAWzpB,MAAO8L,EAAOof,WAAW7C,QAC9Cvc,EAAOof,WAAWpe,SAAU,GAG9Byc,QAAS,WACP,IAAIzd,EAASlT,KACb,IAAK6wB,EAAWzpB,MAAS,OAAO,EAChC,IAAK8L,EAAOof,WAAWpe,QAAW,OAAO,EACzC,IAAI5N,EAAS4M,EAAOC,IAMpB,MAL8C,cAA1CD,EAAOlF,OAAOskB,WAAWY,eAC3B5sB,EAAS/D,EAAE2Q,EAAOlF,OAAOskB,WAAWY,eAEtC5sB,EAAOgB,IAAIupB,EAAWzpB,MAAO8L,EAAOof,WAAW7C,UAC/Cvc,EAAOof,WAAWpe,SAAU,KA2C5Bif,EAAa,CACfrgB,OAAQ,WAEN,IAAII,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOwb,WAE3B,IAAItW,EAAOlF,OAAO4L,KAAlB,CACA,IAAImU,EAAM7a,EAAOsW,WACb4J,EAAUrF,EAAIqF,QACdC,EAAUtF,EAAIsF,QAEdA,GAA4B,EAAjBA,EAAQ/wB,SACjB4Q,EAAOkG,YACTia,EAAQ1vB,SAASqK,EAAOslB,eAExBD,EAAQpvB,YAAY+J,EAAOslB,eAE7BD,EAAQngB,EAAOlF,OAAO0J,eAAiBxE,EAAOuL,SAAW,WAAa,eAAezQ,EAAOulB,YAE1FH,GAA4B,EAAjBA,EAAQ9wB,SACjB4Q,EAAOmG,MACT+Z,EAAQzvB,SAASqK,EAAOslB,eAExBF,EAAQnvB,YAAY+J,EAAOslB,eAE7BF,EAAQlgB,EAAOlF,OAAO0J,eAAiBxE,EAAOuL,SAAW,WAAa,eAAezQ,EAAOulB,cAGhG3K,KAAM,WACJ,IAIIwK,EACAC,EALAngB,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOwb,YACrBxb,EAAOwlB,QAAUxlB,EAAOylB,UAI1BzlB,EAAOwlB,SACTJ,EAAU7wB,EAAEyL,EAAOwlB,QAEjBtgB,EAAOlF,OAAO6a,mBACc,iBAAlB7a,EAAOwlB,QACG,EAAjBJ,EAAQ9wB,QACkC,IAA1C4Q,EAAOC,IAAIxH,KAAKqC,EAAOwlB,QAAQlxB,SAElC8wB,EAAUlgB,EAAOC,IAAIxH,KAAKqC,EAAOwlB,UAGjCxlB,EAAOylB,SACTJ,EAAU9wB,EAAEyL,EAAOylB,QAEjBvgB,EAAOlF,OAAO6a,mBACc,iBAAlB7a,EAAOylB,QACG,EAAjBJ,EAAQ/wB,QACkC,IAA1C4Q,EAAOC,IAAIxH,KAAKqC,EAAOylB,QAAQnxB,SAElC+wB,EAAUngB,EAAOC,IAAIxH,KAAKqC,EAAOylB,UAIjCL,GAA4B,EAAjBA,EAAQ9wB,QACrB8wB,EAAQxtB,GAAG,QAAS,SAAUS,GAC5BA,EAAEid,iBACEpQ,EAAOmG,QAAUnG,EAAOlF,OAAO4L,MACnC1G,EAAO0J,cAGPyW,GAA4B,EAAjBA,EAAQ/wB,QACrB+wB,EAAQztB,GAAG,QAAS,SAAUS,GAC5BA,EAAEid,iBACEpQ,EAAOkG,cAAgBlG,EAAOlF,OAAO4L,MACzC1G,EAAO6J,cAIX1Q,EAAMqC,OAAOwE,EAAOsW,WAAY,CAC9B4J,QAASA,EACTI,OAAQJ,GAAWA,EAAQ,GAC3BC,QAASA,EACTI,OAAQJ,GAAWA,EAAQ,OAG/BxH,QAAS,WACP,IACIkC,EADS/tB,KACIwpB,WACb4J,EAAUrF,EAAIqF,QACdC,EAAUtF,EAAIsF,QACdD,GAAWA,EAAQ9wB,SACrB8wB,EAAQ9rB,IAAI,SACZ8rB,EAAQnvB,YANGjE,KAMgBgO,OAAOwb,WAAW8J,gBAE3CD,GAAWA,EAAQ/wB,SACrB+wB,EAAQ/rB,IAAI,SACZ+rB,EAAQpvB,YAVGjE,KAUgBgO,OAAOwb,WAAW8J,kBA+D/CI,EAAa,CACf5gB,OAAQ,WAEN,IAAII,EAASlT,KACT6T,EAAMX,EAAOW,IACb7F,EAASkF,EAAOlF,OAAO2lB,WAC3B,GAAK3lB,EAAO9I,IAAOgO,EAAOygB,WAAWzuB,IAAOgO,EAAOygB,WAAWxgB,KAAwC,IAAjCD,EAAOygB,WAAWxgB,IAAI7Q,OAA3F,CACA,IAGIsxB,EAHAvf,EAAenB,EAAOe,SAAWf,EAAOlF,OAAOiG,QAAQC,QAAUhB,EAAOe,QAAQG,OAAO9R,OAAS4Q,EAAOkB,OAAO9R,OAC9G6Q,EAAMD,EAAOygB,WAAWxgB,IAGxB0gB,EAAQ3gB,EAAOlF,OAAO4L,KAAOlE,KAAKE,MAAMvB,EAAsC,EAAtBnB,EAAOyJ,cAAqBzJ,EAAOlF,OAAOmJ,gBAAkBjE,EAAOoB,SAAShS,OAcxI,GAbI4Q,EAAOlF,OAAO4L,OAChBga,EAAUle,KAAKE,MAAM1C,EAAOkF,YAAclF,EAAOyJ,cAAgBzJ,EAAOlF,OAAOmJ,iBACjE9C,EAAe,EAA2B,EAAtBnB,EAAOyJ,eACvCiX,GAAYvf,EAAsC,EAAtBnB,EAAOyJ,cAEvBkX,EAAQ,EAAlBD,IAAuBA,GAAWC,GAClCD,EAAU,GAAsC,YAAjC1gB,EAAOlF,OAAO8lB,iBAAgCF,EAAUC,EAAQD,IAEnFA,OADqC,IAArB1gB,EAAOqH,UACbrH,EAAOqH,UAEPrH,EAAOkF,aAAe,EAGd,YAAhBpK,EAAO4T,MAAsB1O,EAAOygB,WAAWI,SAA8C,EAAnC7gB,EAAOygB,WAAWI,QAAQzxB,OAAY,CAClG,IACI0xB,EACAC,EACAC,EAHAH,EAAU7gB,EAAOygB,WAAWI,QAoBhC,GAhBI/lB,EAAOmmB,iBACTjhB,EAAOygB,WAAWS,WAAaL,EAAQzpB,GAAG,GAAG4I,EAAOI,eAAiB,aAAe,gBAAe,GACnGH,EAAI1J,IAAIyJ,EAAOI,eAAiB,QAAU,SAAYJ,EAAOygB,WAAWS,YAAcpmB,EAAOqmB,mBAAqB,GAAM,MACxF,EAA5BrmB,EAAOqmB,yBAAmDttB,IAAzBmM,EAAOsH,gBAC1CtH,EAAOygB,WAAWW,oBAAuBV,EAAU1gB,EAAOsH,cACtDtH,EAAOygB,WAAWW,mBAAsBtmB,EAAOqmB,mBAAqB,EACtEnhB,EAAOygB,WAAWW,mBAAqBtmB,EAAOqmB,mBAAqB,EAC1DnhB,EAAOygB,WAAWW,mBAAqB,IAChDphB,EAAOygB,WAAWW,mBAAqB,IAG3CN,EAAaJ,EAAU1gB,EAAOygB,WAAWW,mBAEzCJ,IADAD,EAAYD,GAActe,KAAKyL,IAAI4S,EAAQzxB,OAAQ0L,EAAOqmB,oBAAsB,IACxDL,GAAc,GAExCD,EAAQ9vB,YAAc+J,EAAwB,kBAAI,IAAOA,EAAwB,kBAAI,SAAYA,EAAwB,kBAAI,cAAiBA,EAAwB,kBAAI,SAAYA,EAAwB,kBAAI,cAAiBA,EAAwB,kBAAI,SAC9O,EAAbmF,EAAI7Q,OACNyxB,EAAQnqB,KAAK,SAAUO,EAAOoqB,GAC5B,IAAIC,EAAUjyB,EAAEgyB,GACZE,EAAcD,EAAQrqB,QACtBsqB,IAAgBb,GAClBY,EAAQ7wB,SAASqK,EAAO0mB,mBAEtB1mB,EAAOmmB,iBACUH,GAAfS,GAA6BA,GAAeR,GAC9CO,EAAQ7wB,SAAWqK,EAAwB,kBAAI,SAE7CymB,IAAgBT,GAClBQ,EACGppB,OACAzH,SAAWqK,EAAwB,kBAAI,SACvC5C,OACAzH,SAAWqK,EAAwB,kBAAI,cAExCymB,IAAgBR,GAClBO,EACGxpB,OACArH,SAAWqK,EAAwB,kBAAI,SACvChD,OACArH,SAAWqK,EAAwB,kBAAI,sBAOhD,GAFc+lB,EAAQzpB,GAAGspB,GACjBjwB,SAASqK,EAAO0mB,mBACpB1mB,EAAOmmB,eAAgB,CAGzB,IAFA,IAAIQ,EAAwBZ,EAAQzpB,GAAG0pB,GACnCY,EAAuBb,EAAQzpB,GAAG2pB,GAC7B5xB,EAAI2xB,EAAY3xB,GAAK4xB,EAAW5xB,GAAK,EAC5C0xB,EAAQzpB,GAAGjI,GAAGsB,SAAWqK,EAAwB,kBAAI,SAEvD2mB,EACGvpB,OACAzH,SAAWqK,EAAwB,kBAAI,SACvC5C,OACAzH,SAAWqK,EAAwB,kBAAI,cAC1C4mB,EACG5pB,OACArH,SAAWqK,EAAwB,kBAAI,SACvChD,OACArH,SAAWqK,EAAwB,kBAAI,cAG9C,GAAIA,EAAOmmB,eAAgB,CACzB,IAAIU,EAAuBnf,KAAKyL,IAAI4S,EAAQzxB,OAAQ0L,EAAOqmB,mBAAqB,GAC5ES,GAAmB5hB,EAAOygB,WAAWS,WAAaS,EAAyB3hB,EAAOygB,WAAqB,YAAK,EAAMO,EAAWhhB,EAAOygB,WAAWS,WAC/I7F,EAAa1a,EAAM,QAAU,OACjCkgB,EAAQtqB,IAAIyJ,EAAOI,eAAiBib,EAAa,MAAQuG,EAAgB,OAO7E,GAJoB,aAAhB9mB,EAAO4T,OACTzO,EAAIxH,KAAM,IAAOqC,EAAmB,cAAInE,KAAKmE,EAAO+mB,sBAAsBnB,EAAU,IACpFzgB,EAAIxH,KAAM,IAAOqC,EAAiB,YAAInE,KAAKmE,EAAOgnB,oBAAoBnB,KAEpD,gBAAhB7lB,EAAO4T,KAAwB,CACjC,IAAIqT,EAEFA,EADEjnB,EAAOknB,oBACchiB,EAAOI,eAAiB,WAAa,aAErCJ,EAAOI,eAAiB,aAAe,WAEhE,IAAI6hB,GAASvB,EAAU,GAAKC,EACxBuB,EAAS,EACTC,EAAS,EACgB,eAAzBJ,EACFG,EAASD,EAETE,EAASF,EAEXhiB,EAAIxH,KAAM,IAAOqC,EAA2B,sBAAI3I,UAAW,6BAA+B+vB,EAAS,YAAcC,EAAS,KAAM7vB,WAAW0N,EAAOlF,OAAOgK,OAEvI,WAAhBhK,EAAO4T,MAAqB5T,EAAOsnB,cACrCniB,EAAIvQ,KAAKoL,EAAOsnB,aAAapiB,EAAQ0gB,EAAU,EAAGC,IAClD3gB,EAAO/B,KAAK,mBAAoB+B,EAAQC,EAAI,KAE5CD,EAAO/B,KAAK,mBAAoB+B,EAAQC,EAAI,IAE9CA,EAAID,EAAOlF,OAAO0J,eAAiBxE,EAAOuL,SAAW,WAAa,eAAezQ,EAAOulB,aAE1FgC,OAAQ,WAEN,IAAIriB,EAASlT,KACTgO,EAASkF,EAAOlF,OAAO2lB,WAC3B,GAAK3lB,EAAO9I,IAAOgO,EAAOygB,WAAWzuB,IAAOgO,EAAOygB,WAAWxgB,KAAwC,IAAjCD,EAAOygB,WAAWxgB,IAAI7Q,OAA3F,CACA,IAAI+R,EAAenB,EAAOe,SAAWf,EAAOlF,OAAOiG,QAAQC,QAAUhB,EAAOe,QAAQG,OAAO9R,OAAS4Q,EAAOkB,OAAO9R,OAE9G6Q,EAAMD,EAAOygB,WAAWxgB,IACxBqiB,EAAiB,GACrB,GAAoB,YAAhBxnB,EAAO4T,KAAoB,CAE7B,IADA,IAAI6T,EAAkBviB,EAAOlF,OAAO4L,KAAOlE,KAAKE,MAAMvB,EAAsC,EAAtBnB,EAAOyJ,cAAqBzJ,EAAOlF,OAAOmJ,gBAAkBjE,EAAOoB,SAAShS,OACzID,EAAI,EAAGA,EAAIozB,EAAiBpzB,GAAK,EACpC2L,EAAO0nB,aACTF,GAAkBxnB,EAAO0nB,aAAaptB,KAAK4K,EAAQ7Q,EAAG2L,EAAO2nB,aAE7DH,GAAkB,IAAOxnB,EAAoB,cAAI,WAAeA,EAAkB,YAAI,OAAWA,EAAoB,cAAI,IAG7HmF,EAAIvQ,KAAK4yB,GACTtiB,EAAOygB,WAAWI,QAAU5gB,EAAIxH,KAAM,IAAOqC,EAAkB,aAE7C,aAAhBA,EAAO4T,OAEP4T,EADExnB,EAAO4nB,eACQ5nB,EAAO4nB,eAAettB,KAAK4K,EAAQlF,EAAO6nB,aAAc7nB,EAAO8nB,YAE/D,gBAAoB9nB,EAAmB,aAAI,4BAEtCA,EAAiB,WAAI,YAE7CmF,EAAIvQ,KAAK4yB,IAES,gBAAhBxnB,EAAO4T,OAEP4T,EADExnB,EAAO+nB,kBACQ/nB,EAAO+nB,kBAAkBztB,KAAK4K,EAAQlF,EAAOgoB,sBAE7C,gBAAoBhoB,EAA2B,qBAAI,YAEtEmF,EAAIvQ,KAAK4yB,IAES,WAAhBxnB,EAAO4T,MACT1O,EAAO/B,KAAK,mBAAoB+B,EAAOygB,WAAWxgB,IAAI,MAG1DyV,KAAM,WACJ,IAAI1V,EAASlT,KACTgO,EAASkF,EAAOlF,OAAO2lB,WAC3B,GAAK3lB,EAAO9I,GAAZ,CAEA,IAAIiO,EAAM5Q,EAAEyL,EAAO9I,IACA,IAAfiO,EAAI7Q,SAGN4Q,EAAOlF,OAAO6a,mBACU,iBAAd7a,EAAO9I,IACD,EAAbiO,EAAI7Q,QACkC,IAAtC4Q,EAAOC,IAAIxH,KAAKqC,EAAO9I,IAAI5C,SAE9B6Q,EAAMD,EAAOC,IAAIxH,KAAKqC,EAAO9I,KAGX,YAAhB8I,EAAO4T,MAAsB5T,EAAOioB,WACtC9iB,EAAIxP,SAASqK,EAAOkoB,gBAGtB/iB,EAAIxP,SAASqK,EAAOmoB,cAAgBnoB,EAAO4T,MAEvB,YAAhB5T,EAAO4T,MAAsB5T,EAAOmmB,iBACtChhB,EAAIxP,SAAU,GAAMqK,EAAoB,cAAKA,EAAW,KAAI,YAC5DkF,EAAOygB,WAAWW,mBAAqB,EACnCtmB,EAAOqmB,mBAAqB,IAC9BrmB,EAAOqmB,mBAAqB,IAGZ,gBAAhBrmB,EAAO4T,MAA0B5T,EAAOknB,qBAC1C/hB,EAAIxP,SAASqK,EAAOooB,0BAGlBpoB,EAAOioB,WACT9iB,EAAIvN,GAAG,QAAU,IAAOoI,EAAkB,YAAI,SAAiB3H,GAC7DA,EAAEid,iBACF,IAAInZ,EAAQ5H,EAAEvC,MAAMmK,QAAU+I,EAAOlF,OAAOmJ,eACxCjE,EAAOlF,OAAO4L,OAAQzP,GAAS+I,EAAOyJ,cAC1CzJ,EAAO6I,QAAQ5R,KAInBkC,EAAMqC,OAAOwE,EAAOygB,WAAY,CAC9BxgB,IAAKA,EACLjO,GAAIiO,EAAI,QAGZ0Y,QAAS,WACP,IAAI3Y,EAASlT,KACTgO,EAASkF,EAAOlF,OAAO2lB,WAC3B,GAAK3lB,EAAO9I,IAAOgO,EAAOygB,WAAWzuB,IAAOgO,EAAOygB,WAAWxgB,KAAwC,IAAjCD,EAAOygB,WAAWxgB,IAAI7Q,OAA3F,CACA,IAAI6Q,EAAMD,EAAOygB,WAAWxgB,IAE5BA,EAAIlP,YAAY+J,EAAOqoB,aACvBljB,EAAIlP,YAAY+J,EAAOmoB,cAAgBnoB,EAAO4T,MAC1C1O,EAAOygB,WAAWI,SAAW7gB,EAAOygB,WAAWI,QAAQ9vB,YAAY+J,EAAO0mB,mBAC1E1mB,EAAOioB,WACT9iB,EAAI7L,IAAI,QAAU,IAAO0G,EAAkB,gBAoG7CsoB,EAAY,CACdnb,aAAc,WACZ,IAAIjI,EAASlT,KACb,GAAKkT,EAAOlF,OAAOuoB,UAAUrxB,IAAOgO,EAAOqjB,UAAUrxB,GAArD,CACA,IAAIqxB,EAAYrjB,EAAOqjB,UACnB1iB,EAAMX,EAAOY,aACbkF,EAAW9F,EAAO8F,SAClBwd,EAAWD,EAAUC,SACrBC,EAAYF,EAAUE,UACtBC,EAAUH,EAAUG,QACpBvjB,EAAMojB,EAAUpjB,IAChBnF,EAASkF,EAAOlF,OAAOuoB,UAEvBI,EAAUH,EACVI,GAAUH,EAAYD,GAAYxd,EAClCnF,EAEW,GADb+iB,GAAUA,IAERD,EAAUH,EAAWI,EACrBA,EAAS,GACqBH,GAApBG,EAASJ,IACnBG,EAAUF,EAAYG,GAEfA,EAAS,GAClBD,EAAUH,EAAWI,EACrBA,EAAS,GACoBH,EAApBG,EAASJ,IAClBG,EAAUF,EAAYG,GAEpB1jB,EAAOI,gBACLlE,EAAQS,aACV6mB,EAAQrxB,UAAW,eAAiBuxB,EAAS,aAE7CF,EAAQrxB,UAAW,cAAgBuxB,EAAS,OAE9CF,EAAQ,GAAGz1B,MAAM+R,MAAQ2jB,EAAU,OAE/BvnB,EAAQS,aACV6mB,EAAQrxB,UAAW,oBAAsBuxB,EAAS,UAElDF,EAAQrxB,UAAW,cAAgBuxB,EAAS,OAE9CF,EAAQ,GAAGz1B,MAAMgS,OAAS0jB,EAAU,MAElC3oB,EAAO6oB,OACT30B,aAAagR,EAAOqjB,UAAU1D,SAC9B1f,EAAI,GAAGlS,MAAM61B,QAAU,EACvB5jB,EAAOqjB,UAAU1D,QAAU5wB,WAAW,WACpCkR,EAAI,GAAGlS,MAAM61B,QAAU,EACvB3jB,EAAI3N,WAAW,MACd,QAGP2S,cAAe,SAAuB1S,GACvBzF,KACDgO,OAAOuoB,UAAUrxB,IADhBlF,KAC8Bu2B,UAAUrxB,IADxClF,KAENu2B,UAAUG,QAAQlxB,WAAWC,IAEtCsN,WAAY,WACV,IAAIG,EAASlT,KACb,GAAKkT,EAAOlF,OAAOuoB,UAAUrxB,IAAOgO,EAAOqjB,UAAUrxB,GAArD,CAEA,IAAIqxB,EAAYrjB,EAAOqjB,UACnBG,EAAUH,EAAUG,QACpBvjB,EAAMojB,EAAUpjB,IAEpBujB,EAAQ,GAAGz1B,MAAM+R,MAAQ,GACzB0jB,EAAQ,GAAGz1B,MAAMgS,OAAS,GAC1B,IAIIujB,EAJAC,EAAYvjB,EAAOI,eAAiBH,EAAI,GAAGzK,YAAcyK,EAAI,GAAGtK,aAEhEkuB,EAAU7jB,EAAOO,KAAOP,EAAOkC,YAC/B4hB,EAAcD,GAAWN,EAAYvjB,EAAOO,MAG9C+iB,EADuC,SAArCtjB,EAAOlF,OAAOuoB,UAAUC,SACfC,EAAYM,EAEZvjB,SAASN,EAAOlF,OAAOuoB,UAAUC,SAAU,IAGpDtjB,EAAOI,eACTojB,EAAQ,GAAGz1B,MAAM+R,MAAQwjB,EAAW,KAEpCE,EAAQ,GAAGz1B,MAAMgS,OAASujB,EAAW,KAIrCrjB,EAAI,GAAGlS,MAAMg2B,QADA,GAAXF,EACqB,OAEA,GAErB7jB,EAAOlF,OAAOkpB,gBAChB/jB,EAAI,GAAGlS,MAAM61B,QAAU,GAEzBzqB,EAAMqC,OAAO6nB,EAAW,CACtBE,UAAWA,EACXM,QAASA,EACTC,YAAaA,EACbR,SAAUA,IAEZD,EAAUpjB,IAAID,EAAOlF,OAAO0J,eAAiBxE,EAAOuL,SAAW,WAAa,eAAevL,EAAOlF,OAAOuoB,UAAUhD,aAErH4D,gBAAiB,SAAyB9wB,GACxC,IAaI+wB,EAbAlkB,EAASlT,KACTu2B,EAAYrjB,EAAOqjB,UACnB1iB,EAAMX,EAAOY,aACbX,EAAMojB,EAAUpjB,IAChBqjB,EAAWD,EAAUC,SACrBC,EAAYF,EAAUE,UAS1BW,IANIlkB,EAAOI,eACsB,eAAXjN,EAAEub,MAAoC,cAAXvb,EAAEub,KAAwBvb,EAAEgc,cAAc,GAAGC,MAAQjc,EAAEic,OAASjc,EAAEgxB,QAElF,eAAXhxB,EAAEub,MAAoC,cAAXvb,EAAEub,KAAwBvb,EAAEgc,cAAc,GAAGG,MAAQnc,EAAEmc,OAASnc,EAAEixB,SAG9EnkB,EAAIrK,SAASoK,EAAOI,eAAiB,OAAS,OAAUkjB,EAAW,IAAOC,EAAYD,GAC3HY,EAAgB1hB,KAAKK,IAAIL,KAAKyL,IAAIiW,EAAe,GAAI,GACjDvjB,IACFujB,EAAgB,EAAIA,GAGtB,IAAItS,EAAW5R,EAAO2F,gBAAmB3F,EAAOiG,eAAiBjG,EAAO2F,gBAAkBue,EAE1FlkB,EAAO+F,eAAe6L,GACtB5R,EAAOiI,aAAa2J,GACpB5R,EAAOmH,oBACPnH,EAAOsG,uBAET+d,YAAa,SAAqBlxB,GAChC,IAAI6M,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOuoB,UACvBA,EAAYrjB,EAAOqjB,UACnB5iB,EAAaT,EAAOS,WACpBR,EAAMojB,EAAUpjB,IAChBujB,EAAUH,EAAUG,QACxBxjB,EAAOqjB,UAAUzU,WAAY,EAC7Bzb,EAAEid,iBACFjd,EAAEge,kBAEF1Q,EAAWnO,WAAW,KACtBkxB,EAAQlxB,WAAW,KACnB+wB,EAAUY,gBAAgB9wB,GAE1BnE,aAAagR,EAAOqjB,UAAUiB,aAE9BrkB,EAAI3N,WAAW,GACXwI,EAAO6oB,MACT1jB,EAAI1J,IAAI,UAAW,GAErByJ,EAAO/B,KAAK,qBAAsB9K,IAEpCoxB,WAAY,SAAoBpxB,GAC9B,IACIkwB,EADSv2B,KACUu2B,UACnB5iB,EAFS3T,KAEW2T,WACpBR,EAAMojB,EAAUpjB,IAChBujB,EAAUH,EAAUG,QAJX12B,KAMDu2B,UAAUzU,YAClBzb,EAAEid,eAAkBjd,EAAEid,iBACnBjd,EAAEoqB,aAAc,EACvB8F,EAAUY,gBAAgB9wB,GAC1BsN,EAAWnO,WAAW,GACtB2N,EAAI3N,WAAW,GACfkxB,EAAQlxB,WAAW,GAZNxF,KAaNmR,KAAK,oBAAqB9K,KAEnCqxB,UAAW,SAAmBrxB,GAC5B,IAAI6M,EAASlT,KAETgO,EAASkF,EAAOlF,OAAOuoB,UAEvBpjB,EADYD,EAAOqjB,UACHpjB,IAEfD,EAAOqjB,UAAUzU,YACtB5O,EAAOqjB,UAAUzU,WAAY,EACzB9T,EAAO6oB,OACT30B,aAAagR,EAAOqjB,UAAUiB,aAC9BtkB,EAAOqjB,UAAUiB,YAAcnrB,EAAMI,SAAS,WAC5C0G,EAAI1J,IAAI,UAAW,GACnB0J,EAAI3N,WAAW,MACd,MAEL0N,EAAO/B,KAAK,mBAAoB9K,GAC5B2H,EAAO2pB,eACTzkB,EAAOqK,mBAGXqa,gBAAiB,WACf,IAAI1kB,EAASlT,KACb,GAAKkT,EAAOlF,OAAOuoB,UAAUrxB,GAA7B,CACA,IAAIqxB,EAAYrjB,EAAOqjB,UACnBlV,EAAcnO,EAAOmO,YACrBmK,EAAqBtY,EAAOsY,mBAC5Bxd,EAASkF,EAAOlF,OAEhB1H,EADMiwB,EAAUpjB,IACH,GACb0kB,KAAiBzoB,EAAQa,kBAAmBjC,EAAOsZ,mBAAmB,CAAEC,SAAS,EAAOphB,SAAS,GACjG8J,KAAkBb,EAAQa,kBAAmBjC,EAAOsZ,mBAAmB,CAAEC,SAAS,EAAMphB,SAAS,GAChGiJ,EAAQC,QAAUD,EAAQI,gBAAiBJ,EAAQO,uBAKlDP,EAAQC,QACV/I,EAAOlG,iBAAiBihB,EAAYgG,MAAOnU,EAAOqjB,UAAUgB,YAAaM,GACzEvxB,EAAOlG,iBAAiBihB,EAAYmG,KAAMtU,EAAOqjB,UAAUkB,WAAYI,GACvEvxB,EAAOlG,iBAAiBihB,EAAYoG,IAAKvU,EAAOqjB,UAAUmB,UAAWznB,KAElEjC,EAAOwQ,gBAAkBgB,EAAOG,MAAQH,EAAOI,SAAa5R,EAAOwQ,gBAAkBpP,EAAQC,OAASmQ,EAAOG,OAChHrZ,EAAOlG,iBAAiB,YAAa8S,EAAOqjB,UAAUgB,YAAaM,GACnE53B,EAAIG,iBAAiB,YAAa8S,EAAOqjB,UAAUkB,WAAYI,GAC/D53B,EAAIG,iBAAiB,UAAW8S,EAAOqjB,UAAUmB,UAAWznB,MAZ9D3J,EAAOlG,iBAAiBorB,EAAmBnE,MAAOnU,EAAOqjB,UAAUgB,YAAaM,GAChF53B,EAAIG,iBAAiBorB,EAAmBhE,KAAMtU,EAAOqjB,UAAUkB,WAAYI,GAC3E53B,EAAIG,iBAAiBorB,EAAmB/D,IAAKvU,EAAOqjB,UAAUmB,UAAWznB,MAc7E6nB,iBAAkB,WAChB,IAAI5kB,EAASlT,KACb,GAAKkT,EAAOlF,OAAOuoB,UAAUrxB,GAA7B,CACA,IAAIqxB,EAAYrjB,EAAOqjB,UACnBlV,EAAcnO,EAAOmO,YACrBmK,EAAqBtY,EAAOsY,mBAC5Bxd,EAASkF,EAAOlF,OAEhB1H,EADMiwB,EAAUpjB,IACH,GACb0kB,KAAiBzoB,EAAQa,kBAAmBjC,EAAOsZ,mBAAmB,CAAEC,SAAS,EAAOphB,SAAS,GACjG8J,KAAkBb,EAAQa,kBAAmBjC,EAAOsZ,mBAAmB,CAAEC,SAAS,EAAMphB,SAAS,GAChGiJ,EAAQC,QAAUD,EAAQI,gBAAiBJ,EAAQO,uBAKlDP,EAAQC,QACV/I,EAAOjG,oBAAoBghB,EAAYgG,MAAOnU,EAAOqjB,UAAUgB,YAAaM,GAC5EvxB,EAAOjG,oBAAoBghB,EAAYmG,KAAMtU,EAAOqjB,UAAUkB,WAAYI,GAC1EvxB,EAAOjG,oBAAoBghB,EAAYoG,IAAKvU,EAAOqjB,UAAUmB,UAAWznB,KAErEjC,EAAOwQ,gBAAkBgB,EAAOG,MAAQH,EAAOI,SAAa5R,EAAOwQ,gBAAkBpP,EAAQC,OAASmQ,EAAOG,OAChHrZ,EAAOjG,oBAAoB,YAAa6S,EAAOqjB,UAAUgB,YAAaM,GACtE53B,EAAII,oBAAoB,YAAa6S,EAAOqjB,UAAUkB,WAAYI,GAClE53B,EAAII,oBAAoB,UAAW6S,EAAOqjB,UAAUmB,UAAWznB,MAZjE3J,EAAOjG,oBAAoBmrB,EAAmBnE,MAAOnU,EAAOqjB,UAAUgB,YAAaM,GACnF53B,EAAII,oBAAoBmrB,EAAmBhE,KAAMtU,EAAOqjB,UAAUkB,WAAYI,GAC9E53B,EAAII,oBAAoBmrB,EAAmB/D,IAAKvU,EAAOqjB,UAAUmB,UAAWznB,MAchF2Y,KAAM,WACJ,IAAI1V,EAASlT,KACb,GAAKkT,EAAOlF,OAAOuoB,UAAUrxB,GAA7B,CACA,IAAIqxB,EAAYrjB,EAAOqjB,UACnBwB,EAAY7kB,EAAOC,IACnBnF,EAASkF,EAAOlF,OAAOuoB,UAEvBpjB,EAAM5Q,EAAEyL,EAAO9I,IACfgO,EAAOlF,OAAO6a,mBAA0C,iBAAd7a,EAAO9I,IAAgC,EAAbiO,EAAI7Q,QAAmD,IAArCy1B,EAAUpsB,KAAKqC,EAAO9I,IAAI5C,SAClH6Q,EAAM4kB,EAAUpsB,KAAKqC,EAAO9I,KAG9B,IAAIwxB,EAAUvjB,EAAIxH,KAAM,IAAOuH,EAAOlF,OAAOuoB,UAAmB,WACzC,IAAnBG,EAAQp0B,SACVo0B,EAAUn0B,EAAG,eAAmB2Q,EAAOlF,OAAOuoB,UAAmB,UAAI,YACrEpjB,EAAI3I,OAAOksB,IAGbrqB,EAAMqC,OAAO6nB,EAAW,CACtBpjB,IAAKA,EACLjO,GAAIiO,EAAI,GACRujB,QAASA,EACTsB,OAAQtB,EAAQ,KAGd1oB,EAAOiqB,WACT1B,EAAUqB,oBAGd/L,QAAS,WACM7rB,KACNu2B,UAAUuB,qBAwEjBI,EAAW,CACbC,aAAc,SAAsBjzB,EAAI8T,GACtC,IACInF,EADS7T,KACI6T,IAEbV,EAAM5Q,EAAE2C,GACRutB,EAAY5e,GAAO,EAAI,EAEvBukB,EAAIjlB,EAAI5O,KAAK,yBAA2B,IACxC8W,EAAIlI,EAAI5O,KAAK,0BACb+W,EAAInI,EAAI5O,KAAK,0BACb4wB,EAAQhiB,EAAI5O,KAAK,8BACjBuyB,EAAU3jB,EAAI5O,KAAK,gCAwBvB,GAtBI8W,GAAKC,GACPD,EAAIA,GAAK,IACTC,EAAIA,GAAK,KAdEtb,KAeKsT,gBAChB+H,EAAI+c,EACJ9c,EAAI,MAEJA,EAAI8c,EACJ/c,EAAI,KAIJA,EADsB,GAApB,EAAIvY,QAAQ,KACT0Q,SAAS6H,EAAG,IAAMrC,EAAWyZ,EAAa,IAE1CpX,EAAIrC,EAAWyZ,EAAa,KAGjCnX,EADsB,GAApB,EAAIxY,QAAQ,KACT0Q,SAAS8H,EAAG,IAAMtC,EAAY,IAE9BsC,EAAItC,EAAY,KAGnB,MAAO8d,EAA6C,CACtD,IAAIuB,EAAiBvB,GAAYA,EAAU,IAAM,EAAIphB,KAAKwB,IAAI8B,IAC9D7F,EAAI,GAAGlS,MAAM61B,QAAUuB,EAEzB,GAAI,MAAOlD,EACThiB,EAAI9N,UAAW,eAAiBgW,EAAI,KAAOC,EAAI,cAC1C,CACL,IAAIgd,EAAenD,GAAUA,EAAQ,IAAM,EAAIzf,KAAKwB,IAAI8B,IACxD7F,EAAI9N,UAAW,eAAiBgW,EAAI,KAAOC,EAAI,gBAAkBgd,EAAe,OAGpFnd,aAAc,WACZ,IAAIjI,EAASlT,KACTmT,EAAMD,EAAOC,IACbiB,EAASlB,EAAOkB,OAChB4E,EAAW9F,EAAO8F,SAClB1E,EAAWpB,EAAOoB,SACtBnB,EAAIpS,SAAS,8EACV6I,KAAK,SAAUO,EAAOjF,GACrBgO,EAAOqlB,SAASJ,aAAajzB,EAAI8T,KAErC5E,EAAOxK,KAAK,SAAUqS,EAAYuc,GAChC,IAAI5f,EAAgB4f,EAAQxf,SACO,EAA/B9F,EAAOlF,OAAOmJ,gBAAsD,SAAhCjE,EAAOlF,OAAO6H,gBACpD+C,GAAiBlD,KAAKE,KAAKqG,EAAa,GAAMjD,GAAY1E,EAAShS,OAAS,IAE9EsW,EAAgBlD,KAAKyL,IAAIzL,KAAKK,IAAI6C,GAAgB,GAAI,GACtDrW,EAAEi2B,GAAS7sB,KAAK,8EACb/B,KAAK,SAAUO,EAAOjF,GACrBgO,EAAOqlB,SAASJ,aAAajzB,EAAI0T,QAIzCT,cAAe,SAAuB1S,QAClB,IAAbA,IAAsBA,EAAWzF,KAAKgO,OAAOgK,OAErChY,KACImT,IACbxH,KAAK,8EACN/B,KAAK,SAAUO,EAAOsuB,GACrB,IAAIC,EAAcn2B,EAAEk2B,GAChBE,EAAmBnlB,SAASklB,EAAYn0B,KAAK,iCAAkC,KAAOkB,EACzE,IAAbA,IAAkBkzB,EAAmB,GACzCD,EAAYlzB,WAAWmzB,OA8C3BC,EAAO,CAETC,0BAA2B,SAAmCxyB,GAC5D,GAAIA,EAAEgc,cAAc/f,OAAS,EAAK,OAAO,EACzC,IAAIw2B,EAAKzyB,EAAEgc,cAAc,GAAGC,MACxByW,EAAK1yB,EAAEgc,cAAc,GAAGG,MACxBwW,EAAK3yB,EAAEgc,cAAc,GAAGC,MACxB2W,EAAK5yB,EAAEgc,cAAc,GAAGG,MAE5B,OADe9M,KAAKqO,KAAMrO,KAAKsO,IAAMgV,EAAKF,EAAK,GAAQpjB,KAAKsO,IAAMiV,EAAKF,EAAK,KAI9EG,eAAgB,SAAwB7yB,GACtC,IAAI6M,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOmrB,KACvBA,EAAOjmB,EAAOimB,KACdC,EAAUD,EAAKC,QAGnB,GAFAD,EAAKE,oBAAqB,EAC1BF,EAAKG,kBAAmB,GACnBlqB,EAAQkB,SAAU,CACrB,GAAe,eAAXjK,EAAEub,MAAqC,eAAXvb,EAAEub,MAAyBvb,EAAEgc,cAAc/f,OAAS,EAClF,OAEF62B,EAAKE,oBAAqB,EAC1BD,EAAQG,WAAaX,EAAKC,0BAA0BxyB,GAEjD+yB,EAAQlK,UAAakK,EAAQlK,SAAS5sB,SACzC82B,EAAQlK,SAAW3sB,EAAE8D,EAAEC,QAAQoF,QAAQ,iBACP,IAA5B0tB,EAAQlK,SAAS5sB,SAAgB82B,EAAQlK,SAAWhc,EAAOkB,OAAO9J,GAAG4I,EAAOkF,cAChFghB,EAAQI,SAAWJ,EAAQlK,SAASvjB,KAAK,oBACzCytB,EAAQK,aAAeL,EAAQI,SAAShuB,OAAQ,IAAOwC,EAAqB,gBAC5EorB,EAAQM,SAAWN,EAAQK,aAAal1B,KAAK,qBAAuByJ,EAAO0rB,SACvC,IAAhCN,EAAQK,aAAan3B,SAK3B82B,EAAQI,SAASh0B,WAAW,GAC5B0N,EAAOimB,KAAKQ,WAAY,GALpBP,EAAQI,cAAWzyB,GAOzB6yB,gBAAiB,SAAyBvzB,GACxC,IACI2H,EADShO,KACOgO,OAAOmrB,KACvBA,EAFSn5B,KAEKm5B,KACdC,EAAUD,EAAKC,QACnB,IAAKhqB,EAAQkB,SAAU,CACrB,GAAe,cAAXjK,EAAEub,MAAoC,cAAXvb,EAAEub,MAAwBvb,EAAEgc,cAAc/f,OAAS,EAChF,OAEF62B,EAAKG,kBAAmB,EACxBF,EAAQS,UAAYjB,EAAKC,0BAA0BxyB,GAEhD+yB,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl3B,SACtC8M,EAAQkB,SAZCtQ,KAaJm5B,KAAKhE,MAAQ9uB,EAAE8uB,MAAQgE,EAAKb,aAEnCa,EAAKhE,MAASiE,EAAQS,UAAYT,EAAQG,WAAcJ,EAAKb,aAE3Da,EAAKhE,MAAQiE,EAAQM,WACvBP,EAAKhE,MAASiE,EAAQM,SAAW,EAAMhkB,KAAKsO,IAAOmV,EAAKhE,MAAQiE,EAAQM,SAAY,EAAI,KAEtFP,EAAKhE,MAAQnnB,EAAO8rB,WACtBX,EAAKhE,MAASnnB,EAAO8rB,SAAW,EAAMpkB,KAAKsO,IAAOhW,EAAO8rB,SAAWX,EAAKhE,MAAS,EAAI,KAExFiE,EAAQI,SAASn0B,UAAW,4BAA+B8zB,EAAU,MAAI,OAE3EY,aAAc,SAAsB1zB,GAClC,IACI2H,EADShO,KACOgO,OAAOmrB,KACvBA,EAFSn5B,KAEKm5B,KACdC,EAAUD,EAAKC,QACnB,IAAKhqB,EAAQkB,SAAU,CACrB,IAAK6oB,EAAKE,qBAAuBF,EAAKG,iBACpC,OAEF,GAAe,aAAXjzB,EAAEub,MAAmC,aAAXvb,EAAEub,MAAuBvb,EAAE2zB,eAAe13B,OAAS,IAAMkd,EAAOI,QAC5F,OAEFuZ,EAAKE,oBAAqB,EAC1BF,EAAKG,kBAAmB,EAErBF,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl3B,SAC1C62B,EAAKhE,MAAQzf,KAAKK,IAAIL,KAAKyL,IAAIgY,EAAKhE,MAAOiE,EAAQM,UAAW1rB,EAAO8rB,UACrEV,EAAQI,SAASh0B,WAhBJxF,KAgBsBgO,OAAOgK,OAAO3S,UAAW,4BAA+B8zB,EAAU,MAAI,KACzGA,EAAKb,aAAea,EAAKhE,MACzBgE,EAAKQ,WAAY,EACE,IAAfR,EAAKhE,QAAeiE,EAAQlK,cAAWnoB,KAE7Cwa,aAAc,SAAsBlb,GAClC,IACI8yB,EADSn5B,KACKm5B,KACdC,EAAUD,EAAKC,QACf/O,EAAQ8O,EAAK9O,MACZ+O,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl3B,SACtC+nB,EAAMvI,YACNtC,EAAOI,SAAWvZ,EAAEid,iBACxB+G,EAAMvI,WAAY,EAClBuI,EAAM4P,aAAa5e,EAAe,eAAXhV,EAAEub,KAAwBvb,EAAEgc,cAAc,GAAGC,MAAQjc,EAAEic,MAC9E+H,EAAM4P,aAAa3e,EAAe,eAAXjV,EAAEub,KAAwBvb,EAAEgc,cAAc,GAAGG,MAAQnc,EAAEmc,SAEhFiB,YAAa,SAAqBpd,GAChC,IAAI6M,EAASlT,KACTm5B,EAAOjmB,EAAOimB,KACdC,EAAUD,EAAKC,QACf/O,EAAQ8O,EAAK9O,MACb1E,EAAWwT,EAAKxT,SACpB,GAAKyT,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl3B,SAC1C4Q,EAAOgP,YAAa,EACfmI,EAAMvI,WAAcsX,EAAQlK,UAAjC,CAEK7E,EAAMtI,UACTsI,EAAMrX,MAAQomB,EAAQI,SAAS,GAAG9wB,YAClC2hB,EAAMpX,OAASmmB,EAAQI,SAAS,GAAG3wB,aACnCwhB,EAAM5H,OAASpW,EAAMO,aAAawsB,EAAQK,aAAa,GAAI,MAAQ,EACnEpP,EAAM3H,OAASrW,EAAMO,aAAawsB,EAAQK,aAAa,GAAI,MAAQ,EACnEL,EAAQc,WAAad,EAAQlK,SAAS,GAAGxmB,YACzC0wB,EAAQe,YAAcf,EAAQlK,SAAS,GAAGrmB,aAC1CuwB,EAAQK,aAAaj0B,WAAW,GAC5B0N,EAAOW,MACTwW,EAAM5H,QAAU4H,EAAM5H,OACtB4H,EAAM3H,QAAU2H,EAAM3H,SAI1B,IAAI0X,EAAc/P,EAAMrX,MAAQmmB,EAAKhE,MACjCkF,EAAehQ,EAAMpX,OAASkmB,EAAKhE,MAEvC,KAAIiF,EAAchB,EAAQc,YAAcG,EAAejB,EAAQe,aAA/D,CAUA,GARA9P,EAAMiQ,KAAO5kB,KAAKyL,IAAMiY,EAAQc,WAAa,EAAME,EAAc,EAAK,GACtE/P,EAAMkQ,MAAQlQ,EAAMiQ,KACpBjQ,EAAMmQ,KAAO9kB,KAAKyL,IAAMiY,EAAQe,YAAc,EAAME,EAAe,EAAK,GACxEhQ,EAAMoQ,MAAQpQ,EAAMmQ,KAEpBnQ,EAAMqQ,eAAerf,EAAe,cAAXhV,EAAEub,KAAuBvb,EAAEgc,cAAc,GAAGC,MAAQjc,EAAEic,MAC/E+H,EAAMqQ,eAAepf,EAAe,cAAXjV,EAAEub,KAAuBvb,EAAEgc,cAAc,GAAGG,MAAQnc,EAAEmc,OAE1E6H,EAAMtI,UAAYoX,EAAKQ,UAAW,CACrC,GACEzmB,EAAOI,iBAEJoC,KAAKC,MAAM0U,EAAMiQ,QAAU5kB,KAAKC,MAAM0U,EAAM5H,SAAW4H,EAAMqQ,eAAerf,EAAIgP,EAAM4P,aAAa5e,GAChG3F,KAAKC,MAAM0U,EAAMkQ,QAAU7kB,KAAKC,MAAM0U,EAAM5H,SAAW4H,EAAMqQ,eAAerf,EAAIgP,EAAM4P,aAAa5e,GAIzG,YADAgP,EAAMvI,WAAY,GAElB,IACC5O,EAAOI,iBAELoC,KAAKC,MAAM0U,EAAMmQ,QAAU9kB,KAAKC,MAAM0U,EAAM3H,SAAW2H,EAAMqQ,eAAepf,EAAI+O,EAAM4P,aAAa3e,GAChG5F,KAAKC,MAAM0U,EAAMoQ,QAAU/kB,KAAKC,MAAM0U,EAAM3H,SAAW2H,EAAMqQ,eAAepf,EAAI+O,EAAM4P,aAAa3e,GAIzG,YADA+O,EAAMvI,WAAY,GAItBzb,EAAEid,iBACFjd,EAAEge,kBAEFgG,EAAMtI,SAAU,EAChBsI,EAAMjI,SAAYiI,EAAMqQ,eAAerf,EAAIgP,EAAM4P,aAAa5e,EAAKgP,EAAM5H,OACzE4H,EAAM9H,SAAY8H,EAAMqQ,eAAepf,EAAI+O,EAAM4P,aAAa3e,EAAK+O,EAAM3H,OAErE2H,EAAMjI,SAAWiI,EAAMiQ,OACzBjQ,EAAMjI,SAAYiI,EAAMiQ,KAAO,EAAM5kB,KAAKsO,IAAOqG,EAAMiQ,KAAOjQ,EAAMjI,SAAY,EAAI,KAElFiI,EAAMjI,SAAWiI,EAAMkQ,OACzBlQ,EAAMjI,SAAYiI,EAAMkQ,KAAO,EAAM7kB,KAAKsO,IAAOqG,EAAMjI,SAAWiI,EAAMkQ,KAAQ,EAAI,KAGlFlQ,EAAM9H,SAAW8H,EAAMmQ,OACzBnQ,EAAM9H,SAAY8H,EAAMmQ,KAAO,EAAM9kB,KAAKsO,IAAOqG,EAAMmQ,KAAOnQ,EAAM9H,SAAY,EAAI,KAElF8H,EAAM9H,SAAW8H,EAAMoQ,OACzBpQ,EAAM9H,SAAY8H,EAAMoQ,KAAO,EAAM/kB,KAAKsO,IAAOqG,EAAM9H,SAAW8H,EAAMoQ,KAAQ,EAAI,KAIjF9U,EAASgV,gBAAiBhV,EAASgV,cAAgBtQ,EAAMqQ,eAAerf,GACxEsK,EAASiV,gBAAiBjV,EAASiV,cAAgBvQ,EAAMqQ,eAAepf,GACxEqK,EAASkV,WAAYlV,EAASkV,SAAW94B,KAAK4K,OACnDgZ,EAAStK,GAAKgP,EAAMqQ,eAAerf,EAAIsK,EAASgV,gBAAkB54B,KAAK4K,MAAQgZ,EAASkV,UAAY,EACpGlV,EAASrK,GAAK+O,EAAMqQ,eAAepf,EAAIqK,EAASiV,gBAAkB74B,KAAK4K,MAAQgZ,EAASkV,UAAY,EAChGnlB,KAAKwB,IAAImT,EAAMqQ,eAAerf,EAAIsK,EAASgV,eAAiB,IAAKhV,EAAStK,EAAI,GAC9E3F,KAAKwB,IAAImT,EAAMqQ,eAAepf,EAAIqK,EAASiV,eAAiB,IAAKjV,EAASrK,EAAI,GAClFqK,EAASgV,cAAgBtQ,EAAMqQ,eAAerf,EAC9CsK,EAASiV,cAAgBvQ,EAAMqQ,eAAepf,EAC9CqK,EAASkV,SAAW94B,KAAK4K,MAEzBysB,EAAQK,aAAap0B,UAAW,eAAkBglB,EAAc,SAAI,OAAUA,EAAc,SAAI,YAElGrF,WAAY,WACV,IACImU,EADSn5B,KACKm5B,KACdC,EAAUD,EAAKC,QACf/O,EAAQ8O,EAAK9O,MACb1E,EAAWwT,EAAKxT,SACpB,GAAKyT,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl3B,OAA1C,CACA,IAAK+nB,EAAMvI,YAAcuI,EAAMtI,QAG7B,OAFAsI,EAAMvI,WAAY,OAClBuI,EAAMtI,SAAU,GAGlBsI,EAAMvI,WAAY,EAClBuI,EAAMtI,SAAU,EAChB,IAAI+Y,EAAoB,IACpBC,EAAoB,IACpBC,EAAoBrV,EAAStK,EAAIyf,EACjCG,EAAe5Q,EAAMjI,SAAW4Y,EAChCE,EAAoBvV,EAASrK,EAAIyf,EACjCI,EAAe9Q,EAAM9H,SAAW2Y,EAGjB,IAAfvV,EAAStK,IAAWyf,EAAoBplB,KAAKwB,KAAK+jB,EAAe5Q,EAAMjI,UAAYuD,EAAStK,IAC7E,IAAfsK,EAASrK,IAAWyf,EAAoBrlB,KAAKwB,KAAKikB,EAAe9Q,EAAM9H,UAAYoD,EAASrK,IAChG,IAAIwK,EAAmBpQ,KAAKK,IAAI+kB,EAAmBC,GAEnD1Q,EAAMjI,SAAW6Y,EACjB5Q,EAAM9H,SAAW4Y,EAGjB,IAAIf,EAAc/P,EAAMrX,MAAQmmB,EAAKhE,MACjCkF,EAAehQ,EAAMpX,OAASkmB,EAAKhE,MACvC9K,EAAMiQ,KAAO5kB,KAAKyL,IAAMiY,EAAQc,WAAa,EAAME,EAAc,EAAK,GACtE/P,EAAMkQ,MAAQlQ,EAAMiQ,KACpBjQ,EAAMmQ,KAAO9kB,KAAKyL,IAAMiY,EAAQe,YAAc,EAAME,EAAe,EAAK,GACxEhQ,EAAMoQ,MAAQpQ,EAAMmQ,KACpBnQ,EAAMjI,SAAW1M,KAAKK,IAAIL,KAAKyL,IAAIkJ,EAAMjI,SAAUiI,EAAMkQ,MAAOlQ,EAAMiQ,MACtEjQ,EAAM9H,SAAW7M,KAAKK,IAAIL,KAAKyL,IAAIkJ,EAAM9H,SAAU8H,EAAMoQ,MAAOpQ,EAAMmQ,MAEtEpB,EAAQK,aAAaj0B,WAAWsgB,GAAkBzgB,UAAW,eAAkBglB,EAAc,SAAI,OAAUA,EAAc,SAAI,WAE/H+Q,gBAAiB,WACf,IACIjC,EADSn5B,KACKm5B,KACdC,EAAUD,EAAKC,QACfA,EAAQlK,UAHClvB,KAGkBwa,gBAHlBxa,KAG2CoY,cACtDghB,EAAQI,SAASn0B,UAAU,+BAC3B+zB,EAAQK,aAAap0B,UAAU,sBAC/B+zB,EAAQlK,cAAWnoB,EACnBqyB,EAAQI,cAAWzyB,EACnBqyB,EAAQK,kBAAe1yB,EAEvBoyB,EAAKhE,MAAQ,EACbgE,EAAKb,aAAe,IAIxBh0B,OAAQ,SAAgB+B,GACtB,IACI8yB,EADSn5B,KACKm5B,KAEdA,EAAKhE,OAAwB,IAAfgE,EAAKhE,MAErBgE,EAAKkC,MAGLlC,EAAKmC,GAAGj1B,IAGZi1B,GAAI,SAAcj1B,GAChB,IAgBIk1B,EACAC,EAGA3X,EACAC,EACA2X,EACAC,EACAC,EACAC,EACAxB,EACAC,EACAwB,EACAC,EACAC,EACAC,EACA9B,EACAC,EAjCAjnB,EAASlT,KAETm5B,EAAOjmB,EAAOimB,KACdnrB,EAASkF,EAAOlF,OAAOmrB,KACvBC,EAAUD,EAAKC,QACf/O,EAAQ8O,EAAK9O,OAEZ+O,EAAQlK,WACXkK,EAAQlK,SAAWhc,EAAO4H,aAAevY,EAAE2Q,EAAO4H,cAAgB5H,EAAOkB,OAAO9J,GAAG4I,EAAOkF,aAC1FghB,EAAQI,SAAWJ,EAAQlK,SAASvjB,KAAK,oBACzCytB,EAAQK,aAAeL,EAAQI,SAAShuB,OAAQ,IAAOwC,EAAqB,iBAEzEorB,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl3B,UAE1C82B,EAAQlK,SAASvrB,SAAU,GAAMqK,EAAuB,uBAqBpB,IAAzBqc,EAAM4P,aAAa5e,GAAqBhV,GACjDk1B,EAAoB,aAAXl1B,EAAEub,KAAsBvb,EAAE2zB,eAAe,GAAG1X,MAAQjc,EAAEic,MAC/DkZ,EAAoB,aAAXn1B,EAAEub,KAAsBvb,EAAE2zB,eAAe,GAAGxX,MAAQnc,EAAEmc,QAE/D+Y,EAASlR,EAAM4P,aAAa5e,EAC5BmgB,EAASnR,EAAM4P,aAAa3e,GAG9B6d,EAAKhE,MAAQiE,EAAQK,aAAal1B,KAAK,qBAAuByJ,EAAO0rB,SACrEP,EAAKb,aAAec,EAAQK,aAAal1B,KAAK,qBAAuByJ,EAAO0rB,SACxErzB,GACF6zB,EAAad,EAAQlK,SAAS,GAAGxmB,YACjCyxB,EAAcf,EAAQlK,SAAS,GAAGrmB,aAGlCgb,EAFUuV,EAAQlK,SAASpmB,SAASU,KAEhB0wB,EAAa,EAAMqB,EACvCzX,EAFUsV,EAAQlK,SAASpmB,SAASS,IAEhB4wB,EAAc,EAAMqB,EAExCG,EAAavC,EAAQI,SAAS,GAAG9wB,YACjCkzB,EAAcxC,EAAQI,SAAS,GAAG3wB,aAClCuxB,EAAcuB,EAAaxC,EAAKhE,MAChCkF,EAAeuB,EAAczC,EAAKhE,MAIlC4G,IAFAF,EAAgBnmB,KAAKyL,IAAM+Y,EAAa,EAAME,EAAc,EAAK,IAGjE4B,IAFAF,EAAgBpmB,KAAKyL,IAAMgZ,EAAc,EAAME,EAAe,EAAK,KAInEoB,EAAa5X,EAAQsV,EAAKhE,OAGT0G,IACfJ,EAAaI,GAEEE,EAAbN,IACFA,EAAaM,IANfL,EAAa5X,EAAQqV,EAAKhE,OAST2G,IACfJ,EAAaI,GAEEE,EAAbN,IACFA,EAAaM,IAIfN,EADAD,EAAa,EAGfrC,EAAQK,aAAaj0B,WAAW,KAAKH,UAAW,eAAiBo2B,EAAa,OAASC,EAAa,SACpGtC,EAAQI,SAASh0B,WAAW,KAAKH,UAAW,4BAA+B8zB,EAAU,MAAI,OAE3FkC,IAAK,WACH,IAAInoB,EAASlT,KAETm5B,EAAOjmB,EAAOimB,KACdnrB,EAASkF,EAAOlF,OAAOmrB,KACvBC,EAAUD,EAAKC,QAEdA,EAAQlK,WACXkK,EAAQlK,SAAWhc,EAAO4H,aAAevY,EAAE2Q,EAAO4H,cAAgB5H,EAAOkB,OAAO9J,GAAG4I,EAAOkF,aAC1FghB,EAAQI,SAAWJ,EAAQlK,SAASvjB,KAAK,oBACzCytB,EAAQK,aAAeL,EAAQI,SAAShuB,OAAQ,IAAOwC,EAAqB,iBAEzEorB,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl3B,SAE1C62B,EAAKhE,MAAQ,EACbgE,EAAKb,aAAe,EACpBc,EAAQK,aAAaj0B,WAAW,KAAKH,UAAU,sBAC/C+zB,EAAQI,SAASh0B,WAAW,KAAKH,UAAU,+BAC3C+zB,EAAQlK,SAASjrB,YAAa,GAAM+J,EAAuB,kBAC3DorB,EAAQlK,cAAWnoB,IAGrB2pB,OAAQ,WACN,IAAIxd,EAASlT,KACTm5B,EAAOjmB,EAAOimB,KAClB,IAAIA,EAAKjlB,QAAT,CACAilB,EAAKjlB,SAAU,EAEf,IAAIjE,IAA+C,eAA7BiD,EAAOmO,YAAYgG,QAA0BjY,EAAQa,kBAAmBiD,EAAOlF,OAAOsZ,mBAAmB,CAAEC,SAAS,EAAMphB,SAAS,GAGrJiJ,EAAQkB,UACV4C,EAAOS,WAAW/N,GAAG,eAAgB,gBAAiBuzB,EAAKD,eAAgBjpB,GAC3EiD,EAAOS,WAAW/N,GAAG,gBAAiB,gBAAiBuzB,EAAKS,gBAAiB3pB,GAC7EiD,EAAOS,WAAW/N,GAAG,aAAc,gBAAiBuzB,EAAKY,aAAc9pB,IACjC,eAA7BiD,EAAOmO,YAAYgG,QAC5BnU,EAAOS,WAAW/N,GAAGsN,EAAOmO,YAAYgG,MAAO,gBAAiB8R,EAAKD,eAAgBjpB,GACrFiD,EAAOS,WAAW/N,GAAGsN,EAAOmO,YAAYmG,KAAM,gBAAiB2R,EAAKS,gBAAiB3pB,GACrFiD,EAAOS,WAAW/N,GAAGsN,EAAOmO,YAAYoG,IAAK,gBAAiB0R,EAAKY,aAAc9pB,IAInFiD,EAAOS,WAAW/N,GAAGsN,EAAOmO,YAAYmG,KAAO,IAAOtU,EAAOlF,OAAOmrB,KAAmB,eAAIA,EAAK1V,eAElGkN,QAAS,WACP,IAAIzd,EAASlT,KACTm5B,EAAOjmB,EAAOimB,KAClB,GAAKA,EAAKjlB,QAAV,CAEAhB,EAAOimB,KAAKjlB,SAAU,EAEtB,IAAIjE,IAA+C,eAA7BiD,EAAOmO,YAAYgG,QAA0BjY,EAAQa,kBAAmBiD,EAAOlF,OAAOsZ,mBAAmB,CAAEC,SAAS,EAAMphB,SAAS,GAGrJiJ,EAAQkB,UACV4C,EAAOS,WAAWrM,IAAI,eAAgB,gBAAiB6xB,EAAKD,eAAgBjpB,GAC5EiD,EAAOS,WAAWrM,IAAI,gBAAiB,gBAAiB6xB,EAAKS,gBAAiB3pB,GAC9EiD,EAAOS,WAAWrM,IAAI,aAAc,gBAAiB6xB,EAAKY,aAAc9pB,IAClC,eAA7BiD,EAAOmO,YAAYgG,QAC5BnU,EAAOS,WAAWrM,IAAI4L,EAAOmO,YAAYgG,MAAO,gBAAiB8R,EAAKD,eAAgBjpB,GACtFiD,EAAOS,WAAWrM,IAAI4L,EAAOmO,YAAYmG,KAAM,gBAAiB2R,EAAKS,gBAAiB3pB,GACtFiD,EAAOS,WAAWrM,IAAI4L,EAAOmO,YAAYoG,IAAK,gBAAiB0R,EAAKY,aAAc9pB,IAIpFiD,EAAOS,WAAWrM,IAAI4L,EAAOmO,YAAYmG,KAAO,IAAOtU,EAAOlF,OAAOmrB,KAAmB,eAAIA,EAAK1V,gBAkGjGwY,EAAO,CACTC,YAAa,SAAqB/xB,EAAOgyB,QACd,IAApBA,IAA6BA,GAAkB,GAEpD,IAAIjpB,EAASlT,KACTgO,EAASkF,EAAOlF,OAAO2gB,KAC3B,QAAqB,IAAVxkB,GACkB,IAAzB+I,EAAOkB,OAAO9R,OAAlB,CACA,IAEI4sB,EAFYhc,EAAOe,SAAWf,EAAOlF,OAAOiG,QAAQC,QAGpDhB,EAAOS,WAAW5S,SAAU,IAAOmS,EAAOlF,OAAiB,WAAI,6BAAgC7D,EAAQ,MACvG+I,EAAOkB,OAAO9J,GAAGH,GAEjBiyB,EAAUlN,EAASvjB,KAAM,IAAOqC,EAAmB,aAAI,SAAYA,EAAkB,YAAI,UAAaA,EAAmB,aAAI,MAC7HkhB,EAAS/qB,SAAS6J,EAAOquB,eAAkBnN,EAAS/qB,SAAS6J,EAAOsuB,cAAiBpN,EAAS/qB,SAAS6J,EAAOuuB,gBAChHH,EAAUA,EAAQp4B,IAAIkrB,EAAS,KAEV,IAAnBkN,EAAQ95B,QAEZ85B,EAAQxyB,KAAK,SAAU4yB,EAAYxS,GACjC,IAAIwP,EAAWj3B,EAAEynB,GACjBwP,EAAS71B,SAASqK,EAAOuuB,cAEzB,IAAIE,EAAajD,EAASj1B,KAAK,mBAC3B0lB,EAAMuP,EAASj1B,KAAK,YACpB2lB,EAASsP,EAASj1B,KAAK,eACvB4lB,EAAQqP,EAASj1B,KAAK,cAE1B2O,EAAO6W,UAAUyP,EAAS,GAAKvP,GAAOwS,EAAavS,EAAQC,GAAO,EAAO,WACvE,GAAI,MAAOjX,GAA8CA,KAAWA,GAAWA,EAAOlF,UAAWkF,EAAOsJ,UAAxG,CAqBA,GApBIigB,GACFjD,EAAS/vB,IAAI,mBAAqB,QAAWgzB,EAAa,MAC1DjD,EAAS10B,WAAW,qBAEhBolB,IACFsP,EAASj1B,KAAK,SAAU2lB,GACxBsP,EAAS10B,WAAW,gBAElBqlB,IACFqP,EAASj1B,KAAK,QAAS4lB,GACvBqP,EAAS10B,WAAW,eAElBmlB,IACFuP,EAASj1B,KAAK,MAAO0lB,GACrBuP,EAAS10B,WAAW,cAIxB00B,EAAS71B,SAASqK,EAAOsuB,aAAar4B,YAAY+J,EAAOuuB,cACzDrN,EAASvjB,KAAM,IAAOqC,EAAqB,gBAAI9J,SAC3CgP,EAAOlF,OAAO4L,MAAQuiB,EAAiB,CACzC,IAAIO,EAAqBxN,EAAS3qB,KAAK,2BACvC,GAAI2qB,EAAS/qB,SAAS+O,EAAOlF,OAAO6L,qBAAsB,CACxD,IAAI8iB,EAAgBzpB,EAAOS,WAAW5S,SAAU,6BAAgC27B,EAAqB,WAAexpB,EAAOlF,OAA0B,oBAAI,KACzJkF,EAAOyb,KAAKuN,YAAYS,EAAcxyB,SAAS,OAC1C,CACL,IAAIyyB,EAAkB1pB,EAAOS,WAAW5S,SAAU,IAAOmS,EAAOlF,OAA0B,oBAAI,6BAAgC0uB,EAAqB,MACnJxpB,EAAOyb,KAAKuN,YAAYU,EAAgBzyB,SAAS,IAGrD+I,EAAO/B,KAAK,iBAAkB+d,EAAS,GAAIsK,EAAS,OAGtDtmB,EAAO/B,KAAK,gBAAiB+d,EAAS,GAAIsK,EAAS,QAGvD5K,KAAM,WACJ,IAAI1b,EAASlT,KACT2T,EAAaT,EAAOS,WACpBuX,EAAehY,EAAOlF,OACtBoG,EAASlB,EAAOkB,OAChBgE,EAAclF,EAAOkF,YACrBpE,EAAYd,EAAOe,SAAWiX,EAAajX,QAAQC,QACnDlG,EAASkd,EAAayD,KAEtB9Y,EAAgBqV,EAAarV,cAKjC,SAASgnB,EAAW1yB,GAClB,GAAI6J,GACF,GAAIL,EAAW5S,SAAU,IAAOmqB,EAAuB,WAAI,6BAAgC/gB,EAAQ,MAAQ7H,OACzG,OAAO,OAEJ,GAAI8R,EAAOjK,GAAU,OAAO,EACnC,OAAO,EAET,SAAS8R,EAAWuc,GAClB,OAAIxkB,EACKzR,EAAEi2B,GAASj0B,KAAK,2BAElBhC,EAAEi2B,GAASruB,QAIpB,GApBsB,SAAlB0L,IACFA,EAAgB,GAkBb3C,EAAOyb,KAAKmO,qBAAsB5pB,EAAOyb,KAAKmO,oBAAqB,GACpE5pB,EAAOlF,OAAO6J,sBAChBlE,EAAW5S,SAAU,IAAOmqB,EAA8B,mBAAIthB,KAAK,SAAUmzB,EAASvE,GACpF,IAAIruB,EAAQ6J,EAAYzR,EAAEi2B,GAASj0B,KAAK,2BAA6BhC,EAAEi2B,GAASruB,QAChF+I,EAAOyb,KAAKuN,YAAY/xB,UAErB,GAAoB,EAAhB0L,EACT,IAAK,IAAIxT,EAAI+V,EAAa/V,EAAI+V,EAAcvC,EAAexT,GAAK,EAC1Dw6B,EAAWx6B,IAAM6Q,EAAOyb,KAAKuN,YAAY75B,QAG/C6Q,EAAOyb,KAAKuN,YAAY9jB,GAE1B,GAAIpK,EAAOgvB,aACT,GAAoB,EAAhBnnB,GAAsB7H,EAAOivB,oBAAkD,EAA5BjvB,EAAOivB,mBAAyB,CAMrF,IALA,IAAIC,EAASlvB,EAAOivB,mBAChBvR,EAAM7V,EACNsnB,EAAWznB,KAAKyL,IAAI/I,EAAcsT,EAAMhW,KAAKK,IAAImnB,EAAQxR,GAAMtX,EAAO9R,QACtE86B,EAAW1nB,KAAKK,IAAIqC,EAAc1C,KAAKK,IAAI2V,EAAKwR,GAAS,GAEpD5lB,EAAMc,EAAcvC,EAAeyB,EAAM6lB,EAAU7lB,GAAO,EAC7DulB,EAAWvlB,IAAQpE,EAAOyb,KAAKuN,YAAY5kB,GAGjD,IAAK,IAAIE,EAAM4lB,EAAU5lB,EAAMY,EAAaZ,GAAO,EAC7CqlB,EAAWrlB,IAAQtE,EAAOyb,KAAKuN,YAAY1kB,OAE5C,CACL,IAAIuC,EAAYpG,EAAW5S,SAAU,IAAOmqB,EAA2B,gBAChD,EAAnBnR,EAAUzX,QAAc4Q,EAAOyb,KAAKuN,YAAYjgB,EAAWlC,IAE/D,IAAIE,EAAYtG,EAAW5S,SAAU,IAAOmqB,EAA2B,gBAChD,EAAnBjR,EAAU3X,QAAc4Q,EAAOyb,KAAKuN,YAAYjgB,EAAWhC,OAiFnEojB,EAAa,CACfC,aAAc,SAAsBjiB,EAAGC,GACrC,IACM6hB,EACAC,EACAG,EAqBFC,EACAC,EAzBAC,EAIK,SAAUC,EAAO1gB,GAGtB,IAFAmgB,GAAY,EACZD,EAAWQ,EAAMr7B,OACY,EAAtB66B,EAAWC,GAEZO,EADJJ,EAAQJ,EAAWC,GAAY,IACXngB,EAClBmgB,EAAWG,EAEXJ,EAAWI,EAGf,OAAOJ,GAuBX,OApBAn9B,KAAKqb,EAAIA,EACTrb,KAAKsb,EAAIA,EACTtb,KAAKi0B,UAAY5Y,EAAE/Y,OAAS,EAO5BtC,KAAK49B,YAAc,SAAqB5E,GACtC,OAAKA,GAGLyE,EAAKC,EAAa19B,KAAKqb,EAAG2d,GAC1BwE,EAAKC,EAAK,GAIAzE,EAAKh5B,KAAKqb,EAAEmiB,KAAQx9B,KAAKsb,EAAEmiB,GAAMz9B,KAAKsb,EAAEkiB,KAASx9B,KAAKqb,EAAEoiB,GAAMz9B,KAAKqb,EAAEmiB,IAAQx9B,KAAKsb,EAAEkiB,IAR5E,GAUbx9B,MAGT69B,uBAAwB,SAAgCC,GACtD,IAAI5qB,EAASlT,KACRkT,EAAO6qB,WAAWC,SACrB9qB,EAAO6qB,WAAWC,OAAS9qB,EAAOlF,OAAO4L,KACrC,IAAIyjB,EAAWC,aAAapqB,EAAOqB,WAAYupB,EAAEvpB,YACjD,IAAI8oB,EAAWC,aAAapqB,EAAOoB,SAAUwpB,EAAExpB,YAGvD6G,aAAc,SAAsB8iB,EAAgB7iB,GAClD,IAEI8iB,EACAC,EAHAjrB,EAASlT,KACTo+B,EAAalrB,EAAO6qB,WAAWM,QAGnC,SAASC,EAAuBR,GAK9B,IAAIrlB,EAAYvF,EAAOY,cAAgBZ,EAAOuF,UAAYvF,EAAOuF,UAC7B,UAAhCvF,EAAOlF,OAAO+vB,WAAWQ,KAC3BrrB,EAAO6qB,WAAWF,uBAAuBC,GAGzCK,GAAuBjrB,EAAO6qB,WAAWC,OAAOJ,aAAanlB,IAG1D0lB,GAAuD,cAAhCjrB,EAAOlF,OAAO+vB,WAAWQ,KACnDL,GAAcJ,EAAE3kB,eAAiB2kB,EAAEjlB,iBAAmB3F,EAAOiG,eAAiBjG,EAAO2F,gBACrFslB,GAAwB1lB,EAAYvF,EAAO2F,gBAAkBqlB,EAAcJ,EAAEjlB,gBAG3E3F,EAAOlF,OAAO+vB,WAAWS,UAC3BL,EAAsBL,EAAE3kB,eAAiBglB,GAE3CL,EAAE7kB,eAAeklB,GACjBL,EAAE3iB,aAAagjB,EAAqBjrB,GACpC4qB,EAAEzjB,oBACFyjB,EAAEtkB,sBAEJ,GAAIpI,MAAMC,QAAQ+sB,GAChB,IAAK,IAAI/7B,EAAI,EAAGA,EAAI+7B,EAAW97B,OAAQD,GAAK,EACtC+7B,EAAW/7B,KAAO+Y,GAAgBgjB,EAAW/7B,aAActC,GAC7Du+B,EAAuBF,EAAW/7B,SAG7B+7B,aAAsBr+B,GAAUqb,IAAiBgjB,GAC1DE,EAAuBF,IAG3BjmB,cAAe,SAAuB1S,EAAU2V,GAC9C,IAEI/Y,EAFA6Q,EAASlT,KACTo+B,EAAalrB,EAAO6qB,WAAWM,QAEnC,SAASI,EAAwBX,GAC/BA,EAAE3lB,cAAc1S,EAAUyN,GACT,IAAbzN,IACFq4B,EAAEriB,kBACEqiB,EAAE9vB,OAAO4N,YACXvP,EAAMI,SAAS,WACbqxB,EAAE/lB,qBAGN+lB,EAAEnqB,WAAWzL,cAAc,WACpBk2B,IACDN,EAAE9vB,OAAO4L,MAAwC,UAAhC1G,EAAOlF,OAAO+vB,WAAWQ,IAC5CT,EAAEjhB,UAEJihB,EAAE51B,oBAIR,GAAIkJ,MAAMC,QAAQ+sB,GAChB,IAAK/7B,EAAI,EAAGA,EAAI+7B,EAAW97B,OAAQD,GAAK,EAClC+7B,EAAW/7B,KAAO+Y,GAAgBgjB,EAAW/7B,aAActC,GAC7D0+B,EAAwBL,EAAW/7B,SAG9B+7B,aAAsBr+B,GAAUqb,IAAiBgjB,GAC1DK,EAAwBL,KA8D1BM,EAAO,CACTC,gBAAiB,SAAyBxrB,GAExC,OADAA,EAAI5O,KAAK,WAAY,KACd4O,GAETyrB,UAAW,SAAmBzrB,EAAK0rB,GAEjC,OADA1rB,EAAI5O,KAAK,OAAQs6B,GACV1rB,GAET2rB,WAAY,SAAoB3rB,EAAK4rB,GAEnC,OADA5rB,EAAI5O,KAAK,aAAcw6B,GAChB5rB,GAET6rB,UAAW,SAAmB7rB,GAE5B,OADAA,EAAI5O,KAAK,iBAAiB,GACnB4O,GAET8rB,SAAU,SAAkB9rB,GAE1B,OADAA,EAAI5O,KAAK,iBAAiB,GACnB4O,GAET+rB,WAAY,SAAoB74B,GAC9B,IAAI6M,EAASlT,KACTgO,EAASkF,EAAOlF,OAAO0wB,KAC3B,GAAkB,KAAdr4B,EAAEspB,QAAN,CACA,IAAIwP,EAAY58B,EAAE8D,EAAEC,QAChB4M,EAAOsW,YAActW,EAAOsW,WAAW4J,SAAW+L,EAAUz4B,GAAGwM,EAAOsW,WAAW4J,WAC7ElgB,EAAOmG,QAAUnG,EAAOlF,OAAO4L,MACnC1G,EAAO0J,YAEL1J,EAAOmG,MACTnG,EAAOwrB,KAAKU,OAAOpxB,EAAOqxB,kBAE1BnsB,EAAOwrB,KAAKU,OAAOpxB,EAAOsxB,mBAG1BpsB,EAAOsW,YAActW,EAAOsW,WAAW6J,SAAW8L,EAAUz4B,GAAGwM,EAAOsW,WAAW6J,WAC7EngB,EAAOkG,cAAgBlG,EAAOlF,OAAO4L,MACzC1G,EAAO6J,YAEL7J,EAAOkG,YACTlG,EAAOwrB,KAAKU,OAAOpxB,EAAOuxB,mBAE1BrsB,EAAOwrB,KAAKU,OAAOpxB,EAAOwxB,mBAG1BtsB,EAAOygB,YAAcwL,EAAUz4B,GAAI,IAAOwM,EAAOlF,OAAO2lB,WAAsB,cAChFwL,EAAU,GAAGM,UAGjBL,OAAQ,SAAgBM,GACtB,IACIC,EADS3/B,KACa0+B,KAAKkB,WACH,IAAxBD,EAAar9B,SACjBq9B,EAAa/8B,KAAK,IAClB+8B,EAAa/8B,KAAK88B,KAEpBG,iBAAkB,WAChB,IAAI3sB,EAASlT,KAEb,IAAIkT,EAAOlF,OAAO4L,KAAlB,CACA,IAAImU,EAAM7a,EAAOsW,WACb4J,EAAUrF,EAAIqF,QACdC,EAAUtF,EAAIsF,QAEdA,GAA4B,EAAjBA,EAAQ/wB,SACjB4Q,EAAOkG,YACTlG,EAAOwrB,KAAKM,UAAU3L,GAEtBngB,EAAOwrB,KAAKO,SAAS5L,IAGrBD,GAA4B,EAAjBA,EAAQ9wB,SACjB4Q,EAAOmG,MACTnG,EAAOwrB,KAAKM,UAAU5L,GAEtBlgB,EAAOwrB,KAAKO,SAAS7L,MAI3B0M,iBAAkB,WAChB,IAAI5sB,EAASlT,KACTgO,EAASkF,EAAOlF,OAAO0wB,KACvBxrB,EAAOygB,YAAczgB,EAAOlF,OAAO2lB,WAAWsC,WAAa/iB,EAAOygB,WAAWI,SAAW7gB,EAAOygB,WAAWI,QAAQzxB,QACpH4Q,EAAOygB,WAAWI,QAAQnqB,KAAK,SAAU6qB,EAAasL,GACpD,IAAIC,EAAYz9B,EAAEw9B,GAClB7sB,EAAOwrB,KAAKC,gBAAgBqB,GAC5B9sB,EAAOwrB,KAAKE,UAAUoB,EAAW,UACjC9sB,EAAOwrB,KAAKI,WAAWkB,EAAWhyB,EAAOiyB,wBAAwB5yB,QAAQ,YAAa2yB,EAAU71B,QAAU,OAIhHye,KAAM,WACJ,IAAI1V,EAASlT,KAEbkT,EAAOC,IAAI3I,OAAO0I,EAAOwrB,KAAKkB,YAG9B,IACIxM,EACAC,EAFArlB,EAASkF,EAAOlF,OAAO0wB,KAGvBxrB,EAAOsW,YAActW,EAAOsW,WAAW4J,UACzCA,EAAUlgB,EAAOsW,WAAW4J,SAE1BlgB,EAAOsW,YAActW,EAAOsW,WAAW6J,UACzCA,EAAUngB,EAAOsW,WAAW6J,SAE1BD,IACFlgB,EAAOwrB,KAAKC,gBAAgBvL,GAC5BlgB,EAAOwrB,KAAKE,UAAUxL,EAAS,UAC/BlgB,EAAOwrB,KAAKI,WAAW1L,EAASplB,EAAOsxB,kBACvClM,EAAQxtB,GAAG,UAAWsN,EAAOwrB,KAAKQ,aAEhC7L,IACFngB,EAAOwrB,KAAKC,gBAAgBtL,GAC5BngB,EAAOwrB,KAAKE,UAAUvL,EAAS,UAC/BngB,EAAOwrB,KAAKI,WAAWzL,EAASrlB,EAAOwxB,kBACvCnM,EAAQztB,GAAG,UAAWsN,EAAOwrB,KAAKQ,aAIhChsB,EAAOygB,YAAczgB,EAAOlF,OAAO2lB,WAAWsC,WAAa/iB,EAAOygB,WAAWI,SAAW7gB,EAAOygB,WAAWI,QAAQzxB,QACpH4Q,EAAOygB,WAAWxgB,IAAIvN,GAAG,UAAY,IAAOsN,EAAOlF,OAAO2lB,WAAsB,YAAIzgB,EAAOwrB,KAAKQ,aAGpGrT,QAAS,WACP,IAGIuH,EACAC,EAJAngB,EAASlT,KACTkT,EAAOwrB,KAAKkB,YAA8C,EAAhC1sB,EAAOwrB,KAAKkB,WAAWt9B,QAAc4Q,EAAOwrB,KAAKkB,WAAW17B,SAItFgP,EAAOsW,YAActW,EAAOsW,WAAW4J,UACzCA,EAAUlgB,EAAOsW,WAAW4J,SAE1BlgB,EAAOsW,YAActW,EAAOsW,WAAW6J,UACzCA,EAAUngB,EAAOsW,WAAW6J,SAE1BD,GACFA,EAAQ9rB,IAAI,UAAW4L,EAAOwrB,KAAKQ,YAEjC7L,GACFA,EAAQ/rB,IAAI,UAAW4L,EAAOwrB,KAAKQ,YAIjChsB,EAAOygB,YAAczgB,EAAOlF,OAAO2lB,WAAWsC,WAAa/iB,EAAOygB,WAAWI,SAAW7gB,EAAOygB,WAAWI,QAAQzxB,QACpH4Q,EAAOygB,WAAWxgB,IAAI7L,IAAI,UAAY,IAAO4L,EAAOlF,OAAO2lB,WAAsB,YAAIzgB,EAAOwrB,KAAKQ,cA0DnGgB,EAAU,CACZtX,KAAM,WACJ,IAAI1V,EAASlT,KACb,GAAKkT,EAAOlF,OAAOtM,QAAnB,CACA,IAAKJ,EAAII,UAAYJ,EAAII,QAAQy+B,UAG/B,OAFAjtB,EAAOlF,OAAOtM,QAAQwS,SAAU,OAChChB,EAAOlF,OAAOoyB,eAAelsB,SAAU,GAGzC,IAAIxS,EAAUwR,EAAOxR,QACrBA,EAAQ0a,aAAc,EACtB1a,EAAQ2+B,MAAQH,EAAQI,iBACnB5+B,EAAQ2+B,MAAMp7B,KAAQvD,EAAQ2+B,MAAM57B,SACzC/C,EAAQ6+B,cAAc,EAAG7+B,EAAQ2+B,MAAM57B,MAAOyO,EAAOlF,OAAOqb,oBACvDnW,EAAOlF,OAAOtM,QAAQ8+B,cACzBl/B,EAAIlB,iBAAiB,WAAY8S,EAAOxR,QAAQ++B,uBAGpD5U,QAAS,WACM7rB,KACDgO,OAAOtM,QAAQ8+B,cACzBl/B,EAAIjB,oBAAoB,WAFbL,KAEgC0B,QAAQ++B,qBAGvDA,mBAAoB,WACLzgC,KACN0B,QAAQ2+B,MAAQH,EAAQI,gBADlBtgC,KAEN0B,QAAQ6+B,cAFFvgC,KAEuBgO,OAAOgK,MAF9BhY,KAE4C0B,QAAQ2+B,MAAM57B,OAAO,IAEhF67B,cAAe,WACb,IAAII,EAAYp/B,EAAIF,SAASu/B,SAASrvB,MAAM,GAAGnO,MAAM,KAAK4E,OAAO,SAAU64B,GAAQ,MAAgB,KAATA,IACtF/M,EAAQ6M,EAAUp+B,OAGtB,MAAO,CAAE2C,IAFCy7B,EAAU7M,EAAQ,GAETpvB,MADPi8B,EAAU7M,EAAQ,KAGhCgN,WAAY,SAAoB57B,EAAKkF,GAEnC,GADanK,KACD0B,QAAQ0a,aADPpc,KAC8BgO,OAAOtM,QAAQwS,QAA1D,CACA,IAAIiC,EAFSnW,KAEMoU,OAAO9J,GAAGH,GACzB1F,EAAQy7B,EAAQY,QAAQ3qB,EAAM5R,KAAK,iBAClCjD,EAAIF,SAASu/B,SAASI,SAAS97B,KAClCR,EAAQQ,EAAM,IAAMR,GAEtB,IAAIu8B,EAAe1/B,EAAII,QAAQu/B,MAC3BD,GAAgBA,EAAav8B,QAAUA,IAR9BzE,KAWFgO,OAAOtM,QAAQ8+B,aACxBl/B,EAAII,QAAQ8+B,aAAa,CAAE/7B,MAAOA,GAAS,KAAMA,GAEjDnD,EAAII,QAAQy+B,UAAU,CAAE17B,MAAOA,GAAS,KAAMA,MAGlDq8B,QAAS,SAAiBj3B,GACxB,OAAOA,EAAK8D,WAAW4S,cACpBlT,QAAQ,OAAQ,KAChBA,QAAQ,WAAY,IACpBA,QAAQ,OAAQ,KAChBA,QAAQ,MAAO,IACfA,QAAQ,MAAO,KAEpBkzB,cAAe,SAAuBvoB,EAAOvT,EAAOiX,GAClD,IAAIxI,EAASlT,KACb,GAAIyE,EACF,IAAK,IAAIpC,EAAI,EAAGC,EAAS4Q,EAAOkB,OAAO9R,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CACjE,IAAI8T,EAAQjD,EAAOkB,OAAO9J,GAAGjI,GAE7B,GADmB69B,EAAQY,QAAQ3qB,EAAM5R,KAAK,mBACzBE,IAAU0R,EAAMhS,SAAS+O,EAAOlF,OAAO6L,qBAAsB,CAChF,IAAI1P,EAAQgM,EAAMhM,QAClB+I,EAAO6I,QAAQ5R,EAAO6N,EAAO0D,SAIjCxI,EAAO6I,QAAQ,EAAG/D,EAAO0D,KAgD3BwlB,EAAiB,CACnBC,YAAa,WACX,IAAIjuB,EAASlT,KACTohC,EAAUnhC,EAAImB,SAASC,KAAKgM,QAAQ,IAAK,IAEzC+zB,IADkBluB,EAAOkB,OAAO9J,GAAG4I,EAAOkF,aAAa7T,KAAK,cAE9D2O,EAAO6I,QAAQ7I,EAAOS,WAAW5S,SAAU,IAAOmS,EAAOlF,OAAiB,WAAI,eAAkBozB,EAAU,MAAQj3B,UAGtHk3B,QAAS,WACP,IAAInuB,EAASlT,KACb,GAAKkT,EAAOktB,eAAehkB,aAAgBlJ,EAAOlF,OAAOoyB,eAAelsB,QACxE,GAAIhB,EAAOlF,OAAOoyB,eAAeI,cAAgBl/B,EAAII,SAAWJ,EAAII,QAAQ8+B,aAC1El/B,EAAII,QAAQ8+B,aAAa,KAAM,KAAQ,IAAOttB,EAAOkB,OAAO9J,GAAG4I,EAAOkF,aAAa7T,KAAK,cAAkB,QACrG,CACL,IAAI4R,EAAQjD,EAAOkB,OAAO9J,GAAG4I,EAAOkF,aAChC/W,EAAO8U,EAAM5R,KAAK,cAAgB4R,EAAM5R,KAAK,gBACjDtE,EAAImB,SAASC,KAAOA,GAAQ,KAGhCunB,KAAM,WACJ,IAAI1V,EAASlT,KACb,MAAKkT,EAAOlF,OAAOoyB,eAAelsB,SAAYhB,EAAOlF,OAAOtM,SAAWwR,EAAOlF,OAAOtM,QAAQwS,SAA7F,CACAhB,EAAOktB,eAAehkB,aAAc,EACpC,IAAI/a,EAAOpB,EAAImB,SAASC,KAAKgM,QAAQ,IAAK,IAC1C,GAAIhM,EAEF,IADA,IACSgB,EAAI,EAAGC,EAAS4Q,EAAOkB,OAAO9R,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CACjE,IAAI8T,EAAQjD,EAAOkB,OAAO9J,GAAGjI,GAE7B,IADgB8T,EAAM5R,KAAK,cAAgB4R,EAAM5R,KAAK,mBACpClD,IAAS8U,EAAMhS,SAAS+O,EAAOlF,OAAO6L,qBAAsB,CAC5E,IAAI1P,EAAQgM,EAAMhM,QAClB+I,EAAO6I,QAAQ5R,EANP,EAMqB+I,EAAOlF,OAAOqb,oBAAoB,IAIjEnW,EAAOlF,OAAOoyB,eAAekB,YAC/B/+B,EAAEjB,GAAKsE,GAAG,aAAcsN,EAAOktB,eAAee,eAGlDtV,QAAS,WACM7rB,KACFgO,OAAOoyB,eAAekB,YAC/B/+B,EAAEjB,GAAKgG,IAAI,aAFAtH,KAEqBogC,eAAee,eAiDjDI,EAAW,CACbC,IAAK,WACH,IAAItuB,EAASlT,KACTyhC,EAAiBvuB,EAAOkB,OAAO9J,GAAG4I,EAAOkF,aACzC1L,EAAQwG,EAAOlF,OAAO8kB,SAASpmB,MAC/B+0B,EAAel9B,KAAK,0BACtBmI,EAAQ+0B,EAAel9B,KAAK,yBAA2B2O,EAAOlF,OAAO8kB,SAASpmB,OAEhFwG,EAAO4f,SAASD,QAAUxmB,EAAMI,SAAS,WACnCyG,EAAOlF,OAAO8kB,SAAS4O,iBACrBxuB,EAAOlF,OAAO4L,MAChB1G,EAAO2J,UACP3J,EAAO6J,UAAU7J,EAAOlF,OAAOgK,OAAO,GAAM,GAC5C9E,EAAO/B,KAAK,aACF+B,EAAOkG,YAGPlG,EAAOlF,OAAO8kB,SAAS6O,gBAIjCzuB,EAAO4f,SAASE,QAHhB9f,EAAO6I,QAAQ7I,EAAOkB,OAAO9R,OAAS,EAAG4Q,EAAOlF,OAAOgK,OAAO,GAAM,GACpE9E,EAAO/B,KAAK,cAJZ+B,EAAO6J,UAAU7J,EAAOlF,OAAOgK,OAAO,GAAM,GAC5C9E,EAAO/B,KAAK,aAOL+B,EAAOlF,OAAO4L,MACvB1G,EAAO2J,UACP3J,EAAO0J,UAAU1J,EAAOlF,OAAOgK,OAAO,GAAM,GAC5C9E,EAAO/B,KAAK,aACF+B,EAAOmG,MAGPnG,EAAOlF,OAAO8kB,SAAS6O,gBAIjCzuB,EAAO4f,SAASE,QAHhB9f,EAAO6I,QAAQ,EAAG7I,EAAOlF,OAAOgK,OAAO,GAAM,GAC7C9E,EAAO/B,KAAK,cAJZ+B,EAAO0J,UAAU1J,EAAOlF,OAAOgK,OAAO,GAAM,GAC5C9E,EAAO/B,KAAK,cAObzE,IAEL2a,MAAO,WACL,IAAInU,EAASlT,KACb,YAAuC,IAA5BkT,EAAO4f,SAASD,WACvB3f,EAAO4f,SAAS8O,UACpB1uB,EAAO4f,SAAS8O,SAAU,EAC1B1uB,EAAO/B,KAAK,iBACZ+B,EAAO4f,SAAS0O,OACT,KAETxO,KAAM,WACJ,IAAI9f,EAASlT,KACb,QAAKkT,EAAO4f,SAAS8O,eACkB,IAA5B1uB,EAAO4f,SAASD,UAEvB3f,EAAO4f,SAASD,UAClB3wB,aAAagR,EAAO4f,SAASD,SAC7B3f,EAAO4f,SAASD,aAAU9rB,GAE5BmM,EAAO4f,SAAS8O,SAAU,EAC1B1uB,EAAO/B,KAAK,iBACL,KAET0wB,MAAO,SAAe7pB,GACpB,IAAI9E,EAASlT,KACRkT,EAAO4f,SAAS8O,UACjB1uB,EAAO4f,SAASgP,SAChB5uB,EAAO4f,SAASD,SAAW3wB,aAAagR,EAAO4f,SAASD,SAC5D3f,EAAO4f,SAASgP,QAAS,EACX,IAAV9pB,GAAgB9E,EAAOlF,OAAO8kB,SAASiP,mBAIzC7uB,EAAOS,WAAW,GAAGvT,iBAAiB,gBAAiB8S,EAAO4f,SAASsI,iBACvEloB,EAAOS,WAAW,GAAGvT,iBAAiB,sBAAuB8S,EAAO4f,SAASsI,mBAJ7EloB,EAAO4f,SAASgP,QAAS,EACzB5uB,EAAO4f,SAAS0O,WAiFlBQ,EAAO,CACT7mB,aAAc,WAGZ,IAFA,IAAIjI,EAASlT,KACToU,EAASlB,EAAOkB,OACX/R,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAAG,CACzC,IAAI6sB,EAAWhc,EAAOkB,OAAO9J,GAAGjI,GAE5B4/B,GADS/S,EAAS,GAAG7W,kBAEpBnF,EAAOlF,OAAOiN,mBAAoBgnB,GAAM/uB,EAAOuF,WACpD,IAAIypB,EAAK,EACJhvB,EAAOI,iBACV4uB,EAAKD,EACLA,EAAK,GAEP,IAAIE,EAAejvB,EAAOlF,OAAOo0B,WAAWC,UACxC3sB,KAAKK,IAAI,EAAIL,KAAKwB,IAAIgY,EAAS,GAAGlW,UAAW,GAC7C,EAAItD,KAAKyL,IAAIzL,KAAKK,IAAImZ,EAAS,GAAGlW,UAAW,GAAI,GACrDkW,EACGzlB,IAAI,CACHqtB,QAASqL,IAEV98B,UAAW,eAAiB48B,EAAK,OAASC,EAAK,cAGtD/pB,cAAe,SAAuB1S,GACpC,IAAIyN,EAASlT,KACToU,EAASlB,EAAOkB,OAChBT,EAAaT,EAAOS,WAExB,GADAS,EAAO5O,WAAWC,GACdyN,EAAOlF,OAAOiN,kBAAiC,IAAbxV,EAAgB,CACpD,IAAI68B,GAAiB,EACrBluB,EAAOlM,cAAc,WACnB,IAAIo6B,GACCpvB,IAAUA,EAAOsJ,UAAtB,CACA8lB,GAAiB,EACjBpvB,EAAO4I,WAAY,EAEnB,IADA,IAAIymB,EAAgB,CAAC,sBAAuB,iBACnClgC,EAAI,EAAGA,EAAIkgC,EAAcjgC,OAAQD,GAAK,EAC7CsR,EAAWjM,QAAQ66B,EAAclgC,UAoDvCmgC,EAAO,CACTrnB,aAAc,WACZ,IAYIsnB,EAZAvvB,EAASlT,KACTmT,EAAMD,EAAOC,IACbQ,EAAaT,EAAOS,WACpBS,EAASlB,EAAOkB,OAChBsuB,EAAcxvB,EAAOF,MACrB2vB,EAAezvB,EAAOD,OACtBY,EAAMX,EAAOY,aACbF,EAAaV,EAAOO,KACpBzF,EAASkF,EAAOlF,OAAO40B,WACvBtvB,EAAeJ,EAAOI,eACtBU,EAAYd,EAAOe,SAAWf,EAAOlF,OAAOiG,QAAQC,QACpD2uB,EAAgB,EAEhB70B,EAAO80B,SACLxvB,GAE2B,KAD7BmvB,EAAgB9uB,EAAWhI,KAAK,wBACdrJ,SAChBmgC,EAAgBlgC,EAAE,0CAClBoR,EAAWnJ,OAAOi4B,IAEpBA,EAAch5B,IAAI,CAAEwJ,OAASyvB,EAAc,QAGd,KAD7BD,EAAgBtvB,EAAIxH,KAAK,wBACPrJ,SAChBmgC,EAAgBlgC,EAAE,0CAClB4Q,EAAI3I,OAAOi4B,KAIjB,IAAK,IAAIpgC,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAAG,CACzC,IAAI6sB,EAAW9a,EAAO9J,GAAGjI,GACrB4Z,EAAa5Z,EACb2R,IACFiI,EAAazI,SAAS0b,EAAS3qB,KAAK,2BAA4B,KAElE,IAAIw+B,EAA0B,GAAb9mB,EACb+mB,EAAQttB,KAAKC,MAAMotB,EAAa,KAChClvB,IACFkvB,GAAcA,EACdC,EAAQttB,KAAKC,OAAOotB,EAAa,MAEnC,IAAI/pB,EAAWtD,KAAKK,IAAIL,KAAKyL,IAAI+N,EAAS,GAAGlW,SAAU,IAAK,GACxDipB,EAAK,EACLC,EAAK,EACLe,EAAK,EACLhnB,EAAa,GAAM,GACrBgmB,EAAc,GAARe,EAAYpvB,EAClBqvB,EAAK,IACKhnB,EAAa,GAAK,GAAM,GAClCgmB,EAAK,EACLgB,EAAc,GAARD,EAAYpvB,IACRqI,EAAa,GAAK,GAAM,GAClCgmB,EAAKruB,EAAsB,EAARovB,EAAYpvB,EAC/BqvB,EAAKrvB,IACKqI,EAAa,GAAK,GAAM,IAClCgmB,GAAMruB,EACNqvB,EAAM,EAAIrvB,EAA4B,EAAbA,EAAiBovB,GAExCnvB,IACFouB,GAAMA,GAGH3uB,IACH4uB,EAAKD,EACLA,EAAK,GAGP,IAAI58B,EAAY,YAAciO,EAAe,GAAKyvB,GAAc,iBAAmBzvB,EAAeyvB,EAAa,GAAK,oBAAsBd,EAAK,OAASC,EAAK,OAASe,EAAK,MAM3K,GALIjqB,GAAY,IAAiB,EAAZA,IACnB6pB,EAA8B,GAAb5mB,EAA+B,GAAXjD,EACjCnF,IAAOgvB,EAA+B,IAAb5mB,EAA+B,GAAXjD,IAEnDkW,EAAS7pB,UAAUA,GACf2I,EAAOk1B,aAAc,CAEvB,IAAIC,EAAe7vB,EAAe4b,EAASvjB,KAAK,6BAA+BujB,EAASvjB,KAAK,4BACzFy3B,EAAc9vB,EAAe4b,EAASvjB,KAAK,8BAAgCujB,EAASvjB,KAAK,+BACjE,IAAxBw3B,EAAa7gC,SACf6gC,EAAe5gC,EAAG,oCAAuC+Q,EAAe,OAAS,OAAS,YAC1F4b,EAAS1kB,OAAO24B,IAES,IAAvBC,EAAY9gC,SACd8gC,EAAc7gC,EAAG,oCAAuC+Q,EAAe,QAAU,UAAY,YAC7F4b,EAAS1kB,OAAO44B,IAEdD,EAAa7gC,SAAU6gC,EAAa,GAAGliC,MAAM61B,QAAUphB,KAAKK,KAAKiD,EAAU,IAC3EoqB,EAAY9gC,SAAU8gC,EAAY,GAAGniC,MAAM61B,QAAUphB,KAAKK,IAAIiD,EAAU,KAUhF,GAPArF,EAAWlK,IAAI,CACb45B,2BAA6B,YAAezvB,EAAa,EAAK,KAC9D0vB,wBAA0B,YAAe1vB,EAAa,EAAK,KAC3D2vB,uBAAyB,YAAe3vB,EAAa,EAAK,KAC1D4vB,mBAAqB,YAAe5vB,EAAa,EAAK,OAGpD5F,EAAO80B,OACT,GAAIxvB,EACFmvB,EAAcp9B,UAAW,qBAAwBq9B,EAAc,EAAK10B,EAAOy1B,cAAgB,QAAWf,EAAc,EAAK,0CAA6C10B,EAAkB,YAAI,SACvL,CACL,IAAI01B,EAAchuB,KAAKwB,IAAI2rB,GAA6D,GAA3CntB,KAAKC,MAAMD,KAAKwB,IAAI2rB,GAAiB,IAC9E3E,EAAa,KACdxoB,KAAKiuB,IAAmB,EAAdD,EAAkBhuB,KAAKwO,GAAM,KAAO,EAC5CxO,KAAKkuB,IAAmB,EAAdF,EAAkBhuB,KAAKwO,GAAM,KAAO,GAE/C2f,EAAS71B,EAAO81B,YAChBC,EAAS/1B,EAAO81B,YAAc5F,EAC9Bp1B,EAASkF,EAAOy1B,aACpBhB,EAAcp9B,UAAW,WAAaw+B,EAAS,QAAUE,EAAS,uBAA0BpB,EAAe,EAAK75B,GAAU,QAAW65B,EAAe,EAAIoB,EAAU,uBAGtK,IAAIC,EAAW1b,EAAQE,UAAYF,EAAQG,aAAiB7U,EAAa,EAAK,EAC9ED,EACGtO,UAAW,qBAAuB2+B,EAAU,gBAAkB9wB,EAAOI,eAAiB,EAAIuvB,GAAiB,iBAAmB3vB,EAAOI,gBAAkBuvB,EAAgB,GAAK,SAEjL1qB,cAAe,SAAuB1S,GACpC,IACI0N,EADSnT,KACImT,IADJnT,KAEOoU,OAEjB5O,WAAWC,GACXkG,KAAK,gHACLnG,WAAWC,GANDzF,KAOFgO,OAAO40B,WAAWE,SAPhB9iC,KAOkCsT,gBAC7CH,EAAIxH,KAAK,uBAAuBnG,WAAWC,KAwD7Cw+B,GAAO,CACT9oB,aAAc,WAIZ,IAHA,IAAIjI,EAASlT,KACToU,EAASlB,EAAOkB,OAChBP,EAAMX,EAAOY,aACRzR,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAAG,CACzC,IAAI6sB,EAAW9a,EAAO9J,GAAGjI,GACrB2W,EAAWkW,EAAS,GAAGlW,SACvB9F,EAAOlF,OAAOk2B,WAAWC,gBAC3BnrB,EAAWtD,KAAKK,IAAIL,KAAKyL,IAAI+N,EAAS,GAAGlW,SAAU,IAAK,IAE1D,IAEIorB,GADU,IAAMprB,EAEhBqrB,EAAU,EACVpC,GAJS/S,EAAS,GAAG7W,kBAKrB6pB,EAAK,EAYT,GAXKhvB,EAAOI,eAKDO,IACTuwB,GAAWA,IALXlC,EAAKD,EAELoC,GAAWD,EACXA,EAFAnC,EAAK,GAOP/S,EAAS,GAAGjuB,MAAMqjC,QAAU5uB,KAAKwB,IAAIxB,KAAKstB,MAAMhqB,IAAa5E,EAAO9R,OAEhE4Q,EAAOlF,OAAOk2B,WAAWhB,aAAc,CAEzC,IAAIC,EAAejwB,EAAOI,eAAiB4b,EAASvjB,KAAK,6BAA+BujB,EAASvjB,KAAK,4BAClGy3B,EAAclwB,EAAOI,eAAiB4b,EAASvjB,KAAK,8BAAgCujB,EAASvjB,KAAK,+BAC1E,IAAxBw3B,EAAa7gC,SACf6gC,EAAe5gC,EAAG,oCAAuC2Q,EAAOI,eAAiB,OAAS,OAAS,YACnG4b,EAAS1kB,OAAO24B,IAES,IAAvBC,EAAY9gC,SACd8gC,EAAc7gC,EAAG,oCAAuC2Q,EAAOI,eAAiB,QAAU,UAAY,YACtG4b,EAAS1kB,OAAO44B,IAEdD,EAAa7gC,SAAU6gC,EAAa,GAAGliC,MAAM61B,QAAUphB,KAAKK,KAAKiD,EAAU,IAC3EoqB,EAAY9gC,SAAU8gC,EAAY,GAAGniC,MAAM61B,QAAUphB,KAAKK,IAAIiD,EAAU,IAE9EkW,EACG7pB,UAAW,eAAiB48B,EAAK,OAASC,EAAK,oBAAsBmC,EAAU,gBAAkBD,EAAU,UAGlHjsB,cAAe,SAAuB1S,GACpC,IAAIyN,EAASlT,KACToU,EAASlB,EAAOkB,OAChBgE,EAAclF,EAAOkF,YACrBzE,EAAaT,EAAOS,WAKxB,GAJAS,EACG5O,WAAWC,GACXkG,KAAK,gHACLnG,WAAWC,GACVyN,EAAOlF,OAAOiN,kBAAiC,IAAbxV,EAAgB,CACpD,IAAI68B,GAAiB,EAErBluB,EAAO9J,GAAG8N,GAAalQ,cAAc,WACnC,IAAIo6B,GACCpvB,IAAUA,EAAOsJ,UAAtB,CAEA8lB,GAAiB,EACjBpvB,EAAO4I,WAAY,EAEnB,IADA,IAAIymB,EAAgB,CAAC,sBAAuB,iBACnClgC,EAAI,EAAGA,EAAIkgC,EAAcjgC,OAAQD,GAAK,EAC7CsR,EAAWjM,QAAQ66B,EAAclgC,UAsDvCkiC,GAAY,CACdppB,aAAc,WAcZ,IAbA,IAAIjI,EAASlT,KACT0iC,EAAcxvB,EAAOF,MACrB2vB,EAAezvB,EAAOD,OACtBmB,EAASlB,EAAOkB,OAChBT,EAAaT,EAAOS,WACpBa,EAAkBtB,EAAOsB,gBACzBxG,EAASkF,EAAOlF,OAAOw2B,gBACvBlxB,EAAeJ,EAAOI,eACtBjO,EAAY6N,EAAOuF,UACnBgsB,EAASnxB,EAA6BovB,EAAc,EAA3Br9B,EAA8Cs9B,EAAe,EAA5Bt9B,EAC1Dq/B,EAASpxB,EAAetF,EAAO02B,QAAU12B,EAAO02B,OAChDjsB,EAAYzK,EAAO22B,MAEdtiC,EAAI,EAAGC,EAAS8R,EAAO9R,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CAC1D,IAAI6sB,EAAW9a,EAAO9J,GAAGjI,GACrB8S,EAAYX,EAAgBnS,GAE5BuiC,GAAqBH,EADPvV,EAAS,GAAG7W,kBACmBlD,EAAY,GAAMA,EAAanH,EAAO62B,SAEnFT,EAAU9wB,EAAeoxB,EAASE,EAAmB,EACrDP,EAAU/wB,EAAe,EAAIoxB,EAASE,EAEtCE,GAAcrsB,EAAY/C,KAAKwB,IAAI0tB,GAEnClJ,EAAapoB,EAAe,EAAItF,EAAO+2B,QAAU,EACjDtJ,EAAanoB,EAAetF,EAAO+2B,QAAU,EAAqB,EAGlErvB,KAAKwB,IAAIukB,GAAc,OAASA,EAAa,GAC7C/lB,KAAKwB,IAAIwkB,GAAc,OAASA,EAAa,GAC7ChmB,KAAKwB,IAAI4tB,GAAc,OAASA,EAAa,GAC7CpvB,KAAKwB,IAAIktB,GAAW,OAASA,EAAU,GACvC1uB,KAAKwB,IAAImtB,GAAW,OAASA,EAAU,GAE3C,IAAIW,EAAiB,eAAiBvJ,EAAa,MAAQC,EAAa,MAAQoJ,EAAa,gBAAkBT,EAAU,gBAAkBD,EAAU,OAIrJ,GAFAlV,EAAS7pB,UAAU2/B,GACnB9V,EAAS,GAAGjuB,MAAMqjC,OAAmD,EAAzC5uB,KAAKwB,IAAIxB,KAAKstB,MAAM4B,IAC5C52B,EAAOk1B,aAAc,CAEvB,IAAI+B,EAAkB3xB,EAAe4b,EAASvjB,KAAK,6BAA+BujB,EAASvjB,KAAK,4BAC5Fu5B,EAAiB5xB,EAAe4b,EAASvjB,KAAK,8BAAgCujB,EAASvjB,KAAK,+BACjE,IAA3Bs5B,EAAgB3iC,SAClB2iC,EAAkB1iC,EAAG,oCAAuC+Q,EAAe,OAAS,OAAS,YAC7F4b,EAAS1kB,OAAOy6B,IAEY,IAA1BC,EAAe5iC,SACjB4iC,EAAiB3iC,EAAG,oCAAuC+Q,EAAe,QAAU,UAAY,YAChG4b,EAAS1kB,OAAO06B,IAEdD,EAAgB3iC,SAAU2iC,EAAgB,GAAGhkC,MAAM61B,QAA6B,EAAnB8N,EAAuBA,EAAmB,GACvGM,EAAe5iC,SAAU4iC,EAAe,GAAGjkC,MAAM61B,QAAgC,GAApB8N,GAAyBA,EAAmB,KAK7Gx1B,EAAQI,eAAiBJ,EAAQO,yBAC1BgE,EAAW,GAAG1S,MACpBkkC,kBAAoBV,EAAS,WAGpCtsB,cAAe,SAAuB1S,GACvBzF,KACNoU,OACJ5O,WAAWC,GACXkG,KAAK,gHACLnG,WAAWC,KAkDdmL,GAAa,CACfsb,EACAC,EACAE,EACAE,EACAqB,EACAyB,EACAuB,EAz6FiB,CACjBre,KAAM,aACNvE,OAAQ,CACNskB,WAAY,CACVpe,SAAS,EACTqe,gBAAgB,EAChBI,QAAQ,EACRD,aAAa,EACbE,YAAa,EACbM,aAAc,cAGlB/gB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBof,WAAY,CACVpe,SAAS,EACTwc,OAAQG,EAAWH,OAAOze,KAAKiB,GAC/Byd,QAASE,EAAWF,QAAQ1e,KAAKiB,GACjCuc,OAAQoB,EAAWpB,OAAOxd,KAAKiB,GAC/Bif,iBAAkBtB,EAAWsB,iBAAiBlgB,KAAKiB,GACnDmf,iBAAkBxB,EAAWwB,iBAAiBpgB,KAAKiB,GACnD4d,eAAgBzkB,EAAMM,UAI5B/G,GAAI,CACFgjB,KAAM,WACS5oB,KACFgO,OAAOskB,WAAWpe,SADhBlU,KACkCsyB,WAAW5B,UAE5D7E,QAAS,WACM7rB,KACFsyB,WAAWpe,SADTlU,KAC2BsyB,WAAW3B,aAqGtC,CACjBpe,KAAM,aACNvE,OAAQ,CACNwb,WAAY,CACVgK,OAAQ,KACRC,OAAQ,KAER2R,aAAa,EACb9R,cAAe,yBACf+C,YAAa,uBACb9C,UAAW,uBAGfphB,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBwpB,WAAY,CACVZ,KAAMuK,EAAWvK,KAAK3W,KAHbjS,MAIT8S,OAAQqgB,EAAWrgB,OAAOb,KAJjBjS,MAKT6rB,QAASsH,EAAWtH,QAAQ5Z,KALnBjS,UASf4F,GAAI,CACFgjB,KAAM,WACS5oB,KACNwpB,WAAWZ,OADL5oB,KAENwpB,WAAW1W,UAEpBuyB,OAAQ,WACOrlC,KACNwpB,WAAW1W,UAEpBwyB,SAAU,WACKtlC,KACNwpB,WAAW1W,UAEpB+Y,QAAS,WACM7rB,KACNwpB,WAAWqC,WAEpB4T,MAAO,SAAep5B,GACpB,IACI0nB,EADS/tB,KACIwpB,WACb4J,EAAUrF,EAAIqF,QACdC,EAAUtF,EAAIsF,SAHLrzB,KAKJgO,OAAOwb,WAAW4b,aACrB7iC,EAAE8D,EAAEC,QAAQI,GAAG2sB,IACf9wB,EAAE8D,EAAEC,QAAQI,GAAG0sB,KAEfA,GAAWA,EAAQ/uB,YATZrE,KAS+BgO,OAAOwb,WAAW6M,aACxDhD,GAAWA,EAAQhvB,YAVZrE,KAU+BgO,OAAOwb,WAAW6M,iBAkPjD,CACjB9jB,KAAM,aACNvE,OAAQ,CACN2lB,WAAY,CACVzuB,GAAI,KACJqgC,cAAe,OACftP,WAAW,EACXmP,aAAa,EACb1P,aAAc,KACdK,kBAAmB,KACnBH,eAAgB,KAChBN,aAAc,KACdJ,qBAAqB,EACrBtT,KAAM,UACNuS,gBAAgB,EAChBE,mBAAoB,EACpBU,sBAAuB,SAAUyQ,GAAU,OAAOA,GAClDxQ,oBAAqB,SAAUwQ,GAAU,OAAOA,GAChD7P,YAAa,2BACbjB,kBAAmB,kCACnByB,cAAe,qBACfN,aAAc,4BACdC,WAAY,0BACZO,YAAa,2BACbL,qBAAsB,qCACtBI,yBAA0B,yCAC1BF,eAAgB,8BAChB3C,UAAW,2BAGfphB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBygB,WAAY,CACV/K,KAAM8K,EAAW9K,KAAK3W,KAAKiB,GAC3BqiB,OAAQ7B,EAAW6B,OAAOtjB,KAAKiB,GAC/BJ,OAAQ4gB,EAAW5gB,OAAOb,KAAKiB,GAC/B2Y,QAAS6H,EAAW7H,QAAQ5Z,KAAKiB,GACjCohB,mBAAoB,MAI1B1uB,GAAI,CACFgjB,KAAM,WACS5oB,KACN2zB,WAAW/K,OADL5oB,KAEN2zB,WAAW4B,SAFLv1B,KAGN2zB,WAAW7gB,UAEpB2yB,kBAAmB,WACJzlC,KACFgO,OAAO4L,KADL5Z,KAEJ2zB,WAAW7gB,cACmB,IAH1B9S,KAGYua,WAHZva,KAIJ2zB,WAAW7gB,UAGtB4yB,gBAAiB,WACF1lC,KACDgO,OAAO4L,MADN5Z,KAEJ2zB,WAAW7gB,UAGtB6yB,mBAAoB,WACL3lC,KACFgO,OAAO4L,OADL5Z,KAEJ2zB,WAAW4B,SAFPv1B,KAGJ2zB,WAAW7gB,WAGtB8yB,qBAAsB,WACP5lC,KACDgO,OAAO4L,OADN5Z,KAEJ2zB,WAAW4B,SAFPv1B,KAGJ2zB,WAAW7gB,WAGtB+Y,QAAS,WACM7rB,KACN2zB,WAAW9H,WAEpB4T,MAAO,SAAep5B,GACpB,IAAI6M,EAASlT,KAEXkT,EAAOlF,OAAO2lB,WAAWzuB,IACtBgO,EAAOlF,OAAO2lB,WAAWyR,aACM,EAA/BlyB,EAAOygB,WAAWxgB,IAAI7Q,SACrBC,EAAE8D,EAAEC,QAAQnC,SAAS+O,EAAOlF,OAAO2lB,WAAWgC,cAElDziB,EAAOygB,WAAWxgB,IAAI9O,YAAY6O,EAAOlF,OAAO2lB,WAAW0C,gBA8RjD,CAChB9jB,KAAM,YACNvE,OAAQ,CACNuoB,UAAW,CACTrxB,GAAI,KACJsxB,SAAU,OACVK,MAAM,EACNoB,WAAW,EACXN,eAAe,EACfpE,UAAW,wBACXsS,UAAW,0BAGf1zB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBqjB,UAAW,CACT3N,KAAM0N,EAAU1N,KAAK3W,KAAKiB,GAC1B2Y,QAASyK,EAAUzK,QAAQ5Z,KAAKiB,GAChCH,WAAYujB,EAAUvjB,WAAWd,KAAKiB,GACtCiI,aAAcmb,EAAUnb,aAAalJ,KAAKiB,GAC1CiF,cAAeme,EAAUne,cAAclG,KAAKiB,GAC5C0kB,gBAAiBtB,EAAUsB,gBAAgB3lB,KAAKiB,GAChD4kB,iBAAkBxB,EAAUwB,iBAAiB7lB,KAAKiB,GAClDikB,gBAAiBb,EAAUa,gBAAgBllB,KAAKiB,GAChDqkB,YAAajB,EAAUiB,YAAYtlB,KAAKiB,GACxCukB,WAAYnB,EAAUmB,WAAWxlB,KAAKiB,GACtCwkB,UAAWpB,EAAUoB,UAAUzlB,KAAKiB,GACpC4O,WAAW,EACX+Q,QAAS,KACT2E,YAAa,SAInB5xB,GAAI,CACFgjB,KAAM,WACS5oB,KACNu2B,UAAU3N,OADJ5oB,KAENu2B,UAAUxjB,aAFJ/S,KAGNu2B,UAAUpb,gBAEnBrI,OAAQ,WACO9S,KACNu2B,UAAUxjB,cAEnByZ,OAAQ,WACOxsB,KACNu2B,UAAUxjB,cAEnBma,eAAgB,WACDltB,KACNu2B,UAAUxjB,cAEnBoI,aAAc,WACCnb,KACNu2B,UAAUpb,gBAEnBhD,cAAe,SAAuB1S,GACvBzF,KACNu2B,UAAUpe,cAAc1S,IAEjComB,QAAS,WACM7rB,KACNu2B,UAAU1K,aAyFN,CACftZ,KAAM,WACNvE,OAAQ,CACNuqB,SAAU,CACRrkB,SAAS,IAGb/B,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBu4B,SAAU,CACRJ,aAAcD,EAASC,aAAalmB,KAH3BjS,MAITmb,aAAc+c,EAAS/c,aAAalJ,KAJ3BjS,MAKTmY,cAAe+f,EAAS/f,cAAclG,KAL7BjS,UASf4F,GAAI,CACF0pB,WAAY,WACGtvB,KACDgO,OAAOuqB,SAASrkB,UADflU,KAENgO,OAAO4J,qBAAsB,IAEtCgR,KAAM,WACS5oB,KACDgO,OAAOuqB,UADNv4B,KAENu4B,SAASpd,gBAElBA,aAAc,WACCnb,KACDgO,OAAOuqB,UADNv4B,KAENu4B,SAASpd,gBAElBhD,cAAe,SAAuB1S,GACvBzF,KACDgO,OAAOuqB,UADNv4B,KAENu4B,SAASpgB,cAAc1S,MAuavB,CACX8M,KAAM,OACNvE,OAAQ,CACNmrB,KAAM,CACJjlB,SAAS,EACTwlB,SAAU,EACVI,SAAU,EACVx1B,QAAQ,EACRwhC,eAAgB,wBAChBC,iBAAkB,wBAGtB5zB,OAAQ,WACN,IAAIe,EAASlT,KACTm5B,EAAO,CACTjlB,SAAS,EACTihB,MAAO,EACPmD,aAAc,EACdqB,WAAW,EACXP,QAAS,CACPlK,cAAUnoB,EACVmzB,gBAAYnzB,EACZozB,iBAAapzB,EACbyyB,cAAUzyB,EACV0yB,kBAAc1yB,EACd2yB,SAAU,GAEZrP,MAAO,CACLvI,eAAW/a,EACXgb,aAAShb,EACTqb,cAAUrb,EACVwb,cAAUxb,EACVuzB,UAAMvzB,EACNyzB,UAAMzzB,EACNwzB,UAAMxzB,EACN0zB,UAAM1zB,EACNiM,WAAOjM,EACPkM,YAAQlM,EACR0b,YAAQ1b,EACR2b,YAAQ3b,EACRkzB,aAAc,GACdS,eAAgB,IAElB/U,SAAU,CACRtK,OAAGtU,EACHuU,OAAGvU,EACH4zB,mBAAe5zB,EACf6zB,mBAAe7zB,EACf8zB,cAAU9zB,IAGd,+HAAiI5D,MAAM,KAAK+I,QAAQ,SAAUC,GAC5JgtB,EAAKhtB,GAAcysB,EAAKzsB,GAAY8F,KAAKiB,KAE3C7G,EAAMqC,OAAOwE,EAAQ,CACnBimB,KAAMA,KAGVvzB,GAAI,CACFgjB,KAAM,WACS5oB,KACFgO,OAAOmrB,KAAKjlB,SADVlU,KAEJm5B,KAAKzI,UAGhB7E,QAAS,WACM7rB,KACNm5B,KAAKxI,WAEdqV,WAAY,SAAoB3/B,GACjBrG,KACDm5B,KAAKjlB,SADJlU,KAENm5B,KAAK5X,aAAalb,IAE3B4/B,SAAU,SAAkB5/B,GACbrG,KACDm5B,KAAKjlB,SADJlU,KAENm5B,KAAKnU,WAAW3e,IAEzB6/B,UAAW,SAAmB7/B,GACfrG,KACFgO,OAAOmrB,KAAKjlB,SADVlU,KAC4Bm5B,KAAKjlB,SADjClU,KACmDgO,OAAOmrB,KAAK70B,QAD/DtE,KAEJm5B,KAAK70B,OAAO+B,IAGvB6B,cAAe,WACAlI,KACFm5B,KAAKjlB,SADHlU,KACqBgO,OAAOmrB,KAAKjlB,SADjClU,KAEJm5B,KAAKiC,qBA4IP,CACX7oB,KAAM,OACNvE,OAAQ,CACN2gB,KAAM,CACJza,SAAS,EACT8oB,cAAc,EACdC,mBAAoB,EACpBkJ,uBAAuB,EAEvB9J,aAAc,cACdE,aAAc,sBACdD,YAAa,qBACb8J,eAAgB,0BAGpBj0B,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnB2uB,KAAM,CACJmO,oBAAoB,EACpBlO,KAAMqN,EAAKrN,KAAK3c,KAJPjS,MAKTk8B,YAAaD,EAAKC,YAAYjqB,KALrBjS,UASf4F,GAAI,CACF0pB,WAAY,WACGtvB,KACFgO,OAAO2gB,KAAKza,SADVlU,KAC4BgO,OAAO8a,gBADnC9oB,KAEJgO,OAAO8a,eAAgB,IAGlCF,KAAM,WACS5oB,KACFgO,OAAO2gB,KAAKza,UADVlU,KAC6BgO,OAAO4L,MAAuC,IAD3E5Z,KACmDgO,OAAOmO,cAD1Dnc,KAEJ2uB,KAAKC,QAGhByX,OAAQ,WACOrmC,KACFgO,OAAOiT,WADLjhB,KACyBgO,OAAOwY,gBADhCxmB,KAEJ2uB,KAAKC,QAGhBpC,OAAQ,WACOxsB,KACFgO,OAAO2gB,KAAKza,SADVlU,KAEJ2uB,KAAKC,QAGhB0X,kBAAmB,WACJtmC,KACFgO,OAAO2gB,KAAKza,SADVlU,KAEJ2uB,KAAKC,QAGhBnT,gBAAiB,WACf,IAAIvI,EAASlT,KACTkT,EAAOlF,OAAO2gB,KAAKza,UACjBhB,EAAOlF,OAAO2gB,KAAKwX,wBAA2BjzB,EAAOlF,OAAO2gB,KAAKwX,wBAA0BjzB,EAAOyb,KAAKmO,qBACzG5pB,EAAOyb,KAAKC,QAIlB1mB,cAAe,WACAlI,KACFgO,OAAO2gB,KAAKza,UADVlU,KAC6BgO,OAAO2gB,KAAKwX,uBADzCnmC,KAEJ2uB,KAAKC,UAqID,CACjBrc,KAAM,aACNvE,OAAQ,CACN+vB,WAAY,CACVM,aAASt3B,EACTy3B,SAAS,EACTD,GAAI,UAGRpsB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnB6qB,WAAY,CACVM,QAASnrB,EAAOlF,OAAO+vB,WAAWM,QAClCR,uBAAwBR,EAAWQ,uBAAuB5rB,KAAKiB,GAC/DiI,aAAckiB,EAAWliB,aAAalJ,KAAKiB,GAC3CiF,cAAeklB,EAAWllB,cAAclG,KAAKiB,OAInDtN,GAAI,CACFkN,OAAQ,WACO9S,KACD+9B,WAAWM,SADVr+B,KAEF+9B,WAAWC,SAFTh+B,KAGJ+9B,WAAWC,YAASj3B,SAHhB/G,KAIG+9B,WAAWC,SAG7BxR,OAAQ,WACOxsB,KACD+9B,WAAWM,SADVr+B,KAEF+9B,WAAWC,SAFTh+B,KAGJ+9B,WAAWC,YAASj3B,SAHhB/G,KAIG+9B,WAAWC,SAG7B9Q,eAAgB,WACDltB,KACD+9B,WAAWM,SADVr+B,KAEF+9B,WAAWC,SAFTh+B,KAGJ+9B,WAAWC,YAASj3B,SAHhB/G,KAIG+9B,WAAWC,SAG7B7iB,aAAc,SAAsB1C,EAAW2C,GAChCpb,KACD+9B,WAAWM,SADVr+B,KAEN+9B,WAAW5iB,aAAa1C,EAAW2C,IAE5CjD,cAAe,SAAuB1S,EAAU2V,GACjCpb,KACD+9B,WAAWM,SADVr+B,KAEN+9B,WAAW5lB,cAAc1S,EAAU2V,MA2JrC,CACT7I,KAAM,OACNvE,OAAQ,CACN0wB,KAAM,CACJxqB,SAAS,EACTqyB,kBAAmB,sBACnB/G,iBAAkB,iBAClBF,iBAAkB,aAClBC,kBAAmB,0BACnBF,iBAAkB,yBAClBY,wBAAyB,0BAG7B9tB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBwrB,KAAM,CACJkB,WAAYr9B,EAAG,gBAAoB2Q,EAAOlF,OAAO0wB,KAAsB,kBAAI,yDAG/E1yB,OAAOC,KAAKyyB,GAAMxyB,QAAQ,SAAUC,GAClC+G,EAAOwrB,KAAKvyB,GAAcuyB,EAAKvyB,GAAY8F,KAAKiB,MAGpDtN,GAAI,CACFgjB,KAAM,WACS5oB,KACDgO,OAAO0wB,KAAKxqB,UADXlU,KAEN0+B,KAAK9V,OAFC5oB,KAGN0+B,KAAKmB,qBAEdwF,OAAQ,WACOrlC,KACDgO,OAAO0wB,KAAKxqB,SADXlU,KAEN0+B,KAAKmB,oBAEdyF,SAAU,WACKtlC,KACDgO,OAAO0wB,KAAKxqB,SADXlU,KAEN0+B,KAAKmB,oBAEd2G,iBAAkB,WACHxmC,KACDgO,OAAO0wB,KAAKxqB,SADXlU,KAEN0+B,KAAKoB,oBAEdjU,QAAS,WACM7rB,KACDgO,OAAO0wB,KAAKxqB,SADXlU,KAEN0+B,KAAK7S,aAoFF,CACdtZ,KAAM,UACNvE,OAAQ,CACNtM,QAAS,CACPwS,SAAS,EACTssB,cAAc,EACdv7B,IAAK,WAGTkN,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBxR,QAAS,CACPknB,KAAMsX,EAAQtX,KAAK3W,KAAKiB,GACxB2tB,WAAYX,EAAQW,WAAW5uB,KAAKiB,GACpCutB,mBAAoBP,EAAQO,mBAAmBxuB,KAAKiB,GACpDqtB,cAAeL,EAAQK,cAActuB,KAAKiB,GAC1C2Y,QAASqU,EAAQrU,QAAQ5Z,KAAKiB,OAIpCtN,GAAI,CACFgjB,KAAM,WACS5oB,KACFgO,OAAOtM,QAAQwS,SADblU,KAEJ0B,QAAQknB,QAGnBiD,QAAS,WACM7rB,KACFgO,OAAOtM,QAAQwS,SADblU,KAEJ0B,QAAQmqB,WAGnB3jB,cAAe,WACAlI,KACF0B,QAAQ0a,aADNpc,KAEJ0B,QAAQm/B,WAFJ7gC,KAEsBgO,OAAOtM,QAAQuD,IAFrCjF,KAEiDoY,gBAqD7C,CACrB7F,KAAM,kBACNvE,OAAQ,CACNoyB,eAAgB,CACdlsB,SAAS,EACTssB,cAAc,EACdc,YAAY,IAGhBnvB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBktB,eAAgB,CACdhkB,aAAa,EACbwM,KAAMsY,EAAetY,KAAK3W,KAAKiB,GAC/B2Y,QAASqV,EAAerV,QAAQ5Z,KAAKiB,GACrCmuB,QAASH,EAAeG,QAAQpvB,KAAKiB,GACrCiuB,YAAaD,EAAeC,YAAYlvB,KAAKiB,OAInDtN,GAAI,CACFgjB,KAAM,WACS5oB,KACFgO,OAAOoyB,eAAelsB,SADpBlU,KAEJogC,eAAexX,QAG1BiD,QAAS,WACM7rB,KACFgO,OAAOoyB,eAAelsB,SADpBlU,KAEJogC,eAAevU,WAG1B3jB,cAAe,WACAlI,KACFogC,eAAehkB,aADbpc,KAEJogC,eAAeiB,aAoFb,CACf9uB,KAAM,WACNvE,OAAQ,CACN8kB,SAAU,CACR5e,SAAS,EACTxH,MAAO,IACPq1B,mBAAmB,EACnB0E,sBAAsB,EACtB9E,iBAAiB,EACjBD,kBAAkB,IAGtBvvB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnB4f,SAAU,CACR8O,SAAS,EACTE,QAAQ,EACRN,IAAKD,EAASC,IAAIvvB,KAAKiB,GACvBmU,MAAOka,EAASla,MAAMpV,KAAKiB,GAC3B8f,KAAMuO,EAASvO,KAAK/gB,KAAKiB,GACzB2uB,MAAON,EAASM,MAAM5vB,KAAKiB,GAC3BkoB,gBAAiB,SAAyB/0B,GACnC6M,IAAUA,EAAOsJ,WAActJ,EAAOS,YACvCtN,EAAEC,SAAWtG,OACjBkT,EAAOS,WAAW,GAAGtT,oBAAoB,gBAAiB6S,EAAO4f,SAASsI,iBAC1EloB,EAAOS,WAAW,GAAGtT,oBAAoB,sBAAuB6S,EAAO4f,SAASsI,iBAChFloB,EAAO4f,SAASgP,QAAS,EACpB5uB,EAAO4f,SAAS8O,QAGnB1uB,EAAO4f,SAAS0O,MAFhBtuB,EAAO4f,SAASE,aAQ1BptB,GAAI,CACFgjB,KAAM,WACS5oB,KACFgO,OAAO8kB,SAAS5e,SADdlU,KAEJ8yB,SAASzL,SAGpBqf,sBAAuB,SAA+B1uB,EAAOgE,GAC9Chc,KACF8yB,SAAS8O,UACd5lB,IAFOhc,KAEagO,OAAO8kB,SAAS2T,qBAF7BzmC,KAGF8yB,SAAS+O,MAAM7pB,GAHbhY,KAKF8yB,SAASE,SAItB2T,gBAAiB,WACF3mC,KACF8yB,SAAS8O,UADP5hC,KAEAgO,OAAO8kB,SAAS2T,qBAFhBzmC,KAGF8yB,SAASE,OAHPhzB,KAKF8yB,SAAS+O,UAItBhW,QAAS,WACM7rB,KACF8yB,SAAS8O,SADP5hC,KAEJ8yB,SAASE,UAmDP,CACfzgB,KAAM,cACNvE,OAAQ,CACNo0B,WAAY,CACVC,WAAW,IAGflwB,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBoiC,WAAY,CACVjnB,aAAc6mB,EAAK7mB,aAAalJ,KAHvBjS,MAITmY,cAAe6pB,EAAK7pB,cAAclG,KAJzBjS,UAQf4F,GAAI,CACF0pB,WAAY,WACV,IAAIpc,EAASlT,KACb,GAA6B,SAAzBkT,EAAOlF,OAAOoJ,OAAlB,CACAlE,EAAOwW,WAAWzmB,KAAOiQ,EAAOlF,OAA6B,uBAAI,QACjE,IAAIuhB,EAAkB,CACpB1Z,cAAe,EACfJ,gBAAiB,EACjB0B,eAAgB,EAChBS,qBAAqB,EACrB7C,aAAc,EACdkG,kBAAkB,GAEpB5O,EAAMqC,OAAOwE,EAAOlF,OAAQuhB,GAC5BljB,EAAMqC,OAAOwE,EAAO6U,eAAgBwH,KAEtCpU,aAAc,WAEiB,SADhBnb,KACFgO,OAAOoJ,QADLpX,KAENoiC,WAAWjnB,gBAEpBhD,cAAe,SAAuB1S,GAEP,SADhBzF,KACFgO,OAAOoJ,QADLpX,KAENoiC,WAAWjqB,cAAc1S,MAwIrB,CACf8M,KAAM,cACNvE,OAAQ,CACN40B,WAAY,CACVM,cAAc,EACdJ,QAAQ,EACRW,aAAc,GACdK,YAAa,MAGjB3xB,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnB4iC,WAAY,CACVznB,aAAcqnB,EAAKrnB,aAAalJ,KAHvBjS,MAITmY,cAAeqqB,EAAKrqB,cAAclG,KAJzBjS,UAQf4F,GAAI,CACF0pB,WAAY,WACV,IAAIpc,EAASlT,KACb,GAA6B,SAAzBkT,EAAOlF,OAAOoJ,OAAlB,CACAlE,EAAOwW,WAAWzmB,KAAOiQ,EAAOlF,OAA6B,uBAAI,QACjEkF,EAAOwW,WAAWzmB,KAAOiQ,EAAOlF,OAA6B,uBAAI,MACjE,IAAIuhB,EAAkB,CACpB1Z,cAAe,EACfJ,gBAAiB,EACjB0B,eAAgB,EAChBS,qBAAqB,EACrB8M,gBAAiB,EACjB3P,aAAc,EACdkC,gBAAgB,EAChBgE,kBAAkB,GAEpB5O,EAAMqC,OAAOwE,EAAOlF,OAAQuhB,GAC5BljB,EAAMqC,OAAOwE,EAAO6U,eAAgBwH,KAEtCpU,aAAc,WAEiB,SADhBnb,KACFgO,OAAOoJ,QADLpX,KAEN4iC,WAAWznB,gBAEpBhD,cAAe,SAAuB1S,GAEP,SADhBzF,KACFgO,OAAOoJ,QADLpX,KAEN4iC,WAAWzqB,cAAc1S,MA+ErB,CACf8M,KAAM,cACNvE,OAAQ,CACNk2B,WAAY,CACVhB,cAAc,EACdiB,eAAe,IAGnBhyB,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBkkC,WAAY,CACV/oB,aAAc8oB,GAAK9oB,aAAalJ,KAHvBjS,MAITmY,cAAe8rB,GAAK9rB,cAAclG,KAJzBjS,UAQf4F,GAAI,CACF0pB,WAAY,WACV,IAAIpc,EAASlT,KACb,GAA6B,SAAzBkT,EAAOlF,OAAOoJ,OAAlB,CACAlE,EAAOwW,WAAWzmB,KAAOiQ,EAAOlF,OAA6B,uBAAI,QACjEkF,EAAOwW,WAAWzmB,KAAOiQ,EAAOlF,OAA6B,uBAAI,MACjE,IAAIuhB,EAAkB,CACpB1Z,cAAe,EACfJ,gBAAiB,EACjB0B,eAAgB,EAChBS,qBAAqB,EACrB7C,aAAc,EACdkG,kBAAkB,GAEpB5O,EAAMqC,OAAOwE,EAAOlF,OAAQuhB,GAC5BljB,EAAMqC,OAAOwE,EAAO6U,eAAgBwH,KAEtCpU,aAAc,WAEiB,SADhBnb,KACFgO,OAAOoJ,QADLpX,KAENkkC,WAAW/oB,gBAEpBhD,cAAe,SAAuB1S,GAEP,SADhBzF,KACFgO,OAAOoJ,QADLpX,KAENkkC,WAAW/rB,cAAc1S,MA6EhB,CACpB8M,KAAM,mBACNvE,OAAQ,CACNw2B,gBAAiB,CACfE,OAAQ,GACRK,QAAS,EACTJ,MAAO,IACPE,SAAU,EACV3B,cAAc,IAGlB/wB,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBwkC,gBAAiB,CACfrpB,aAAcopB,GAAUppB,aAAalJ,KAH5BjS,MAITmY,cAAeosB,GAAUpsB,cAAclG,KAJ9BjS,UAQf4F,GAAI,CACF0pB,WAAY,WACV,IAAIpc,EAASlT,KACgB,cAAzBkT,EAAOlF,OAAOoJ,SAElBlE,EAAOwW,WAAWzmB,KAAOiQ,EAAOlF,OAA6B,uBAAI,aACjEkF,EAAOwW,WAAWzmB,KAAOiQ,EAAOlF,OAA6B,uBAAI,MAEjEkF,EAAOlF,OAAO4J,qBAAsB,EACpC1E,EAAO6U,eAAenQ,qBAAsB,IAE9CuD,aAAc,WAEiB,cADhBnb,KACFgO,OAAOoJ,QADLpX,KAENwkC,gBAAgBrpB,gBAEzBhD,cAAe,SAAuB1S,GAEP,cADhBzF,KACFgO,OAAOoJ,QADLpX,KAENwkC,gBAAgBrsB,cAAc1S,OAwC3C,YAP0B,IAAf1F,EAAOsS,MAChBtS,EAAOsS,IAAMtS,EAAO0D,MAAM4O,IAC1BtS,EAAOuS,cAAgBvS,EAAO0D,MAAM6O,eAGtCvS,EAAOsS,IAAIzB,IAEJ7Q", "file": "swiper.min.js", "sourcesContent": ["/**\n * Swiper 4.3.5\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * http://www.idangero.us/swiper/\n *\n * Copyright 2014-2018 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: July 31, 2018\n */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global.Swiper = factory());\n}(this, (function () { 'use strict';\n\n  /**\n   * SSR Window 1.0.1\n   * Better handling for window object in SSR environment\n   * https://github.com/nolimits4web/ssr-window\n   *\n   * Copyright 2018, <PERSON>\n   *\n   * Licensed under MIT\n   *\n   * Released on: July 18, 2018\n   */\n  var doc = (typeof document === 'undefined') ? {\n    body: {},\n    addEventListener: function addEventListener() {},\n    removeEventListener: function removeEventListener() {},\n    activeElement: {\n      blur: function blur() {},\n      nodeName: '',\n    },\n    querySelector: function querySelector() {\n      return null;\n    },\n    querySelectorAll: function querySelectorAll() {\n      return [];\n    },\n    getElementById: function getElementById() {\n      return null;\n    },\n    createEvent: function createEvent() {\n      return {\n        initEvent: function initEvent() {},\n      };\n    },\n    createElement: function createElement() {\n      return {\n        children: [],\n        childNodes: [],\n        style: {},\n        setAttribute: function setAttribute() {},\n        getElementsByTagName: function getElementsByTagName() {\n          return [];\n        },\n      };\n    },\n    location: { hash: '' },\n  } : document; // eslint-disable-line\n\n  var win = (typeof window === 'undefined') ? {\n    document: doc,\n    navigator: {\n      userAgent: '',\n    },\n    location: {},\n    history: {},\n    CustomEvent: function CustomEvent() {\n      return this;\n    },\n    addEventListener: function addEventListener() {},\n    removeEventListener: function removeEventListener() {},\n    getComputedStyle: function getComputedStyle() {\n      return {\n        getPropertyValue: function getPropertyValue() {\n          return '';\n        },\n      };\n    },\n    Image: function Image() {},\n    Date: function Date() {},\n    screen: {},\n    setTimeout: function setTimeout() {},\n    clearTimeout: function clearTimeout() {},\n  } : window; // eslint-disable-line\n\n  /**\n   * Dom7 2.0.7\n   * Minimalistic JavaScript library for DOM manipulation, with a jQuery-compatible API\n   * http://framework7.io/docs/dom.html\n   *\n   * Copyright 2018, Vladimir Kharlampidi\n   * The iDangero.us\n   * http://www.idangero.us/\n   *\n   * Licensed under MIT\n   *\n   * Released on: June 14, 2018\n   */\n\n  var Dom7 = function Dom7(arr) {\n    var self = this;\n    // Create array-like object\n    for (var i = 0; i < arr.length; i += 1) {\n      self[i] = arr[i];\n    }\n    self.length = arr.length;\n    // Return collection with methods\n    return this;\n  };\n\n  function $(selector, context) {\n    var arr = [];\n    var i = 0;\n    if (selector && !context) {\n      if (selector instanceof Dom7) {\n        return selector;\n      }\n    }\n    if (selector) {\n        // String\n      if (typeof selector === 'string') {\n        var els;\n        var tempParent;\n        var html = selector.trim();\n        if (html.indexOf('<') >= 0 && html.indexOf('>') >= 0) {\n          var toCreate = 'div';\n          if (html.indexOf('<li') === 0) { toCreate = 'ul'; }\n          if (html.indexOf('<tr') === 0) { toCreate = 'tbody'; }\n          if (html.indexOf('<td') === 0 || html.indexOf('<th') === 0) { toCreate = 'tr'; }\n          if (html.indexOf('<tbody') === 0) { toCreate = 'table'; }\n          if (html.indexOf('<option') === 0) { toCreate = 'select'; }\n          tempParent = doc.createElement(toCreate);\n          tempParent.innerHTML = html;\n          for (i = 0; i < tempParent.childNodes.length; i += 1) {\n            arr.push(tempParent.childNodes[i]);\n          }\n        } else {\n          if (!context && selector[0] === '#' && !selector.match(/[ .<>:~]/)) {\n            // Pure ID selector\n            els = [doc.getElementById(selector.trim().split('#')[1])];\n          } else {\n            // Other selectors\n            els = (context || doc).querySelectorAll(selector.trim());\n          }\n          for (i = 0; i < els.length; i += 1) {\n            if (els[i]) { arr.push(els[i]); }\n          }\n        }\n      } else if (selector.nodeType || selector === win || selector === doc) {\n        // Node/element\n        arr.push(selector);\n      } else if (selector.length > 0 && selector[0].nodeType) {\n        // Array of elements or instance of Dom\n        for (i = 0; i < selector.length; i += 1) {\n          arr.push(selector[i]);\n        }\n      }\n    }\n    return new Dom7(arr);\n  }\n\n  $.fn = Dom7.prototype;\n  $.Class = Dom7;\n  $.Dom7 = Dom7;\n\n  function unique(arr) {\n    var uniqueArray = [];\n    for (var i = 0; i < arr.length; i += 1) {\n      if (uniqueArray.indexOf(arr[i]) === -1) { uniqueArray.push(arr[i]); }\n    }\n    return uniqueArray;\n  }\n\n  // Classes and attributes\n  function addClass(className) {\n    var this$1 = this;\n\n    if (typeof className === 'undefined') {\n      return this;\n    }\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this$1[j] !== 'undefined' && typeof this$1[j].classList !== 'undefined') { this$1[j].classList.add(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function removeClass(className) {\n    var this$1 = this;\n\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this$1[j] !== 'undefined' && typeof this$1[j].classList !== 'undefined') { this$1[j].classList.remove(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function hasClass(className) {\n    if (!this[0]) { return false; }\n    return this[0].classList.contains(className);\n  }\n  function toggleClass(className) {\n    var this$1 = this;\n\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this$1[j] !== 'undefined' && typeof this$1[j].classList !== 'undefined') { this$1[j].classList.toggle(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function attr(attrs, value) {\n    var arguments$1 = arguments;\n    var this$1 = this;\n\n    if (arguments.length === 1 && typeof attrs === 'string') {\n      // Get attr\n      if (this[0]) { return this[0].getAttribute(attrs); }\n      return undefined;\n    }\n\n    // Set attrs\n    for (var i = 0; i < this.length; i += 1) {\n      if (arguments$1.length === 2) {\n        // String\n        this$1[i].setAttribute(attrs, value);\n      } else {\n        // Object\n        // eslint-disable-next-line\n        for (var attrName in attrs) {\n          this$1[i][attrName] = attrs[attrName];\n          this$1[i].setAttribute(attrName, attrs[attrName]);\n        }\n      }\n    }\n    return this;\n  }\n  // eslint-disable-next-line\n  function removeAttr(attr) {\n    var this$1 = this;\n\n    for (var i = 0; i < this.length; i += 1) {\n      this$1[i].removeAttribute(attr);\n    }\n    return this;\n  }\n  function data(key, value) {\n    var this$1 = this;\n\n    var el;\n    if (typeof value === 'undefined') {\n      el = this[0];\n      // Get value\n      if (el) {\n        if (el.dom7ElementDataStorage && (key in el.dom7ElementDataStorage)) {\n          return el.dom7ElementDataStorage[key];\n        }\n\n        var dataKey = el.getAttribute((\"data-\" + key));\n        if (dataKey) {\n          return dataKey;\n        }\n        return undefined;\n      }\n      return undefined;\n    }\n\n    // Set value\n    for (var i = 0; i < this.length; i += 1) {\n      el = this$1[i];\n      if (!el.dom7ElementDataStorage) { el.dom7ElementDataStorage = {}; }\n      el.dom7ElementDataStorage[key] = value;\n    }\n    return this;\n  }\n  // Transforms\n  // eslint-disable-next-line\n  function transform(transform) {\n    var this$1 = this;\n\n    for (var i = 0; i < this.length; i += 1) {\n      var elStyle = this$1[i].style;\n      elStyle.webkitTransform = transform;\n      elStyle.transform = transform;\n    }\n    return this;\n  }\n  function transition(duration) {\n    var this$1 = this;\n\n    if (typeof duration !== 'string') {\n      duration = duration + \"ms\"; // eslint-disable-line\n    }\n    for (var i = 0; i < this.length; i += 1) {\n      var elStyle = this$1[i].style;\n      elStyle.webkitTransitionDuration = duration;\n      elStyle.transitionDuration = duration;\n    }\n    return this;\n  }\n  // Events\n  function on() {\n    var this$1 = this;\n    var assign;\n\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n    var eventType = args[0];\n    var targetSelector = args[1];\n    var listener = args[2];\n    var capture = args[3];\n    if (typeof args[1] === 'function') {\n      (assign = args, eventType = assign[0], listener = assign[1], capture = assign[2]);\n      targetSelector = undefined;\n    }\n    if (!capture) { capture = false; }\n\n    function handleLiveEvent(e) {\n      var target = e.target;\n      if (!target) { return; }\n      var eventData = e.target.dom7EventData || [];\n      if (eventData.indexOf(e) < 0) {\n        eventData.unshift(e);\n      }\n      if ($(target).is(targetSelector)) { listener.apply(target, eventData); }\n      else {\n        var parents = $(target).parents(); // eslint-disable-line\n        for (var k = 0; k < parents.length; k += 1) {\n          if ($(parents[k]).is(targetSelector)) { listener.apply(parents[k], eventData); }\n        }\n      }\n    }\n    function handleEvent(e) {\n      var eventData = e && e.target ? e.target.dom7EventData || [] : [];\n      if (eventData.indexOf(e) < 0) {\n        eventData.unshift(e);\n      }\n      listener.apply(this, eventData);\n    }\n    var events = eventType.split(' ');\n    var j;\n    for (var i = 0; i < this.length; i += 1) {\n      var el = this$1[i];\n      if (!targetSelector) {\n        for (j = 0; j < events.length; j += 1) {\n          var event = events[j];\n          if (!el.dom7Listeners) { el.dom7Listeners = {}; }\n          if (!el.dom7Listeners[event]) { el.dom7Listeners[event] = []; }\n          el.dom7Listeners[event].push({\n            listener: listener,\n            proxyListener: handleEvent,\n          });\n          el.addEventListener(event, handleEvent, capture);\n        }\n      } else {\n        // Live events\n        for (j = 0; j < events.length; j += 1) {\n          var event$1 = events[j];\n          if (!el.dom7LiveListeners) { el.dom7LiveListeners = {}; }\n          if (!el.dom7LiveListeners[event$1]) { el.dom7LiveListeners[event$1] = []; }\n          el.dom7LiveListeners[event$1].push({\n            listener: listener,\n            proxyListener: handleLiveEvent,\n          });\n          el.addEventListener(event$1, handleLiveEvent, capture);\n        }\n      }\n    }\n    return this;\n  }\n  function off() {\n    var this$1 = this;\n    var assign;\n\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n    var eventType = args[0];\n    var targetSelector = args[1];\n    var listener = args[2];\n    var capture = args[3];\n    if (typeof args[1] === 'function') {\n      (assign = args, eventType = assign[0], listener = assign[1], capture = assign[2]);\n      targetSelector = undefined;\n    }\n    if (!capture) { capture = false; }\n\n    var events = eventType.split(' ');\n    for (var i = 0; i < events.length; i += 1) {\n      var event = events[i];\n      for (var j = 0; j < this.length; j += 1) {\n        var el = this$1[j];\n        var handlers = (void 0);\n        if (!targetSelector && el.dom7Listeners) {\n          handlers = el.dom7Listeners[event];\n        } else if (targetSelector && el.dom7LiveListeners) {\n          handlers = el.dom7LiveListeners[event];\n        }\n        if (handlers && handlers.length) {\n          for (var k = handlers.length - 1; k >= 0; k -= 1) {\n            var handler = handlers[k];\n            if (listener && handler.listener === listener) {\n              el.removeEventListener(event, handler.proxyListener, capture);\n              handlers.splice(k, 1);\n            } else if (!listener) {\n              el.removeEventListener(event, handler.proxyListener, capture);\n              handlers.splice(k, 1);\n            }\n          }\n        }\n      }\n    }\n    return this;\n  }\n  function trigger() {\n    var this$1 = this;\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var events = args[0].split(' ');\n    var eventData = args[1];\n    for (var i = 0; i < events.length; i += 1) {\n      var event = events[i];\n      for (var j = 0; j < this.length; j += 1) {\n        var el = this$1[j];\n        var evt = (void 0);\n        try {\n          evt = new win.CustomEvent(event, {\n            detail: eventData,\n            bubbles: true,\n            cancelable: true,\n          });\n        } catch (e) {\n          evt = doc.createEvent('Event');\n          evt.initEvent(event, true, true);\n          evt.detail = eventData;\n        }\n        // eslint-disable-next-line\n        el.dom7EventData = args.filter(function (data, dataIndex) { return dataIndex > 0; });\n        el.dispatchEvent(evt);\n        el.dom7EventData = [];\n        delete el.dom7EventData;\n      }\n    }\n    return this;\n  }\n  function transitionEnd(callback) {\n    var events = ['webkitTransitionEnd', 'transitionend'];\n    var dom = this;\n    var i;\n    function fireCallBack(e) {\n      /* jshint validthis:true */\n      if (e.target !== this) { return; }\n      callback.call(this, e);\n      for (i = 0; i < events.length; i += 1) {\n        dom.off(events[i], fireCallBack);\n      }\n    }\n    if (callback) {\n      for (i = 0; i < events.length; i += 1) {\n        dom.on(events[i], fireCallBack);\n      }\n    }\n    return this;\n  }\n  function outerWidth(includeMargins) {\n    if (this.length > 0) {\n      if (includeMargins) {\n        // eslint-disable-next-line\n        var styles = this.styles();\n        return this[0].offsetWidth + parseFloat(styles.getPropertyValue('margin-right')) + parseFloat(styles.getPropertyValue('margin-left'));\n      }\n      return this[0].offsetWidth;\n    }\n    return null;\n  }\n  function outerHeight(includeMargins) {\n    if (this.length > 0) {\n      if (includeMargins) {\n        // eslint-disable-next-line\n        var styles = this.styles();\n        return this[0].offsetHeight + parseFloat(styles.getPropertyValue('margin-top')) + parseFloat(styles.getPropertyValue('margin-bottom'));\n      }\n      return this[0].offsetHeight;\n    }\n    return null;\n  }\n  function offset() {\n    if (this.length > 0) {\n      var el = this[0];\n      var box = el.getBoundingClientRect();\n      var body = doc.body;\n      var clientTop = el.clientTop || body.clientTop || 0;\n      var clientLeft = el.clientLeft || body.clientLeft || 0;\n      var scrollTop = el === win ? win.scrollY : el.scrollTop;\n      var scrollLeft = el === win ? win.scrollX : el.scrollLeft;\n      return {\n        top: (box.top + scrollTop) - clientTop,\n        left: (box.left + scrollLeft) - clientLeft,\n      };\n    }\n\n    return null;\n  }\n  function styles() {\n    if (this[0]) { return win.getComputedStyle(this[0], null); }\n    return {};\n  }\n  function css(props, value) {\n    var this$1 = this;\n\n    var i;\n    if (arguments.length === 1) {\n      if (typeof props === 'string') {\n        if (this[0]) { return win.getComputedStyle(this[0], null).getPropertyValue(props); }\n      } else {\n        for (i = 0; i < this.length; i += 1) {\n          // eslint-disable-next-line\n          for (var prop in props) {\n            this$1[i].style[prop] = props[prop];\n          }\n        }\n        return this;\n      }\n    }\n    if (arguments.length === 2 && typeof props === 'string') {\n      for (i = 0; i < this.length; i += 1) {\n        this$1[i].style[props] = value;\n      }\n      return this;\n    }\n    return this;\n  }\n  // Iterate over the collection passing elements to `callback`\n  function each(callback) {\n    var this$1 = this;\n\n    // Don't bother continuing without a callback\n    if (!callback) { return this; }\n    // Iterate over the current collection\n    for (var i = 0; i < this.length; i += 1) {\n      // If the callback returns false\n      if (callback.call(this$1[i], i, this$1[i]) === false) {\n        // End the loop early\n        return this$1;\n      }\n    }\n    // Return `this` to allow chained DOM operations\n    return this;\n  }\n  // eslint-disable-next-line\n  function html(html) {\n    var this$1 = this;\n\n    if (typeof html === 'undefined') {\n      return this[0] ? this[0].innerHTML : undefined;\n    }\n\n    for (var i = 0; i < this.length; i += 1) {\n      this$1[i].innerHTML = html;\n    }\n    return this;\n  }\n  // eslint-disable-next-line\n  function text(text) {\n    var this$1 = this;\n\n    if (typeof text === 'undefined') {\n      if (this[0]) {\n        return this[0].textContent.trim();\n      }\n      return null;\n    }\n\n    for (var i = 0; i < this.length; i += 1) {\n      this$1[i].textContent = text;\n    }\n    return this;\n  }\n  function is(selector) {\n    var el = this[0];\n    var compareWith;\n    var i;\n    if (!el || typeof selector === 'undefined') { return false; }\n    if (typeof selector === 'string') {\n      if (el.matches) { return el.matches(selector); }\n      else if (el.webkitMatchesSelector) { return el.webkitMatchesSelector(selector); }\n      else if (el.msMatchesSelector) { return el.msMatchesSelector(selector); }\n\n      compareWith = $(selector);\n      for (i = 0; i < compareWith.length; i += 1) {\n        if (compareWith[i] === el) { return true; }\n      }\n      return false;\n    } else if (selector === doc) { return el === doc; }\n    else if (selector === win) { return el === win; }\n\n    if (selector.nodeType || selector instanceof Dom7) {\n      compareWith = selector.nodeType ? [selector] : selector;\n      for (i = 0; i < compareWith.length; i += 1) {\n        if (compareWith[i] === el) { return true; }\n      }\n      return false;\n    }\n    return false;\n  }\n  function index() {\n    var child = this[0];\n    var i;\n    if (child) {\n      i = 0;\n      // eslint-disable-next-line\n      while ((child = child.previousSibling) !== null) {\n        if (child.nodeType === 1) { i += 1; }\n      }\n      return i;\n    }\n    return undefined;\n  }\n  // eslint-disable-next-line\n  function eq(index) {\n    if (typeof index === 'undefined') { return this; }\n    var length = this.length;\n    var returnIndex;\n    if (index > length - 1) {\n      return new Dom7([]);\n    }\n    if (index < 0) {\n      returnIndex = length + index;\n      if (returnIndex < 0) { return new Dom7([]); }\n      return new Dom7([this[returnIndex]]);\n    }\n    return new Dom7([this[index]]);\n  }\n  function append() {\n    var this$1 = this;\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var newChild;\n\n    for (var k = 0; k < args.length; k += 1) {\n      newChild = args[k];\n      for (var i = 0; i < this.length; i += 1) {\n        if (typeof newChild === 'string') {\n          var tempDiv = doc.createElement('div');\n          tempDiv.innerHTML = newChild;\n          while (tempDiv.firstChild) {\n            this$1[i].appendChild(tempDiv.firstChild);\n          }\n        } else if (newChild instanceof Dom7) {\n          for (var j = 0; j < newChild.length; j += 1) {\n            this$1[i].appendChild(newChild[j]);\n          }\n        } else {\n          this$1[i].appendChild(newChild);\n        }\n      }\n    }\n\n    return this;\n  }\n  function prepend(newChild) {\n    var this$1 = this;\n\n    var i;\n    var j;\n    for (i = 0; i < this.length; i += 1) {\n      if (typeof newChild === 'string') {\n        var tempDiv = doc.createElement('div');\n        tempDiv.innerHTML = newChild;\n        for (j = tempDiv.childNodes.length - 1; j >= 0; j -= 1) {\n          this$1[i].insertBefore(tempDiv.childNodes[j], this$1[i].childNodes[0]);\n        }\n      } else if (newChild instanceof Dom7) {\n        for (j = 0; j < newChild.length; j += 1) {\n          this$1[i].insertBefore(newChild[j], this$1[i].childNodes[0]);\n        }\n      } else {\n        this$1[i].insertBefore(newChild, this$1[i].childNodes[0]);\n      }\n    }\n    return this;\n  }\n  function next(selector) {\n    if (this.length > 0) {\n      if (selector) {\n        if (this[0].nextElementSibling && $(this[0].nextElementSibling).is(selector)) {\n          return new Dom7([this[0].nextElementSibling]);\n        }\n        return new Dom7([]);\n      }\n\n      if (this[0].nextElementSibling) { return new Dom7([this[0].nextElementSibling]); }\n      return new Dom7([]);\n    }\n    return new Dom7([]);\n  }\n  function nextAll(selector) {\n    var nextEls = [];\n    var el = this[0];\n    if (!el) { return new Dom7([]); }\n    while (el.nextElementSibling) {\n      var next = el.nextElementSibling; // eslint-disable-line\n      if (selector) {\n        if ($(next).is(selector)) { nextEls.push(next); }\n      } else { nextEls.push(next); }\n      el = next;\n    }\n    return new Dom7(nextEls);\n  }\n  function prev(selector) {\n    if (this.length > 0) {\n      var el = this[0];\n      if (selector) {\n        if (el.previousElementSibling && $(el.previousElementSibling).is(selector)) {\n          return new Dom7([el.previousElementSibling]);\n        }\n        return new Dom7([]);\n      }\n\n      if (el.previousElementSibling) { return new Dom7([el.previousElementSibling]); }\n      return new Dom7([]);\n    }\n    return new Dom7([]);\n  }\n  function prevAll(selector) {\n    var prevEls = [];\n    var el = this[0];\n    if (!el) { return new Dom7([]); }\n    while (el.previousElementSibling) {\n      var prev = el.previousElementSibling; // eslint-disable-line\n      if (selector) {\n        if ($(prev).is(selector)) { prevEls.push(prev); }\n      } else { prevEls.push(prev); }\n      el = prev;\n    }\n    return new Dom7(prevEls);\n  }\n  function parent(selector) {\n    var this$1 = this;\n\n    var parents = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      if (this$1[i].parentNode !== null) {\n        if (selector) {\n          if ($(this$1[i].parentNode).is(selector)) { parents.push(this$1[i].parentNode); }\n        } else {\n          parents.push(this$1[i].parentNode);\n        }\n      }\n    }\n    return $(unique(parents));\n  }\n  function parents(selector) {\n    var this$1 = this;\n\n    var parents = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      var parent = this$1[i].parentNode; // eslint-disable-line\n      while (parent) {\n        if (selector) {\n          if ($(parent).is(selector)) { parents.push(parent); }\n        } else {\n          parents.push(parent);\n        }\n        parent = parent.parentNode;\n      }\n    }\n    return $(unique(parents));\n  }\n  function closest(selector) {\n    var closest = this; // eslint-disable-line\n    if (typeof selector === 'undefined') {\n      return new Dom7([]);\n    }\n    if (!closest.is(selector)) {\n      closest = closest.parents(selector).eq(0);\n    }\n    return closest;\n  }\n  function find(selector) {\n    var this$1 = this;\n\n    var foundElements = [];\n    for (var i = 0; i < this.length; i += 1) {\n      var found = this$1[i].querySelectorAll(selector);\n      for (var j = 0; j < found.length; j += 1) {\n        foundElements.push(found[j]);\n      }\n    }\n    return new Dom7(foundElements);\n  }\n  function children(selector) {\n    var this$1 = this;\n\n    var children = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      var childNodes = this$1[i].childNodes;\n\n      for (var j = 0; j < childNodes.length; j += 1) {\n        if (!selector) {\n          if (childNodes[j].nodeType === 1) { children.push(childNodes[j]); }\n        } else if (childNodes[j].nodeType === 1 && $(childNodes[j]).is(selector)) {\n          children.push(childNodes[j]);\n        }\n      }\n    }\n    return new Dom7(unique(children));\n  }\n  function remove() {\n    var this$1 = this;\n\n    for (var i = 0; i < this.length; i += 1) {\n      if (this$1[i].parentNode) { this$1[i].parentNode.removeChild(this$1[i]); }\n    }\n    return this;\n  }\n  function add() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var dom = this;\n    var i;\n    var j;\n    for (i = 0; i < args.length; i += 1) {\n      var toAdd = $(args[i]);\n      for (j = 0; j < toAdd.length; j += 1) {\n        dom[dom.length] = toAdd[j];\n        dom.length += 1;\n      }\n    }\n    return dom;\n  }\n\n  var Methods = {\n    addClass: addClass,\n    removeClass: removeClass,\n    hasClass: hasClass,\n    toggleClass: toggleClass,\n    attr: attr,\n    removeAttr: removeAttr,\n    data: data,\n    transform: transform,\n    transition: transition,\n    on: on,\n    off: off,\n    trigger: trigger,\n    transitionEnd: transitionEnd,\n    outerWidth: outerWidth,\n    outerHeight: outerHeight,\n    offset: offset,\n    css: css,\n    each: each,\n    html: html,\n    text: text,\n    is: is,\n    index: index,\n    eq: eq,\n    append: append,\n    prepend: prepend,\n    next: next,\n    nextAll: nextAll,\n    prev: prev,\n    prevAll: prevAll,\n    parent: parent,\n    parents: parents,\n    closest: closest,\n    find: find,\n    children: children,\n    remove: remove,\n    add: add,\n    styles: styles,\n  };\n\n  Object.keys(Methods).forEach(function (methodName) {\n    $.fn[methodName] = Methods[methodName];\n  });\n\n  var Utils = {\n    deleteProps: function deleteProps(obj) {\n      var object = obj;\n      Object.keys(object).forEach(function (key) {\n        try {\n          object[key] = null;\n        } catch (e) {\n          // no getter for object\n        }\n        try {\n          delete object[key];\n        } catch (e) {\n          // something got wrong\n        }\n      });\n    },\n    nextTick: function nextTick(callback, delay) {\n      if ( delay === void 0 ) delay = 0;\n\n      return setTimeout(callback, delay);\n    },\n    now: function now() {\n      return Date.now();\n    },\n    getTranslate: function getTranslate(el, axis) {\n      if ( axis === void 0 ) axis = 'x';\n\n      var matrix;\n      var curTransform;\n      var transformMatrix;\n\n      var curStyle = win.getComputedStyle(el, null);\n\n      if (win.WebKitCSSMatrix) {\n        curTransform = curStyle.transform || curStyle.webkitTransform;\n        if (curTransform.split(',').length > 6) {\n          curTransform = curTransform.split(', ').map(function (a) { return a.replace(',', '.'); }).join(', ');\n        }\n        // Some old versions of Webkit choke when 'none' is passed; pass\n        // empty string instead in this case\n        transformMatrix = new win.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n      } else {\n        transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n        matrix = transformMatrix.toString().split(',');\n      }\n\n      if (axis === 'x') {\n        // Latest Chrome and webkits Fix\n        if (win.WebKitCSSMatrix) { curTransform = transformMatrix.m41; }\n        // Crazy IE10 Matrix\n        else if (matrix.length === 16) { curTransform = parseFloat(matrix[12]); }\n        // Normal Browsers\n        else { curTransform = parseFloat(matrix[4]); }\n      }\n      if (axis === 'y') {\n        // Latest Chrome and webkits Fix\n        if (win.WebKitCSSMatrix) { curTransform = transformMatrix.m42; }\n        // Crazy IE10 Matrix\n        else if (matrix.length === 16) { curTransform = parseFloat(matrix[13]); }\n        // Normal Browsers\n        else { curTransform = parseFloat(matrix[5]); }\n      }\n      return curTransform || 0;\n    },\n    parseUrlQuery: function parseUrlQuery(url) {\n      var query = {};\n      var urlToParse = url || win.location.href;\n      var i;\n      var params;\n      var param;\n      var length;\n      if (typeof urlToParse === 'string' && urlToParse.length) {\n        urlToParse = urlToParse.indexOf('?') > -1 ? urlToParse.replace(/\\S*\\?/, '') : '';\n        params = urlToParse.split('&').filter(function (paramsPart) { return paramsPart !== ''; });\n        length = params.length;\n\n        for (i = 0; i < length; i += 1) {\n          param = params[i].replace(/#\\S+/g, '').split('=');\n          query[decodeURIComponent(param[0])] = typeof param[1] === 'undefined' ? undefined : decodeURIComponent(param[1]) || '';\n        }\n      }\n      return query;\n    },\n    isObject: function isObject(o) {\n      return typeof o === 'object' && o !== null && o.constructor && o.constructor === Object;\n    },\n    extend: function extend() {\n      var args = [], len$1 = arguments.length;\n      while ( len$1-- ) args[ len$1 ] = arguments[ len$1 ];\n\n      var to = Object(args[0]);\n      for (var i = 1; i < args.length; i += 1) {\n        var nextSource = args[i];\n        if (nextSource !== undefined && nextSource !== null) {\n          var keysArray = Object.keys(Object(nextSource));\n          for (var nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n            var nextKey = keysArray[nextIndex];\n            var desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n            if (desc !== undefined && desc.enumerable) {\n              if (Utils.isObject(to[nextKey]) && Utils.isObject(nextSource[nextKey])) {\n                Utils.extend(to[nextKey], nextSource[nextKey]);\n              } else if (!Utils.isObject(to[nextKey]) && Utils.isObject(nextSource[nextKey])) {\n                to[nextKey] = {};\n                Utils.extend(to[nextKey], nextSource[nextKey]);\n              } else {\n                to[nextKey] = nextSource[nextKey];\n              }\n            }\n          }\n        }\n      }\n      return to;\n    },\n  };\n\n  var Support = (function Support() {\n    var testDiv = doc.createElement('div');\n    return {\n      touch: (win.Modernizr && win.Modernizr.touch === true) || (function checkTouch() {\n        return !!(('ontouchstart' in win) || (win.DocumentTouch && doc instanceof win.DocumentTouch));\n      }()),\n\n      pointerEvents: !!(win.navigator.pointerEnabled || win.PointerEvent),\n      prefixedPointerEvents: !!win.navigator.msPointerEnabled,\n\n      transition: (function checkTransition() {\n        var style = testDiv.style;\n        return ('transition' in style || 'webkitTransition' in style || 'MozTransition' in style);\n      }()),\n      transforms3d: (win.Modernizr && win.Modernizr.csstransforms3d === true) || (function checkTransforms3d() {\n        var style = testDiv.style;\n        return ('webkitPerspective' in style || 'MozPerspective' in style || 'OPerspective' in style || 'MsPerspective' in style || 'perspective' in style);\n      }()),\n\n      flexbox: (function checkFlexbox() {\n        var style = testDiv.style;\n        var styles = ('alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient').split(' ');\n        for (var i = 0; i < styles.length; i += 1) {\n          if (styles[i] in style) { return true; }\n        }\n        return false;\n      }()),\n\n      observer: (function checkObserver() {\n        return ('MutationObserver' in win || 'WebkitMutationObserver' in win);\n      }()),\n\n      passiveListener: (function checkPassiveListener() {\n        var supportsPassive = false;\n        try {\n          var opts = Object.defineProperty({}, 'passive', {\n            // eslint-disable-next-line\n            get: function get() {\n              supportsPassive = true;\n            },\n          });\n          win.addEventListener('testPassiveListener', null, opts);\n        } catch (e) {\n          // No support\n        }\n        return supportsPassive;\n      }()),\n\n      gestures: (function checkGestures() {\n        return 'ongesturestart' in win;\n      }()),\n    };\n  }());\n\n  var SwiperClass = function SwiperClass(params) {\n    if ( params === void 0 ) params = {};\n\n    var self = this;\n    self.params = params;\n\n    // Events\n    self.eventsListeners = {};\n\n    if (self.params && self.params.on) {\n      Object.keys(self.params.on).forEach(function (eventName) {\n        self.on(eventName, self.params.on[eventName]);\n      });\n    }\n  };\n\n  var staticAccessors = { components: { configurable: true } };\n\n  SwiperClass.prototype.on = function on (events, handler, priority) {\n    var self = this;\n    if (typeof handler !== 'function') { return self; }\n    var method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(function (event) {\n      if (!self.eventsListeners[event]) { self.eventsListeners[event] = []; }\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.once = function once (events, handler, priority) {\n    var self = this;\n    if (typeof handler !== 'function') { return self; }\n    function onceHandler() {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n      handler.apply(self, args);\n      self.off(events, onceHandler);\n    }\n    return self.on(events, onceHandler, priority);\n  };\n\n  SwiperClass.prototype.off = function off (events, handler) {\n    var self = this;\n    if (!self.eventsListeners) { return self; }\n    events.split(' ').forEach(function (event) {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else {\n        self.eventsListeners[event].forEach(function (eventHandler, index) {\n          if (eventHandler === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.emit = function emit () {\n      var args = [], len = arguments.length;\n      while ( len-- ) args[ len ] = arguments[ len ];\n\n    var self = this;\n    if (!self.eventsListeners) { return self; }\n    var events;\n    var data;\n    var context;\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    var eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(function (event) {\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        var handlers = [];\n        self.eventsListeners[event].forEach(function (eventHandler) {\n          handlers.push(eventHandler);\n        });\n        handlers.forEach(function (eventHandler) {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.useModulesParams = function useModulesParams (instanceParams) {\n    var instance = this;\n    if (!instance.modules) { return; }\n    Object.keys(instance.modules).forEach(function (moduleName) {\n      var module = instance.modules[moduleName];\n      // Extend params\n      if (module.params) {\n        Utils.extend(instanceParams, module.params);\n      }\n    });\n  };\n\n  SwiperClass.prototype.useModules = function useModules (modulesParams) {\n      if ( modulesParams === void 0 ) modulesParams = {};\n\n    var instance = this;\n    if (!instance.modules) { return; }\n    Object.keys(instance.modules).forEach(function (moduleName) {\n      var module = instance.modules[moduleName];\n      var moduleParams = modulesParams[moduleName] || {};\n      // Extend instance methods and props\n      if (module.instance) {\n        Object.keys(module.instance).forEach(function (modulePropName) {\n          var moduleProp = module.instance[modulePropName];\n          if (typeof moduleProp === 'function') {\n            instance[modulePropName] = moduleProp.bind(instance);\n          } else {\n            instance[modulePropName] = moduleProp;\n          }\n        });\n      }\n      // Add event listeners\n      if (module.on && instance.on) {\n        Object.keys(module.on).forEach(function (moduleEventName) {\n          instance.on(moduleEventName, module.on[moduleEventName]);\n        });\n      }\n\n      // Module create callback\n      if (module.create) {\n        module.create.bind(instance)(moduleParams);\n      }\n    });\n  };\n\n  staticAccessors.components.set = function (components) {\n    var Class = this;\n    if (!Class.use) { return; }\n    Class.use(components);\n  };\n\n  SwiperClass.installModule = function installModule (module) {\n      var params = [], len = arguments.length - 1;\n      while ( len-- > 0 ) params[ len ] = arguments[ len + 1 ];\n\n    var Class = this;\n    if (!Class.prototype.modules) { Class.prototype.modules = {}; }\n    var name = module.name || (((Object.keys(Class.prototype.modules).length) + \"_\" + (Utils.now())));\n    Class.prototype.modules[name] = module;\n    // Prototype\n    if (module.proto) {\n      Object.keys(module.proto).forEach(function (key) {\n        Class.prototype[key] = module.proto[key];\n      });\n    }\n    // Class\n    if (module.static) {\n      Object.keys(module.static).forEach(function (key) {\n        Class[key] = module.static[key];\n      });\n    }\n    // Callback\n    if (module.install) {\n      module.install.apply(Class, params);\n    }\n    return Class;\n  };\n\n  SwiperClass.use = function use (module) {\n      var params = [], len = arguments.length - 1;\n      while ( len-- > 0 ) params[ len ] = arguments[ len + 1 ];\n\n    var Class = this;\n    if (Array.isArray(module)) {\n      module.forEach(function (m) { return Class.installModule(m); });\n      return Class;\n    }\n    return Class.installModule.apply(Class, [ module ].concat( params ));\n  };\n\n  Object.defineProperties( SwiperClass, staticAccessors );\n\n  function updateSize () {\n    var swiper = this;\n    var width;\n    var height;\n    var $el = swiper.$el;\n    if (typeof swiper.params.width !== 'undefined') {\n      width = swiper.params.width;\n    } else {\n      width = $el[0].clientWidth;\n    }\n    if (typeof swiper.params.height !== 'undefined') {\n      height = swiper.params.height;\n    } else {\n      height = $el[0].clientHeight;\n    }\n    if ((width === 0 && swiper.isHorizontal()) || (height === 0 && swiper.isVertical())) {\n      return;\n    }\n\n    // Subtract paddings\n    width = width - parseInt($el.css('padding-left'), 10) - parseInt($el.css('padding-right'), 10);\n    height = height - parseInt($el.css('padding-top'), 10) - parseInt($el.css('padding-bottom'), 10);\n\n    Utils.extend(swiper, {\n      width: width,\n      height: height,\n      size: swiper.isHorizontal() ? width : height,\n    });\n  }\n\n  function updateSlides () {\n    var swiper = this;\n    var params = swiper.params;\n\n    var $wrapperEl = swiper.$wrapperEl;\n    var swiperSize = swiper.size;\n    var rtl = swiper.rtlTranslate;\n    var wrongRTL = swiper.wrongRTL;\n    var isVirtual = swiper.virtual && params.virtual.enabled;\n    var previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n    var slides = $wrapperEl.children((\".\" + (swiper.params.slideClass)));\n    var slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n    var snapGrid = [];\n    var slidesGrid = [];\n    var slidesSizesGrid = [];\n\n    var offsetBefore = params.slidesOffsetBefore;\n    if (typeof offsetBefore === 'function') {\n      offsetBefore = params.slidesOffsetBefore.call(swiper);\n    }\n\n    var offsetAfter = params.slidesOffsetAfter;\n    if (typeof offsetAfter === 'function') {\n      offsetAfter = params.slidesOffsetAfter.call(swiper);\n    }\n\n    var previousSnapGridLength = swiper.snapGrid.length;\n    var previousSlidesGridLength = swiper.snapGrid.length;\n\n    var spaceBetween = params.spaceBetween;\n    var slidePosition = -offsetBefore;\n    var prevSlideSize = 0;\n    var index = 0;\n    if (typeof swiperSize === 'undefined') {\n      return;\n    }\n    if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n      spaceBetween = (parseFloat(spaceBetween.replace('%', '')) / 100) * swiperSize;\n    }\n\n    swiper.virtualSize = -spaceBetween;\n\n    // reset margins\n    if (rtl) { slides.css({ marginLeft: '', marginTop: '' }); }\n    else { slides.css({ marginRight: '', marginBottom: '' }); }\n\n    var slidesNumberEvenToRows;\n    if (params.slidesPerColumn > 1) {\n      if (Math.floor(slidesLength / params.slidesPerColumn) === slidesLength / swiper.params.slidesPerColumn) {\n        slidesNumberEvenToRows = slidesLength;\n      } else {\n        slidesNumberEvenToRows = Math.ceil(slidesLength / params.slidesPerColumn) * params.slidesPerColumn;\n      }\n      if (params.slidesPerView !== 'auto' && params.slidesPerColumnFill === 'row') {\n        slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, params.slidesPerView * params.slidesPerColumn);\n      }\n    }\n\n    // Calc slides\n    var slideSize;\n    var slidesPerColumn = params.slidesPerColumn;\n    var slidesPerRow = slidesNumberEvenToRows / slidesPerColumn;\n    var numFullColumns = slidesPerRow - ((params.slidesPerColumn * slidesPerRow) - slidesLength);\n    for (var i = 0; i < slidesLength; i += 1) {\n      slideSize = 0;\n      var slide = slides.eq(i);\n      if (params.slidesPerColumn > 1) {\n        // Set slides order\n        var newSlideOrderIndex = (void 0);\n        var column = (void 0);\n        var row = (void 0);\n        if (params.slidesPerColumnFill === 'column') {\n          column = Math.floor(i / slidesPerColumn);\n          row = i - (column * slidesPerColumn);\n          if (column > numFullColumns || (column === numFullColumns && row === slidesPerColumn - 1)) {\n            row += 1;\n            if (row >= slidesPerColumn) {\n              row = 0;\n              column += 1;\n            }\n          }\n          newSlideOrderIndex = column + ((row * slidesNumberEvenToRows) / slidesPerColumn);\n          slide\n            .css({\n              '-webkit-box-ordinal-group': newSlideOrderIndex,\n              '-moz-box-ordinal-group': newSlideOrderIndex,\n              '-ms-flex-order': newSlideOrderIndex,\n              '-webkit-order': newSlideOrderIndex,\n              order: newSlideOrderIndex,\n            });\n        } else {\n          row = Math.floor(i / slidesPerRow);\n          column = i - (row * slidesPerRow);\n        }\n        slide\n          .css(\n            (\"margin-\" + (swiper.isHorizontal() ? 'top' : 'left')),\n            (row !== 0 && params.spaceBetween) && (((params.spaceBetween) + \"px\"))\n          )\n          .attr('data-swiper-column', column)\n          .attr('data-swiper-row', row);\n      }\n      if (slide.css('display') === 'none') { continue; } // eslint-disable-line\n\n      if (params.slidesPerView === 'auto') {\n        var slideStyles = win.getComputedStyle(slide[0], null);\n        var currentTransform = slide[0].style.transform;\n        var currentWebKitTransform = slide[0].style.webkitTransform;\n        if (currentTransform) {\n          slide[0].style.transform = 'none';\n        }\n        if (currentWebKitTransform) {\n          slide[0].style.webkitTransform = 'none';\n        }\n        if (swiper.isHorizontal()) {\n          slideSize = slide[0].getBoundingClientRect().width\n            + parseFloat(slideStyles.getPropertyValue('margin-left'))\n            + parseFloat(slideStyles.getPropertyValue('margin-right'));\n        } else {\n          slideSize = slide[0].getBoundingClientRect().height\n            + parseFloat(slideStyles.getPropertyValue('margin-top'))\n            + parseFloat(slideStyles.getPropertyValue('margin-bottom'));\n        }\n        if (currentTransform) {\n          slide[0].style.transform = currentTransform;\n        }\n        if (currentWebKitTransform) {\n          slide[0].style.webkitTransform = currentWebKitTransform;\n        }\n        if (params.roundLengths) { slideSize = Math.floor(slideSize); }\n      } else {\n        slideSize = (swiperSize - ((params.slidesPerView - 1) * spaceBetween)) / params.slidesPerView;\n        if (params.roundLengths) { slideSize = Math.floor(slideSize); }\n\n        if (slides[i]) {\n          if (swiper.isHorizontal()) {\n            slides[i].style.width = slideSize + \"px\";\n          } else {\n            slides[i].style.height = slideSize + \"px\";\n          }\n        }\n      }\n      if (slides[i]) {\n        slides[i].swiperSlideSize = slideSize;\n      }\n      slidesSizesGrid.push(slideSize);\n\n\n      if (params.centeredSlides) {\n        slidePosition = slidePosition + (slideSize / 2) + (prevSlideSize / 2) + spaceBetween;\n        if (prevSlideSize === 0 && i !== 0) { slidePosition = slidePosition - (swiperSize / 2) - spaceBetween; }\n        if (i === 0) { slidePosition = slidePosition - (swiperSize / 2) - spaceBetween; }\n        if (Math.abs(slidePosition) < 1 / 1000) { slidePosition = 0; }\n        if (params.roundLengths) { slidePosition = Math.floor(slidePosition); }\n        if ((index) % params.slidesPerGroup === 0) { snapGrid.push(slidePosition); }\n        slidesGrid.push(slidePosition);\n      } else {\n        if (params.roundLengths) { slidePosition = Math.floor(slidePosition); }\n        if ((index) % params.slidesPerGroup === 0) { snapGrid.push(slidePosition); }\n        slidesGrid.push(slidePosition);\n        slidePosition = slidePosition + slideSize + spaceBetween;\n      }\n\n      swiper.virtualSize += slideSize + spaceBetween;\n\n      prevSlideSize = slideSize;\n\n      index += 1;\n    }\n    swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n    var newSlidesGrid;\n\n    if (\n      rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n      $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") });\n    }\n    if (!Support.flexbox || params.setWrapperSize) {\n      if (swiper.isHorizontal()) { $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      else { $wrapperEl.css({ height: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n    }\n\n    if (params.slidesPerColumn > 1) {\n      swiper.virtualSize = (slideSize + params.spaceBetween) * slidesNumberEvenToRows;\n      swiper.virtualSize = Math.ceil(swiper.virtualSize / params.slidesPerColumn) - params.spaceBetween;\n      if (swiper.isHorizontal()) { $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      else { $wrapperEl.css({ height: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      if (params.centeredSlides) {\n        newSlidesGrid = [];\n        for (var i$1 = 0; i$1 < snapGrid.length; i$1 += 1) {\n          var slidesGridItem = snapGrid[i$1];\n          if (params.roundLengths) { slidesGridItem = Math.floor(slidesGridItem); }\n          if (snapGrid[i$1] < swiper.virtualSize + snapGrid[0]) { newSlidesGrid.push(slidesGridItem); }\n        }\n        snapGrid = newSlidesGrid;\n      }\n    }\n\n    // Remove last grid elements depending on width\n    if (!params.centeredSlides) {\n      newSlidesGrid = [];\n      for (var i$2 = 0; i$2 < snapGrid.length; i$2 += 1) {\n        var slidesGridItem$1 = snapGrid[i$2];\n        if (params.roundLengths) { slidesGridItem$1 = Math.floor(slidesGridItem$1); }\n        if (snapGrid[i$2] <= swiper.virtualSize - swiperSize) {\n          newSlidesGrid.push(slidesGridItem$1);\n        }\n      }\n      snapGrid = newSlidesGrid;\n      if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n        snapGrid.push(swiper.virtualSize - swiperSize);\n      }\n    }\n    if (snapGrid.length === 0) { snapGrid = [0]; }\n\n    if (params.spaceBetween !== 0) {\n      if (swiper.isHorizontal()) {\n        if (rtl) { slides.css({ marginLeft: (spaceBetween + \"px\") }); }\n        else { slides.css({ marginRight: (spaceBetween + \"px\") }); }\n      } else { slides.css({ marginBottom: (spaceBetween + \"px\") }); }\n    }\n\n    Utils.extend(swiper, {\n      slides: slides,\n      snapGrid: snapGrid,\n      slidesGrid: slidesGrid,\n      slidesSizesGrid: slidesSizesGrid,\n    });\n\n    if (slidesLength !== previousSlidesLength) {\n      swiper.emit('slidesLengthChange');\n    }\n    if (snapGrid.length !== previousSnapGridLength) {\n      if (swiper.params.watchOverflow) { swiper.checkOverflow(); }\n      swiper.emit('snapGridLengthChange');\n    }\n    if (slidesGrid.length !== previousSlidesGridLength) {\n      swiper.emit('slidesGridLengthChange');\n    }\n\n    if (params.watchSlidesProgress || params.watchSlidesVisibility) {\n      swiper.updateSlidesOffset();\n    }\n  }\n\n  function updateAutoHeight (speed) {\n    var swiper = this;\n    var activeSlides = [];\n    var newHeight = 0;\n    var i;\n    if (typeof speed === 'number') {\n      swiper.setTransition(speed);\n    } else if (speed === true) {\n      swiper.setTransition(swiper.params.speed);\n    }\n    // Find slides currently in view\n    if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        var index = swiper.activeIndex + i;\n        if (index > swiper.slides.length) { break; }\n        activeSlides.push(swiper.slides.eq(index)[0]);\n      }\n    } else {\n      activeSlides.push(swiper.slides.eq(swiper.activeIndex)[0]);\n    }\n\n    // Find new height from highest slide in view\n    for (i = 0; i < activeSlides.length; i += 1) {\n      if (typeof activeSlides[i] !== 'undefined') {\n        var height = activeSlides[i].offsetHeight;\n        newHeight = height > newHeight ? height : newHeight;\n      }\n    }\n\n    // Update Height\n    if (newHeight) { swiper.$wrapperEl.css('height', (newHeight + \"px\")); }\n  }\n\n  function updateSlidesOffset () {\n    var swiper = this;\n    var slides = swiper.slides;\n    for (var i = 0; i < slides.length; i += 1) {\n      slides[i].swiperSlideOffset = swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop;\n    }\n  }\n\n  function updateSlidesProgress (translate) {\n    if ( translate === void 0 ) translate = (this && this.translate) || 0;\n\n    var swiper = this;\n    var params = swiper.params;\n\n    var slides = swiper.slides;\n    var rtl = swiper.rtlTranslate;\n\n    if (slides.length === 0) { return; }\n    if (typeof slides[0].swiperSlideOffset === 'undefined') { swiper.updateSlidesOffset(); }\n\n    var offsetCenter = -translate;\n    if (rtl) { offsetCenter = translate; }\n\n    // Visible Slides\n    slides.removeClass(params.slideVisibleClass);\n\n    for (var i = 0; i < slides.length; i += 1) {\n      var slide = slides[i];\n      var slideProgress = (\n        (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0)) - slide.swiperSlideOffset\n      ) / (slide.swiperSlideSize + params.spaceBetween);\n      if (params.watchSlidesVisibility) {\n        var slideBefore = -(offsetCenter - slide.swiperSlideOffset);\n        var slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n        var isVisible = (slideBefore >= 0 && slideBefore < swiper.size)\n                  || (slideAfter > 0 && slideAfter <= swiper.size)\n                  || (slideBefore <= 0 && slideAfter >= swiper.size);\n        if (isVisible) {\n          slides.eq(i).addClass(params.slideVisibleClass);\n        }\n      }\n      slide.progress = rtl ? -slideProgress : slideProgress;\n    }\n  }\n\n  function updateProgress (translate) {\n    if ( translate === void 0 ) translate = (this && this.translate) || 0;\n\n    var swiper = this;\n    var params = swiper.params;\n\n    var translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n    var progress = swiper.progress;\n    var isBeginning = swiper.isBeginning;\n    var isEnd = swiper.isEnd;\n    var wasBeginning = isBeginning;\n    var wasEnd = isEnd;\n    if (translatesDiff === 0) {\n      progress = 0;\n      isBeginning = true;\n      isEnd = true;\n    } else {\n      progress = (translate - swiper.minTranslate()) / (translatesDiff);\n      isBeginning = progress <= 0;\n      isEnd = progress >= 1;\n    }\n    Utils.extend(swiper, {\n      progress: progress,\n      isBeginning: isBeginning,\n      isEnd: isEnd,\n    });\n\n    if (params.watchSlidesProgress || params.watchSlidesVisibility) { swiper.updateSlidesProgress(translate); }\n\n    if (isBeginning && !wasBeginning) {\n      swiper.emit('reachBeginning toEdge');\n    }\n    if (isEnd && !wasEnd) {\n      swiper.emit('reachEnd toEdge');\n    }\n    if ((wasBeginning && !isBeginning) || (wasEnd && !isEnd)) {\n      swiper.emit('fromEdge');\n    }\n\n    swiper.emit('progress', progress);\n  }\n\n  function updateSlidesClasses () {\n    var swiper = this;\n\n    var slides = swiper.slides;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n    var realIndex = swiper.realIndex;\n    var isVirtual = swiper.virtual && params.virtual.enabled;\n\n    slides.removeClass(((params.slideActiveClass) + \" \" + (params.slideNextClass) + \" \" + (params.slidePrevClass) + \" \" + (params.slideDuplicateActiveClass) + \" \" + (params.slideDuplicateNextClass) + \" \" + (params.slideDuplicatePrevClass)));\n\n    var activeSlide;\n    if (isVirtual) {\n      activeSlide = swiper.$wrapperEl.find((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + activeIndex + \"\\\"]\"));\n    } else {\n      activeSlide = slides.eq(activeIndex);\n    }\n\n    // Active classes\n    activeSlide.addClass(params.slideActiveClass);\n\n    if (params.loop) {\n      // Duplicate to all looped slides\n      if (activeSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]\"))\n          .addClass(params.slideDuplicateActiveClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]\"))\n          .addClass(params.slideDuplicateActiveClass);\n      }\n    }\n    // Next Slide\n    var nextSlide = activeSlide.nextAll((\".\" + (params.slideClass))).eq(0).addClass(params.slideNextClass);\n    if (params.loop && nextSlide.length === 0) {\n      nextSlide = slides.eq(0);\n      nextSlide.addClass(params.slideNextClass);\n    }\n    // Prev Slide\n    var prevSlide = activeSlide.prevAll((\".\" + (params.slideClass))).eq(0).addClass(params.slidePrevClass);\n    if (params.loop && prevSlide.length === 0) {\n      prevSlide = slides.eq(-1);\n      prevSlide.addClass(params.slidePrevClass);\n    }\n    if (params.loop) {\n      // Duplicate to all looped slides\n      if (nextSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + (nextSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicateNextClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + (nextSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicateNextClass);\n      }\n      if (prevSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + (prevSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicatePrevClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + (prevSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicatePrevClass);\n      }\n    }\n  }\n\n  function updateActiveIndex (newActiveIndex) {\n    var swiper = this;\n    var translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n    var slidesGrid = swiper.slidesGrid;\n    var snapGrid = swiper.snapGrid;\n    var params = swiper.params;\n    var previousIndex = swiper.activeIndex;\n    var previousRealIndex = swiper.realIndex;\n    var previousSnapIndex = swiper.snapIndex;\n    var activeIndex = newActiveIndex;\n    var snapIndex;\n    if (typeof activeIndex === 'undefined') {\n      for (var i = 0; i < slidesGrid.length; i += 1) {\n        if (typeof slidesGrid[i + 1] !== 'undefined') {\n          if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - ((slidesGrid[i + 1] - slidesGrid[i]) / 2)) {\n            activeIndex = i;\n          } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n            activeIndex = i + 1;\n          }\n        } else if (translate >= slidesGrid[i]) {\n          activeIndex = i;\n        }\n      }\n      // Normalize slideIndex\n      if (params.normalizeSlideIndex) {\n        if (activeIndex < 0 || typeof activeIndex === 'undefined') { activeIndex = 0; }\n      }\n    }\n    if (snapGrid.indexOf(translate) >= 0) {\n      snapIndex = snapGrid.indexOf(translate);\n    } else {\n      snapIndex = Math.floor(activeIndex / params.slidesPerGroup);\n    }\n    if (snapIndex >= snapGrid.length) { snapIndex = snapGrid.length - 1; }\n    if (activeIndex === previousIndex) {\n      if (snapIndex !== previousSnapIndex) {\n        swiper.snapIndex = snapIndex;\n        swiper.emit('snapIndexChange');\n      }\n      return;\n    }\n\n    // Get real index\n    var realIndex = parseInt(swiper.slides.eq(activeIndex).attr('data-swiper-slide-index') || activeIndex, 10);\n\n    Utils.extend(swiper, {\n      snapIndex: snapIndex,\n      realIndex: realIndex,\n      previousIndex: previousIndex,\n      activeIndex: activeIndex,\n    });\n    swiper.emit('activeIndexChange');\n    swiper.emit('snapIndexChange');\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n\n  function updateClickedSlide (e) {\n    var swiper = this;\n    var params = swiper.params;\n    var slide = $(e.target).closest((\".\" + (params.slideClass)))[0];\n    var slideFound = false;\n    if (slide) {\n      for (var i = 0; i < swiper.slides.length; i += 1) {\n        if (swiper.slides[i] === slide) { slideFound = true; }\n      }\n    }\n\n    if (slide && slideFound) {\n      swiper.clickedSlide = slide;\n      if (swiper.virtual && swiper.params.virtual.enabled) {\n        swiper.clickedIndex = parseInt($(slide).attr('data-swiper-slide-index'), 10);\n      } else {\n        swiper.clickedIndex = $(slide).index();\n      }\n    } else {\n      swiper.clickedSlide = undefined;\n      swiper.clickedIndex = undefined;\n      return;\n    }\n    if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n      swiper.slideToClickedSlide();\n    }\n  }\n\n  var update = {\n    updateSize: updateSize,\n    updateSlides: updateSlides,\n    updateAutoHeight: updateAutoHeight,\n    updateSlidesOffset: updateSlidesOffset,\n    updateSlidesProgress: updateSlidesProgress,\n    updateProgress: updateProgress,\n    updateSlidesClasses: updateSlidesClasses,\n    updateActiveIndex: updateActiveIndex,\n    updateClickedSlide: updateClickedSlide,\n  };\n\n  function getTranslate (axis) {\n    if ( axis === void 0 ) axis = this.isHorizontal() ? 'x' : 'y';\n\n    var swiper = this;\n\n    var params = swiper.params;\n    var rtl = swiper.rtlTranslate;\n    var translate = swiper.translate;\n    var $wrapperEl = swiper.$wrapperEl;\n\n    if (params.virtualTranslate) {\n      return rtl ? -translate : translate;\n    }\n\n    var currentTranslate = Utils.getTranslate($wrapperEl[0], axis);\n    if (rtl) { currentTranslate = -currentTranslate; }\n\n    return currentTranslate || 0;\n  }\n\n  function setTranslate (translate, byController) {\n    var swiper = this;\n    var rtl = swiper.rtlTranslate;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var progress = swiper.progress;\n    var x = 0;\n    var y = 0;\n    var z = 0;\n\n    if (swiper.isHorizontal()) {\n      x = rtl ? -translate : translate;\n    } else {\n      y = translate;\n    }\n\n    if (params.roundLengths) {\n      x = Math.floor(x);\n      y = Math.floor(y);\n    }\n\n    if (!params.virtualTranslate) {\n      if (Support.transforms3d) { $wrapperEl.transform((\"translate3d(\" + x + \"px, \" + y + \"px, \" + z + \"px)\")); }\n      else { $wrapperEl.transform((\"translate(\" + x + \"px, \" + y + \"px)\")); }\n    }\n    swiper.previousTranslate = swiper.translate;\n    swiper.translate = swiper.isHorizontal() ? x : y;\n\n    // Check if we need to update progress\n    var newProgress;\n    var translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n    if (translatesDiff === 0) {\n      newProgress = 0;\n    } else {\n      newProgress = (translate - swiper.minTranslate()) / (translatesDiff);\n    }\n    if (newProgress !== progress) {\n      swiper.updateProgress(translate);\n    }\n\n    swiper.emit('setTranslate', swiper.translate, byController);\n  }\n\n  function minTranslate () {\n    return (-this.snapGrid[0]);\n  }\n\n  function maxTranslate () {\n    return (-this.snapGrid[this.snapGrid.length - 1]);\n  }\n\n  var translate = {\n    getTranslate: getTranslate,\n    setTranslate: setTranslate,\n    minTranslate: minTranslate,\n    maxTranslate: maxTranslate,\n  };\n\n  function setTransition (duration, byController) {\n    var swiper = this;\n\n    swiper.$wrapperEl.transition(duration);\n\n    swiper.emit('setTransition', duration, byController);\n  }\n\n  function transitionStart (runCallbacks, direction) {\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var params = swiper.params;\n    var previousIndex = swiper.previousIndex;\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n\n    var dir = direction;\n    if (!dir) {\n      if (activeIndex > previousIndex) { dir = 'next'; }\n      else if (activeIndex < previousIndex) { dir = 'prev'; }\n      else { dir = 'reset'; }\n    }\n\n    swiper.emit('transitionStart');\n\n    if (runCallbacks && activeIndex !== previousIndex) {\n      if (dir === 'reset') {\n        swiper.emit('slideResetTransitionStart');\n        return;\n      }\n      swiper.emit('slideChangeTransitionStart');\n      if (dir === 'next') {\n        swiper.emit('slideNextTransitionStart');\n      } else {\n        swiper.emit('slidePrevTransitionStart');\n      }\n    }\n  }\n\n  function transitionEnd$1 (runCallbacks, direction) {\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var previousIndex = swiper.previousIndex;\n    swiper.animating = false;\n    swiper.setTransition(0);\n\n    var dir = direction;\n    if (!dir) {\n      if (activeIndex > previousIndex) { dir = 'next'; }\n      else if (activeIndex < previousIndex) { dir = 'prev'; }\n      else { dir = 'reset'; }\n    }\n\n    swiper.emit('transitionEnd');\n\n    if (runCallbacks && activeIndex !== previousIndex) {\n      if (dir === 'reset') {\n        swiper.emit('slideResetTransitionEnd');\n        return;\n      }\n      swiper.emit('slideChangeTransitionEnd');\n      if (dir === 'next') {\n        swiper.emit('slideNextTransitionEnd');\n      } else {\n        swiper.emit('slidePrevTransitionEnd');\n      }\n    }\n  }\n\n  var transition$1 = {\n    setTransition: setTransition,\n    transitionStart: transitionStart,\n    transitionEnd: transitionEnd$1,\n  };\n\n  function slideTo (index, speed, runCallbacks, internal) {\n    if ( index === void 0 ) index = 0;\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var slideIndex = index;\n    if (slideIndex < 0) { slideIndex = 0; }\n\n    var params = swiper.params;\n    var snapGrid = swiper.snapGrid;\n    var slidesGrid = swiper.slidesGrid;\n    var previousIndex = swiper.previousIndex;\n    var activeIndex = swiper.activeIndex;\n    var rtl = swiper.rtlTranslate;\n    if (swiper.animating && params.preventInteractionOnTransition) {\n      return false;\n    }\n\n    var snapIndex = Math.floor(slideIndex / params.slidesPerGroup);\n    if (snapIndex >= snapGrid.length) { snapIndex = snapGrid.length - 1; }\n\n    if ((activeIndex || params.initialSlide || 0) === (previousIndex || 0) && runCallbacks) {\n      swiper.emit('beforeSlideChangeStart');\n    }\n\n    var translate = -snapGrid[snapIndex];\n\n    // Update progress\n    swiper.updateProgress(translate);\n\n    // Normalize slideIndex\n    if (params.normalizeSlideIndex) {\n      for (var i = 0; i < slidesGrid.length; i += 1) {\n        if (-Math.floor(translate * 100) >= Math.floor(slidesGrid[i] * 100)) {\n          slideIndex = i;\n        }\n      }\n    }\n    // Directions locks\n    if (swiper.initialized && slideIndex !== activeIndex) {\n      if (!swiper.allowSlideNext && translate < swiper.translate && translate < swiper.minTranslate()) {\n        return false;\n      }\n      if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n        if ((activeIndex || 0) !== slideIndex) { return false; }\n      }\n    }\n\n    var direction;\n    if (slideIndex > activeIndex) { direction = 'next'; }\n    else if (slideIndex < activeIndex) { direction = 'prev'; }\n    else { direction = 'reset'; }\n\n\n    // Update Index\n    if ((rtl && -translate === swiper.translate) || (!rtl && translate === swiper.translate)) {\n      swiper.updateActiveIndex(slideIndex);\n      // Update Height\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n      swiper.updateSlidesClasses();\n      if (params.effect !== 'slide') {\n        swiper.setTranslate(translate);\n      }\n      if (direction !== 'reset') {\n        swiper.transitionStart(runCallbacks, direction);\n        swiper.transitionEnd(runCallbacks, direction);\n      }\n      return false;\n    }\n\n    if (speed === 0 || !Support.transition) {\n      swiper.setTransition(0);\n      swiper.setTranslate(translate);\n      swiper.updateActiveIndex(slideIndex);\n      swiper.updateSlidesClasses();\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    } else {\n      swiper.setTransition(speed);\n      swiper.setTranslate(translate);\n      swiper.updateActiveIndex(slideIndex);\n      swiper.updateSlidesClasses();\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.transitionStart(runCallbacks, direction);\n      if (!swiper.animating) {\n        swiper.animating = true;\n        if (!swiper.onSlideToWrapperTransitionEnd) {\n          swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n            if (!swiper || swiper.destroyed) { return; }\n            if (e.target !== this) { return; }\n            swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n            swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n            swiper.onSlideToWrapperTransitionEnd = null;\n            delete swiper.onSlideToWrapperTransitionEnd;\n            swiper.transitionEnd(runCallbacks, direction);\n          };\n        }\n        swiper.$wrapperEl[0].addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n      }\n    }\n\n    return true;\n  }\n\n  function slideToLoop (index, speed, runCallbacks, internal) {\n    if ( index === void 0 ) index = 0;\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var newIndex = index;\n    if (swiper.params.loop) {\n      newIndex += swiper.loopedSlides;\n    }\n\n    return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideNext (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var params = swiper.params;\n    var animating = swiper.animating;\n    if (params.loop) {\n      if (animating) { return false; }\n      swiper.loopFix();\n      // eslint-disable-next-line\n      swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n      return swiper.slideTo(swiper.activeIndex + params.slidesPerGroup, speed, runCallbacks, internal);\n    }\n    return swiper.slideTo(swiper.activeIndex + params.slidesPerGroup, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slidePrev (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var params = swiper.params;\n    var animating = swiper.animating;\n    var snapGrid = swiper.snapGrid;\n    var slidesGrid = swiper.slidesGrid;\n    var rtlTranslate = swiper.rtlTranslate;\n\n    if (params.loop) {\n      if (animating) { return false; }\n      swiper.loopFix();\n      // eslint-disable-next-line\n      swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n    }\n    var translate = rtlTranslate ? swiper.translate : -swiper.translate;\n    function normalize(val) {\n      if (val < 0) { return -Math.floor(Math.abs(val)); }\n      return Math.floor(val);\n    }\n    var normalizedTranslate = normalize(translate);\n    var normalizedSnapGrid = snapGrid.map(function (val) { return normalize(val); });\n    var normalizedSlidesGrid = slidesGrid.map(function (val) { return normalize(val); });\n\n    var currentSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate)];\n    var prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n    var prevIndex;\n    if (typeof prevSnap !== 'undefined') {\n      prevIndex = slidesGrid.indexOf(prevSnap);\n      if (prevIndex < 0) { prevIndex = swiper.activeIndex - 1; }\n    }\n    return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideReset (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideToClosest (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var index = swiper.activeIndex;\n    var snapIndex = Math.floor(index / swiper.params.slidesPerGroup);\n\n    if (snapIndex < swiper.snapGrid.length - 1) {\n      var translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n\n      var currentSnap = swiper.snapGrid[snapIndex];\n      var nextSnap = swiper.snapGrid[snapIndex + 1];\n\n      if ((translate - currentSnap) > (nextSnap - currentSnap) / 2) {\n        index = swiper.params.slidesPerGroup;\n      }\n    }\n\n    return swiper.slideTo(index, speed, runCallbacks, internal);\n  }\n\n  function slideToClickedSlide () {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n\n    var slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n    var slideToIndex = swiper.clickedIndex;\n    var realIndex;\n    if (params.loop) {\n      if (swiper.animating) { return; }\n      realIndex = parseInt($(swiper.clickedSlide).attr('data-swiper-slide-index'), 10);\n      if (params.centeredSlides) {\n        if (\n          (slideToIndex < swiper.loopedSlides - (slidesPerView / 2))\n          || (slideToIndex > (swiper.slides.length - swiper.loopedSlides) + (slidesPerView / 2))\n        ) {\n          swiper.loopFix();\n          slideToIndex = $wrapperEl\n            .children((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]:not(.\" + (params.slideDuplicateClass) + \")\"))\n            .eq(0)\n            .index();\n\n          Utils.nextTick(function () {\n            swiper.slideTo(slideToIndex);\n          });\n        } else {\n          swiper.slideTo(slideToIndex);\n        }\n      } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n        swiper.loopFix();\n        slideToIndex = $wrapperEl\n          .children((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]:not(.\" + (params.slideDuplicateClass) + \")\"))\n          .eq(0)\n          .index();\n\n        Utils.nextTick(function () {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  }\n\n  var slide = {\n    slideTo: slideTo,\n    slideToLoop: slideToLoop,\n    slideNext: slideNext,\n    slidePrev: slidePrev,\n    slideReset: slideReset,\n    slideToClosest: slideToClosest,\n    slideToClickedSlide: slideToClickedSlide,\n  };\n\n  function loopCreate () {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    // Remove duplicated slides\n    $wrapperEl.children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass))).remove();\n\n    var slides = $wrapperEl.children((\".\" + (params.slideClass)));\n\n    if (params.loopFillGroupWithBlank) {\n      var blankSlidesNum = params.slidesPerGroup - (slides.length % params.slidesPerGroup);\n      if (blankSlidesNum !== params.slidesPerGroup) {\n        for (var i = 0; i < blankSlidesNum; i += 1) {\n          var blankNode = $(doc.createElement('div')).addClass(((params.slideClass) + \" \" + (params.slideBlankClass)));\n          $wrapperEl.append(blankNode);\n        }\n        slides = $wrapperEl.children((\".\" + (params.slideClass)));\n      }\n    }\n\n    if (params.slidesPerView === 'auto' && !params.loopedSlides) { params.loopedSlides = slides.length; }\n\n    swiper.loopedSlides = parseInt(params.loopedSlides || params.slidesPerView, 10);\n    swiper.loopedSlides += params.loopAdditionalSlides;\n    if (swiper.loopedSlides > slides.length) {\n      swiper.loopedSlides = slides.length;\n    }\n\n    var prependSlides = [];\n    var appendSlides = [];\n    slides.each(function (index, el) {\n      var slide = $(el);\n      if (index < swiper.loopedSlides) { appendSlides.push(el); }\n      if (index < slides.length && index >= slides.length - swiper.loopedSlides) { prependSlides.push(el); }\n      slide.attr('data-swiper-slide-index', index);\n    });\n    for (var i$1 = 0; i$1 < appendSlides.length; i$1 += 1) {\n      $wrapperEl.append($(appendSlides[i$1].cloneNode(true)).addClass(params.slideDuplicateClass));\n    }\n    for (var i$2 = prependSlides.length - 1; i$2 >= 0; i$2 -= 1) {\n      $wrapperEl.prepend($(prependSlides[i$2].cloneNode(true)).addClass(params.slideDuplicateClass));\n    }\n  }\n\n  function loopFix () {\n    var swiper = this;\n    var params = swiper.params;\n    var activeIndex = swiper.activeIndex;\n    var slides = swiper.slides;\n    var loopedSlides = swiper.loopedSlides;\n    var allowSlidePrev = swiper.allowSlidePrev;\n    var allowSlideNext = swiper.allowSlideNext;\n    var snapGrid = swiper.snapGrid;\n    var rtl = swiper.rtlTranslate;\n    var newIndex;\n    swiper.allowSlidePrev = true;\n    swiper.allowSlideNext = true;\n\n    var snapTranslate = -snapGrid[activeIndex];\n    var diff = snapTranslate - swiper.getTranslate();\n\n\n    // Fix For Negative Oversliding\n    if (activeIndex < loopedSlides) {\n      newIndex = (slides.length - (loopedSlides * 3)) + activeIndex;\n      newIndex += loopedSlides;\n      var slideChanged = swiper.slideTo(newIndex, 0, false, true);\n      if (slideChanged && diff !== 0) {\n        swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n      }\n    } else if ((params.slidesPerView === 'auto' && activeIndex >= loopedSlides * 2) || (activeIndex >= slides.length - loopedSlides)) {\n      // Fix For Positive Oversliding\n      newIndex = -slides.length + activeIndex + loopedSlides;\n      newIndex += loopedSlides;\n      var slideChanged$1 = swiper.slideTo(newIndex, 0, false, true);\n      if (slideChanged$1 && diff !== 0) {\n        swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n  }\n\n  function loopDestroy () {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    var slides = swiper.slides;\n    $wrapperEl.children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass))).remove();\n    slides.removeAttr('data-swiper-slide-index');\n  }\n\n  var loop = {\n    loopCreate: loopCreate,\n    loopFix: loopFix,\n    loopDestroy: loopDestroy,\n  };\n\n  function setGrabCursor (moving) {\n    var swiper = this;\n    if (Support.touch || !swiper.params.simulateTouch || (swiper.params.watchOverflow && swiper.isLocked)) { return; }\n    var el = swiper.el;\n    el.style.cursor = 'move';\n    el.style.cursor = moving ? '-webkit-grabbing' : '-webkit-grab';\n    el.style.cursor = moving ? '-moz-grabbin' : '-moz-grab';\n    el.style.cursor = moving ? 'grabbing' : 'grab';\n  }\n\n  function unsetGrabCursor () {\n    var swiper = this;\n    if (Support.touch || (swiper.params.watchOverflow && swiper.isLocked)) { return; }\n    swiper.el.style.cursor = '';\n  }\n\n  var grabCursor = {\n    setGrabCursor: setGrabCursor,\n    unsetGrabCursor: unsetGrabCursor,\n  };\n\n  function appendSlide (slides) {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i = 0; i < slides.length; i += 1) {\n        if (slides[i]) { $wrapperEl.append(slides[i]); }\n      }\n    } else {\n      $wrapperEl.append(slides);\n    }\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n  }\n\n  function prependSlide (slides) {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n    var newActiveIndex = activeIndex + 1;\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i = 0; i < slides.length; i += 1) {\n        if (slides[i]) { $wrapperEl.prepend(slides[i]); }\n      }\n      newActiveIndex = activeIndex + slides.length;\n    } else {\n      $wrapperEl.prepend(slides);\n    }\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n\n  function addSlide (index, slides) {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    var activeIndex = swiper.activeIndex;\n    var activeIndexBuffer = activeIndex;\n    if (params.loop) {\n      activeIndexBuffer -= swiper.loopedSlides;\n      swiper.loopDestroy();\n      swiper.slides = $wrapperEl.children((\".\" + (params.slideClass)));\n    }\n    var baseLength = swiper.slides.length;\n    if (index <= 0) {\n      swiper.prependSlide(slides);\n      return;\n    }\n    if (index >= baseLength) {\n      swiper.appendSlide(slides);\n      return;\n    }\n    var newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n\n    var slidesBuffer = [];\n    for (var i = baseLength - 1; i >= index; i -= 1) {\n      var currentSlide = swiper.slides.eq(i);\n      currentSlide.remove();\n      slidesBuffer.unshift(currentSlide);\n    }\n\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i$1 = 0; i$1 < slides.length; i$1 += 1) {\n        if (slides[i$1]) { $wrapperEl.append(slides[i$1]); }\n      }\n      newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n    } else {\n      $wrapperEl.append(slides);\n    }\n\n    for (var i$2 = 0; i$2 < slidesBuffer.length; i$2 += 1) {\n      $wrapperEl.append(slidesBuffer[i$2]);\n    }\n\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    if (params.loop) {\n      swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n    } else {\n      swiper.slideTo(newActiveIndex, 0, false);\n    }\n  }\n\n  function removeSlide (slidesIndexes) {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n\n    var activeIndexBuffer = activeIndex;\n    if (params.loop) {\n      activeIndexBuffer -= swiper.loopedSlides;\n      swiper.loopDestroy();\n      swiper.slides = $wrapperEl.children((\".\" + (params.slideClass)));\n    }\n    var newActiveIndex = activeIndexBuffer;\n    var indexToRemove;\n\n    if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n      for (var i = 0; i < slidesIndexes.length; i += 1) {\n        indexToRemove = slidesIndexes[i];\n        if (swiper.slides[indexToRemove]) { swiper.slides.eq(indexToRemove).remove(); }\n        if (indexToRemove < newActiveIndex) { newActiveIndex -= 1; }\n      }\n      newActiveIndex = Math.max(newActiveIndex, 0);\n    } else {\n      indexToRemove = slidesIndexes;\n      if (swiper.slides[indexToRemove]) { swiper.slides.eq(indexToRemove).remove(); }\n      if (indexToRemove < newActiveIndex) { newActiveIndex -= 1; }\n      newActiveIndex = Math.max(newActiveIndex, 0);\n    }\n\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    if (params.loop) {\n      swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n    } else {\n      swiper.slideTo(newActiveIndex, 0, false);\n    }\n  }\n\n  function removeAllSlides () {\n    var swiper = this;\n\n    var slidesIndexes = [];\n    for (var i = 0; i < swiper.slides.length; i += 1) {\n      slidesIndexes.push(i);\n    }\n    swiper.removeSlide(slidesIndexes);\n  }\n\n  var manipulation = {\n    appendSlide: appendSlide,\n    prependSlide: prependSlide,\n    addSlide: addSlide,\n    removeSlide: removeSlide,\n    removeAllSlides: removeAllSlides,\n  };\n\n  var Device = (function Device() {\n    var ua = win.navigator.userAgent;\n\n    var device = {\n      ios: false,\n      android: false,\n      androidChrome: false,\n      desktop: false,\n      windows: false,\n      iphone: false,\n      ipod: false,\n      ipad: false,\n      cordova: win.cordova || win.phonegap,\n      phonegap: win.cordova || win.phonegap,\n    };\n\n    var windows = ua.match(/(Windows Phone);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n    var android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n    var ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n    var ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n    var iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n\n\n    // Windows\n    if (windows) {\n      device.os = 'windows';\n      device.osVersion = windows[2];\n      device.windows = true;\n    }\n    // Android\n    if (android && !windows) {\n      device.os = 'android';\n      device.osVersion = android[2];\n      device.android = true;\n      device.androidChrome = ua.toLowerCase().indexOf('chrome') >= 0;\n    }\n    if (ipad || iphone || ipod) {\n      device.os = 'ios';\n      device.ios = true;\n    }\n    // iOS\n    if (iphone && !ipod) {\n      device.osVersion = iphone[2].replace(/_/g, '.');\n      device.iphone = true;\n    }\n    if (ipad) {\n      device.osVersion = ipad[2].replace(/_/g, '.');\n      device.ipad = true;\n    }\n    if (ipod) {\n      device.osVersion = ipod[3] ? ipod[3].replace(/_/g, '.') : null;\n      device.iphone = true;\n    }\n    // iOS 8+ changed UA\n    if (device.ios && device.osVersion && ua.indexOf('Version/') >= 0) {\n      if (device.osVersion.split('.')[0] === '10') {\n        device.osVersion = ua.toLowerCase().split('version/')[1].split(' ')[0];\n      }\n    }\n\n    // Desktop\n    device.desktop = !(device.os || device.android || device.webView);\n\n    // Webview\n    device.webView = (iphone || ipad || ipod) && ua.match(/.*AppleWebKit(?!.*Safari)/i);\n\n    // Minimal UI\n    if (device.os && device.os === 'ios') {\n      var osVersionArr = device.osVersion.split('.');\n      var metaViewport = doc.querySelector('meta[name=\"viewport\"]');\n      device.minimalUi = !device.webView\n        && (ipod || iphone)\n        && (osVersionArr[0] * 1 === 7 ? osVersionArr[1] * 1 >= 1 : osVersionArr[0] * 1 > 7)\n        && metaViewport && metaViewport.getAttribute('content').indexOf('minimal-ui') >= 0;\n    }\n\n    // Pixel Ratio\n    device.pixelRatio = win.devicePixelRatio || 1;\n\n    // Export object\n    return device;\n  }());\n\n  function onTouchStart (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n    var params = swiper.params;\n    var touches = swiper.touches;\n    if (swiper.animating && params.preventInteractionOnTransition) {\n      return;\n    }\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    data.isTouchEvent = e.type === 'touchstart';\n    if (!data.isTouchEvent && 'which' in e && e.which === 3) { return; }\n    if (data.isTouched && data.isMoved) { return; }\n    if (params.noSwiping && $(e.target).closest(params.noSwipingSelector ? params.noSwipingSelector : (\".\" + (params.noSwipingClass)))[0]) {\n      swiper.allowClick = true;\n      return;\n    }\n    if (params.swipeHandler) {\n      if (!$(e).closest(params.swipeHandler)[0]) { return; }\n    }\n\n    touches.currentX = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n    touches.currentY = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n    var startX = touches.currentX;\n    var startY = touches.currentY;\n\n    // Do NOT start if iOS edge swipe is detected. Otherwise iOS app (UIWebView) cannot swipe-to-go-back anymore\n\n    var edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n    var edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n    if (\n      edgeSwipeDetection\n      && ((startX <= edgeSwipeThreshold)\n      || (startX >= win.screen.width - edgeSwipeThreshold))\n    ) {\n      return;\n    }\n\n    Utils.extend(data, {\n      isTouched: true,\n      isMoved: false,\n      allowTouchCallbacks: true,\n      isScrolling: undefined,\n      startMoving: undefined,\n    });\n\n    touches.startX = startX;\n    touches.startY = startY;\n    data.touchStartTime = Utils.now();\n    swiper.allowClick = true;\n    swiper.updateSize();\n    swiper.swipeDirection = undefined;\n    if (params.threshold > 0) { data.allowThresholdMove = false; }\n    if (e.type !== 'touchstart') {\n      var preventDefault = true;\n      if ($(e.target).is(data.formElements)) { preventDefault = false; }\n      if (\n        doc.activeElement\n        && $(doc.activeElement).is(data.formElements)\n        && doc.activeElement !== e.target\n      ) {\n        doc.activeElement.blur();\n      }\n      if (preventDefault && swiper.allowTouchMove) {\n        e.preventDefault();\n      }\n    }\n    swiper.emit('touchStart', e);\n  }\n\n  function onTouchMove (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n    var params = swiper.params;\n    var touches = swiper.touches;\n    var rtl = swiper.rtlTranslate;\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    if (!data.isTouched) {\n      if (data.startMoving && data.isScrolling) {\n        swiper.emit('touchMoveOpposite', e);\n      }\n      return;\n    }\n    if (data.isTouchEvent && e.type === 'mousemove') { return; }\n    var pageX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n    var pageY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n    if (e.preventedByNestedSwiper) {\n      touches.startX = pageX;\n      touches.startY = pageY;\n      return;\n    }\n    if (!swiper.allowTouchMove) {\n      // isMoved = true;\n      swiper.allowClick = false;\n      if (data.isTouched) {\n        Utils.extend(touches, {\n          startX: pageX,\n          startY: pageY,\n          currentX: pageX,\n          currentY: pageY,\n        });\n        data.touchStartTime = Utils.now();\n      }\n      return;\n    }\n    if (data.isTouchEvent && params.touchReleaseOnEdges && !params.loop) {\n      if (swiper.isVertical()) {\n        // Vertical\n        if (\n          (pageY < touches.startY && swiper.translate <= swiper.maxTranslate())\n          || (pageY > touches.startY && swiper.translate >= swiper.minTranslate())\n        ) {\n          data.isTouched = false;\n          data.isMoved = false;\n          return;\n        }\n      } else if (\n        (pageX < touches.startX && swiper.translate <= swiper.maxTranslate())\n        || (pageX > touches.startX && swiper.translate >= swiper.minTranslate())\n      ) {\n        return;\n      }\n    }\n    if (data.isTouchEvent && doc.activeElement) {\n      if (e.target === doc.activeElement && $(e.target).is(data.formElements)) {\n        data.isMoved = true;\n        swiper.allowClick = false;\n        return;\n      }\n    }\n    if (data.allowTouchCallbacks) {\n      swiper.emit('touchMove', e);\n    }\n    if (e.targetTouches && e.targetTouches.length > 1) { return; }\n\n    touches.currentX = pageX;\n    touches.currentY = pageY;\n\n    var diffX = touches.currentX - touches.startX;\n    var diffY = touches.currentY - touches.startY;\n    if (swiper.params.threshold && Math.sqrt((Math.pow( diffX, 2 )) + (Math.pow( diffY, 2 ))) < swiper.params.threshold) { return; }\n\n    if (typeof data.isScrolling === 'undefined') {\n      var touchAngle;\n      if ((swiper.isHorizontal() && touches.currentY === touches.startY) || (swiper.isVertical() && touches.currentX === touches.startX)) {\n        data.isScrolling = false;\n      } else {\n        // eslint-disable-next-line\n        if ((diffX * diffX) + (diffY * diffY) >= 25) {\n          touchAngle = (Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180) / Math.PI;\n          data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : (90 - touchAngle > params.touchAngle);\n        }\n      }\n    }\n    if (data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    if (typeof data.startMoving === 'undefined') {\n      if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n        data.startMoving = true;\n      }\n    }\n    if (data.isScrolling) {\n      data.isTouched = false;\n      return;\n    }\n    if (!data.startMoving) {\n      return;\n    }\n    swiper.allowClick = false;\n    e.preventDefault();\n    if (params.touchMoveStopPropagation && !params.nested) {\n      e.stopPropagation();\n    }\n\n    if (!data.isMoved) {\n      if (params.loop) {\n        swiper.loopFix();\n      }\n      data.startTranslate = swiper.getTranslate();\n      swiper.setTransition(0);\n      if (swiper.animating) {\n        swiper.$wrapperEl.trigger('webkitTransitionEnd transitionend');\n      }\n      data.allowMomentumBounce = false;\n      // Grab Cursor\n      if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n        swiper.setGrabCursor(true);\n      }\n      swiper.emit('sliderFirstMove', e);\n    }\n    swiper.emit('sliderMove', e);\n    data.isMoved = true;\n\n    var diff = swiper.isHorizontal() ? diffX : diffY;\n    touches.diff = diff;\n\n    diff *= params.touchRatio;\n    if (rtl) { diff = -diff; }\n\n    swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n    data.currentTranslate = diff + data.startTranslate;\n\n    var disableParentSwiper = true;\n    var resistanceRatio = params.resistanceRatio;\n    if (params.touchReleaseOnEdges) {\n      resistanceRatio = 0;\n    }\n    if ((diff > 0 && data.currentTranslate > swiper.minTranslate())) {\n      disableParentSwiper = false;\n      if (params.resistance) { data.currentTranslate = (swiper.minTranslate() - 1) + (Math.pow( (-swiper.minTranslate() + data.startTranslate + diff), resistanceRatio )); }\n    } else if (diff < 0 && data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) { data.currentTranslate = (swiper.maxTranslate() + 1) - (Math.pow( (swiper.maxTranslate() - data.startTranslate - diff), resistanceRatio )); }\n    }\n\n    if (disableParentSwiper) {\n      e.preventedByNestedSwiper = true;\n    }\n\n    // Directions locks\n    if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n      data.currentTranslate = data.startTranslate;\n    }\n    if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n      data.currentTranslate = data.startTranslate;\n    }\n\n\n    // Threshold\n    if (params.threshold > 0) {\n      if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n        if (!data.allowThresholdMove) {\n          data.allowThresholdMove = true;\n          touches.startX = touches.currentX;\n          touches.startY = touches.currentY;\n          data.currentTranslate = data.startTranslate;\n          touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n          return;\n        }\n      } else {\n        data.currentTranslate = data.startTranslate;\n        return;\n      }\n    }\n\n    if (!params.followFinger) { return; }\n\n    // Update active index in free mode\n    if (params.freeMode || params.watchSlidesProgress || params.watchSlidesVisibility) {\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    if (params.freeMode) {\n      // Velocity\n      if (data.velocities.length === 0) {\n        data.velocities.push({\n          position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n          time: data.touchStartTime,\n        });\n      }\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n        time: Utils.now(),\n      });\n    }\n    // Update progress\n    swiper.updateProgress(data.currentTranslate);\n    // Update translate\n    swiper.setTranslate(data.currentTranslate);\n  }\n\n  function onTouchEnd (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n\n    var params = swiper.params;\n    var touches = swiper.touches;\n    var rtl = swiper.rtlTranslate;\n    var $wrapperEl = swiper.$wrapperEl;\n    var slidesGrid = swiper.slidesGrid;\n    var snapGrid = swiper.snapGrid;\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    if (data.allowTouchCallbacks) {\n      swiper.emit('touchEnd', e);\n    }\n    data.allowTouchCallbacks = false;\n    if (!data.isTouched) {\n      if (data.isMoved && params.grabCursor) {\n        swiper.setGrabCursor(false);\n      }\n      data.isMoved = false;\n      data.startMoving = false;\n      return;\n    }\n    // Return Grab Cursor\n    if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(false);\n    }\n\n    // Time diff\n    var touchEndTime = Utils.now();\n    var timeDiff = touchEndTime - data.touchStartTime;\n\n    // Tap, doubleTap, Click\n    if (swiper.allowClick) {\n      swiper.updateClickedSlide(e);\n      swiper.emit('tap', e);\n      if (timeDiff < 300 && (touchEndTime - data.lastClickTime) > 300) {\n        if (data.clickTimeout) { clearTimeout(data.clickTimeout); }\n        data.clickTimeout = Utils.nextTick(function () {\n          if (!swiper || swiper.destroyed) { return; }\n          swiper.emit('click', e);\n        }, 300);\n      }\n      if (timeDiff < 300 && (touchEndTime - data.lastClickTime) < 300) {\n        if (data.clickTimeout) { clearTimeout(data.clickTimeout); }\n        swiper.emit('doubleTap', e);\n      }\n    }\n\n    data.lastClickTime = Utils.now();\n    Utils.nextTick(function () {\n      if (!swiper.destroyed) { swiper.allowClick = true; }\n    });\n\n    if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 || data.currentTranslate === data.startTranslate) {\n      data.isTouched = false;\n      data.isMoved = false;\n      data.startMoving = false;\n      return;\n    }\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n\n    var currentPos;\n    if (params.followFinger) {\n      currentPos = rtl ? swiper.translate : -swiper.translate;\n    } else {\n      currentPos = -data.currentTranslate;\n    }\n\n    if (params.freeMode) {\n      if (currentPos < -swiper.minTranslate()) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (currentPos > -swiper.maxTranslate()) {\n        if (swiper.slides.length < snapGrid.length) {\n          swiper.slideTo(snapGrid.length - 1);\n        } else {\n          swiper.slideTo(swiper.slides.length - 1);\n        }\n        return;\n      }\n\n      if (params.freeModeMomentum) {\n        if (data.velocities.length > 1) {\n          var lastMoveEvent = data.velocities.pop();\n          var velocityEvent = data.velocities.pop();\n\n          var distance = lastMoveEvent.position - velocityEvent.position;\n          var time = lastMoveEvent.time - velocityEvent.time;\n          swiper.velocity = distance / time;\n          swiper.velocity /= 2;\n          if (Math.abs(swiper.velocity) < params.freeModeMinimumVelocity) {\n            swiper.velocity = 0;\n          }\n          // this implies that the user stopped moving a finger then released.\n          // There would be no events with distance zero, so the last event is stale.\n          if (time > 150 || (Utils.now() - lastMoveEvent.time) > 300) {\n            swiper.velocity = 0;\n          }\n        } else {\n          swiper.velocity = 0;\n        }\n        swiper.velocity *= params.freeModeMomentumVelocityRatio;\n\n        data.velocities.length = 0;\n        var momentumDuration = 1000 * params.freeModeMomentumRatio;\n        var momentumDistance = swiper.velocity * momentumDuration;\n\n        var newPosition = swiper.translate + momentumDistance;\n        if (rtl) { newPosition = -newPosition; }\n\n        var doBounce = false;\n        var afterBouncePosition;\n        var bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeModeMomentumBounceRatio;\n        var needsLoopFix;\n        if (newPosition < swiper.maxTranslate()) {\n          if (params.freeModeMomentumBounce) {\n            if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n              newPosition = swiper.maxTranslate() - bounceAmount;\n            }\n            afterBouncePosition = swiper.maxTranslate();\n            doBounce = true;\n            data.allowMomentumBounce = true;\n          } else {\n            newPosition = swiper.maxTranslate();\n          }\n          if (params.loop && params.centeredSlides) { needsLoopFix = true; }\n        } else if (newPosition > swiper.minTranslate()) {\n          if (params.freeModeMomentumBounce) {\n            if (newPosition - swiper.minTranslate() > bounceAmount) {\n              newPosition = swiper.minTranslate() + bounceAmount;\n            }\n            afterBouncePosition = swiper.minTranslate();\n            doBounce = true;\n            data.allowMomentumBounce = true;\n          } else {\n            newPosition = swiper.minTranslate();\n          }\n          if (params.loop && params.centeredSlides) { needsLoopFix = true; }\n        } else if (params.freeModeSticky) {\n          var nextSlide;\n          for (var j = 0; j < snapGrid.length; j += 1) {\n            if (snapGrid[j] > -newPosition) {\n              nextSlide = j;\n              break;\n            }\n          }\n\n          if (Math.abs(snapGrid[nextSlide] - newPosition) < Math.abs(snapGrid[nextSlide - 1] - newPosition) || swiper.swipeDirection === 'next') {\n            newPosition = snapGrid[nextSlide];\n          } else {\n            newPosition = snapGrid[nextSlide - 1];\n          }\n          newPosition = -newPosition;\n        }\n        if (needsLoopFix) {\n          swiper.once('transitionEnd', function () {\n            swiper.loopFix();\n          });\n        }\n        // Fix duration\n        if (swiper.velocity !== 0) {\n          if (rtl) {\n            momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n          } else {\n            momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n          }\n        } else if (params.freeModeSticky) {\n          swiper.slideToClosest();\n          return;\n        }\n\n        if (params.freeModeMomentumBounce && doBounce) {\n          swiper.updateProgress(afterBouncePosition);\n          swiper.setTransition(momentumDuration);\n          swiper.setTranslate(newPosition);\n          swiper.transitionStart(true, swiper.swipeDirection);\n          swiper.animating = true;\n          $wrapperEl.transitionEnd(function () {\n            if (!swiper || swiper.destroyed || !data.allowMomentumBounce) { return; }\n            swiper.emit('momentumBounce');\n\n            swiper.setTransition(params.speed);\n            swiper.setTranslate(afterBouncePosition);\n            $wrapperEl.transitionEnd(function () {\n              if (!swiper || swiper.destroyed) { return; }\n              swiper.transitionEnd();\n            });\n          });\n        } else if (swiper.velocity) {\n          swiper.updateProgress(newPosition);\n          swiper.setTransition(momentumDuration);\n          swiper.setTranslate(newPosition);\n          swiper.transitionStart(true, swiper.swipeDirection);\n          if (!swiper.animating) {\n            swiper.animating = true;\n            $wrapperEl.transitionEnd(function () {\n              if (!swiper || swiper.destroyed) { return; }\n              swiper.transitionEnd();\n            });\n          }\n        } else {\n          swiper.updateProgress(newPosition);\n        }\n\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      } else if (params.freeModeSticky) {\n        swiper.slideToClosest();\n        return;\n      }\n\n      if (!params.freeModeMomentum || timeDiff >= params.longSwipesMs) {\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      }\n      return;\n    }\n\n    // Find current slide\n    var stopIndex = 0;\n    var groupSize = swiper.slidesSizesGrid[0];\n    for (var i = 0; i < slidesGrid.length; i += params.slidesPerGroup) {\n      if (typeof slidesGrid[i + params.slidesPerGroup] !== 'undefined') {\n        if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + params.slidesPerGroup]) {\n          stopIndex = i;\n          groupSize = slidesGrid[i + params.slidesPerGroup] - slidesGrid[i];\n        }\n      } else if (currentPos >= slidesGrid[i]) {\n        stopIndex = i;\n        groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n      }\n    }\n\n    // Find current slide size\n    var ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n\n    if (timeDiff > params.longSwipesMs) {\n      // Long touches\n      if (!params.longSwipes) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (swiper.swipeDirection === 'next') {\n        if (ratio >= params.longSwipesRatio) { swiper.slideTo(stopIndex + params.slidesPerGroup); }\n        else { swiper.slideTo(stopIndex); }\n      }\n      if (swiper.swipeDirection === 'prev') {\n        if (ratio > (1 - params.longSwipesRatio)) { swiper.slideTo(stopIndex + params.slidesPerGroup); }\n        else { swiper.slideTo(stopIndex); }\n      }\n    } else {\n      // Short swipes\n      if (!params.shortSwipes) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(stopIndex + params.slidesPerGroup);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  }\n\n  function onResize () {\n    var swiper = this;\n\n    var params = swiper.params;\n    var el = swiper.el;\n\n    if (el && el.offsetWidth === 0) { return; }\n\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Save locks\n    var allowSlideNext = swiper.allowSlideNext;\n    var allowSlidePrev = swiper.allowSlidePrev;\n    var snapGrid = swiper.snapGrid;\n\n    // Disable locks on resize\n    swiper.allowSlideNext = true;\n    swiper.allowSlidePrev = true;\n\n    swiper.updateSize();\n    swiper.updateSlides();\n\n    if (params.freeMode) {\n      var newTranslate = Math.min(Math.max(swiper.translate, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      swiper.updateSlidesClasses();\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n        swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n      } else {\n        swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n    }\n    // Return locks after resize\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n\n    if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n  }\n\n  function onClick (e) {\n    var swiper = this;\n    if (!swiper.allowClick) {\n      if (swiper.params.preventClicks) { e.preventDefault(); }\n      if (swiper.params.preventClicksPropagation && swiper.animating) {\n        e.stopPropagation();\n        e.stopImmediatePropagation();\n      }\n    }\n  }\n\n  function attachEvents() {\n    var swiper = this;\n    var params = swiper.params;\n    var touchEvents = swiper.touchEvents;\n    var el = swiper.el;\n    var wrapperEl = swiper.wrapperEl;\n\n    {\n      swiper.onTouchStart = onTouchStart.bind(swiper);\n      swiper.onTouchMove = onTouchMove.bind(swiper);\n      swiper.onTouchEnd = onTouchEnd.bind(swiper);\n    }\n\n    swiper.onClick = onClick.bind(swiper);\n\n    var target = params.touchEventsTarget === 'container' ? el : wrapperEl;\n    var capture = !!params.nested;\n\n    // Touch Events\n    {\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.addEventListener(touchEvents.start, swiper.onTouchStart, false);\n        doc.addEventListener(touchEvents.move, swiper.onTouchMove, capture);\n        doc.addEventListener(touchEvents.end, swiper.onTouchEnd, false);\n      } else {\n        if (Support.touch) {\n          var passiveListener = touchEvents.start === 'touchstart' && Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n          target.addEventListener(touchEvents.start, swiper.onTouchStart, passiveListener);\n          target.addEventListener(touchEvents.move, swiper.onTouchMove, Support.passiveListener ? { passive: false, capture: capture } : capture);\n          target.addEventListener(touchEvents.end, swiper.onTouchEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.addEventListener('mousedown', swiper.onTouchStart, false);\n          doc.addEventListener('mousemove', swiper.onTouchMove, capture);\n          doc.addEventListener('mouseup', swiper.onTouchEnd, false);\n        }\n      }\n      // Prevent Links Clicks\n      if (params.preventClicks || params.preventClicksPropagation) {\n        target.addEventListener('click', swiper.onClick, true);\n      }\n    }\n\n    // Resize handler\n    swiper.on((Device.ios || Device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate'), onResize, true);\n  }\n\n  function detachEvents() {\n    var swiper = this;\n\n    var params = swiper.params;\n    var touchEvents = swiper.touchEvents;\n    var el = swiper.el;\n    var wrapperEl = swiper.wrapperEl;\n\n    var target = params.touchEventsTarget === 'container' ? el : wrapperEl;\n    var capture = !!params.nested;\n\n    // Touch Events\n    {\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.removeEventListener(touchEvents.start, swiper.onTouchStart, false);\n        doc.removeEventListener(touchEvents.move, swiper.onTouchMove, capture);\n        doc.removeEventListener(touchEvents.end, swiper.onTouchEnd, false);\n      } else {\n        if (Support.touch) {\n          var passiveListener = touchEvents.start === 'onTouchStart' && Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n          target.removeEventListener(touchEvents.start, swiper.onTouchStart, passiveListener);\n          target.removeEventListener(touchEvents.move, swiper.onTouchMove, capture);\n          target.removeEventListener(touchEvents.end, swiper.onTouchEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.removeEventListener('mousedown', swiper.onTouchStart, false);\n          doc.removeEventListener('mousemove', swiper.onTouchMove, capture);\n          doc.removeEventListener('mouseup', swiper.onTouchEnd, false);\n        }\n      }\n      // Prevent Links Clicks\n      if (params.preventClicks || params.preventClicksPropagation) {\n        target.removeEventListener('click', swiper.onClick, true);\n      }\n    }\n\n    // Resize handler\n    swiper.off((Device.ios || Device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate'), onResize);\n  }\n\n  var events = {\n    attachEvents: attachEvents,\n    detachEvents: detachEvents,\n  };\n\n  function setBreakpoint () {\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var initialized = swiper.initialized;\n    var loopedSlides = swiper.loopedSlides; if ( loopedSlides === void 0 ) loopedSlides = 0;\n    var params = swiper.params;\n    var breakpoints = params.breakpoints;\n    if (!breakpoints || (breakpoints && Object.keys(breakpoints).length === 0)) { return; }\n    // Set breakpoint for window width and update parameters\n    var breakpoint = swiper.getBreakpoint(breakpoints);\n    if (breakpoint && swiper.currentBreakpoint !== breakpoint) {\n      var breakPointsParams = breakpoint in breakpoints ? breakpoints[breakpoint] : swiper.originalParams;\n      var needsReLoop = params.loop && (breakPointsParams.slidesPerView !== params.slidesPerView);\n\n      Utils.extend(swiper.params, breakPointsParams);\n\n      Utils.extend(swiper, {\n        allowTouchMove: swiper.params.allowTouchMove,\n        allowSlideNext: swiper.params.allowSlideNext,\n        allowSlidePrev: swiper.params.allowSlidePrev,\n      });\n\n      swiper.currentBreakpoint = breakpoint;\n\n      if (needsReLoop && initialized) {\n        swiper.loopDestroy();\n        swiper.loopCreate();\n        swiper.updateSlides();\n        swiper.slideTo((activeIndex - loopedSlides) + swiper.loopedSlides, 0, false);\n      }\n      swiper.emit('breakpoint', breakPointsParams);\n    }\n  }\n\n  function getBreakpoint (breakpoints) {\n    // Get breakpoint for window width\n    if (!breakpoints) { return undefined; }\n    var breakpoint = false;\n    var points = [];\n    Object.keys(breakpoints).forEach(function (point) {\n      points.push(point);\n    });\n    points.sort(function (a, b) { return parseInt(a, 10) - parseInt(b, 10); });\n    for (var i = 0; i < points.length; i += 1) {\n      var point = points[i];\n      if (point >= win.innerWidth && !breakpoint) {\n        breakpoint = point;\n      }\n    }\n    return breakpoint || 'max';\n  }\n\n  var breakpoints = { setBreakpoint: setBreakpoint, getBreakpoint: getBreakpoint };\n\n  var Browser = (function Browser() {\n    function isSafari() {\n      var ua = win.navigator.userAgent.toLowerCase();\n      return (ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0);\n    }\n    return {\n      isIE: !!win.navigator.userAgent.match(/Trident/g) || !!win.navigator.userAgent.match(/MSIE/g),\n      isSafari: isSafari(),\n      isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(win.navigator.userAgent),\n    };\n  }());\n\n  function addClasses () {\n    var swiper = this;\n    var classNames = swiper.classNames;\n    var params = swiper.params;\n    var rtl = swiper.rtl;\n    var $el = swiper.$el;\n    var suffixes = [];\n\n    suffixes.push(params.direction);\n\n    if (params.freeMode) {\n      suffixes.push('free-mode');\n    }\n    if (!Support.flexbox) {\n      suffixes.push('no-flexbox');\n    }\n    if (params.autoHeight) {\n      suffixes.push('autoheight');\n    }\n    if (rtl) {\n      suffixes.push('rtl');\n    }\n    if (params.slidesPerColumn > 1) {\n      suffixes.push('multirow');\n    }\n    if (Device.android) {\n      suffixes.push('android');\n    }\n    if (Device.ios) {\n      suffixes.push('ios');\n    }\n    // WP8 Touch Events Fix\n    if (Browser.isIE && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n      suffixes.push((\"wp8-\" + (params.direction)));\n    }\n\n    suffixes.forEach(function (suffix) {\n      classNames.push(params.containerModifierClass + suffix);\n    });\n\n    $el.addClass(classNames.join(' '));\n  }\n\n  function removeClasses () {\n    var swiper = this;\n    var $el = swiper.$el;\n    var classNames = swiper.classNames;\n\n    $el.removeClass(classNames.join(' '));\n  }\n\n  var classes = { addClasses: addClasses, removeClasses: removeClasses };\n\n  function loadImage (imageEl, src, srcset, sizes, checkForComplete, callback) {\n    var image;\n    function onReady() {\n      if (callback) { callback(); }\n    }\n    if (!imageEl.complete || !checkForComplete) {\n      if (src) {\n        image = new win.Image();\n        image.onload = onReady;\n        image.onerror = onReady;\n        if (sizes) {\n          image.sizes = sizes;\n        }\n        if (srcset) {\n          image.srcset = srcset;\n        }\n        if (src) {\n          image.src = src;\n        }\n      } else {\n        onReady();\n      }\n    } else {\n      // image already loaded...\n      onReady();\n    }\n  }\n\n  function preloadImages () {\n    var swiper = this;\n    swiper.imagesToLoad = swiper.$el.find('img');\n    function onReady() {\n      if (typeof swiper === 'undefined' || swiper === null || !swiper || swiper.destroyed) { return; }\n      if (swiper.imagesLoaded !== undefined) { swiper.imagesLoaded += 1; }\n      if (swiper.imagesLoaded === swiper.imagesToLoad.length) {\n        if (swiper.params.updateOnImagesReady) { swiper.update(); }\n        swiper.emit('imagesReady');\n      }\n    }\n    for (var i = 0; i < swiper.imagesToLoad.length; i += 1) {\n      var imageEl = swiper.imagesToLoad[i];\n      swiper.loadImage(\n        imageEl,\n        imageEl.currentSrc || imageEl.getAttribute('src'),\n        imageEl.srcset || imageEl.getAttribute('srcset'),\n        imageEl.sizes || imageEl.getAttribute('sizes'),\n        true,\n        onReady\n      );\n    }\n  }\n\n  var images = {\n    loadImage: loadImage,\n    preloadImages: preloadImages,\n  };\n\n  function checkOverflow() {\n    var swiper = this;\n    var wasLocked = swiper.isLocked;\n\n    swiper.isLocked = swiper.snapGrid.length === 1;\n    swiper.allowSlideNext = !swiper.isLocked;\n    swiper.allowSlidePrev = !swiper.isLocked;\n\n    // events\n    if (wasLocked !== swiper.isLocked) { swiper.emit(swiper.isLocked ? 'lock' : 'unlock'); }\n\n    if (wasLocked && wasLocked !== swiper.isLocked) {\n      swiper.isEnd = false;\n      swiper.navigation.update();\n    }\n  }\n\n  var checkOverflow$1 = { checkOverflow: checkOverflow };\n\n  var defaults = {\n    init: true,\n    direction: 'horizontal',\n    touchEventsTarget: 'container',\n    initialSlide: 0,\n    speed: 300,\n    //\n    preventInteractionOnTransition: false,\n\n    // To support iOS's swipe-to-go-back gesture (when being used in-app, with UIWebView).\n    edgeSwipeDetection: false,\n    edgeSwipeThreshold: 20,\n\n    // Free mode\n    freeMode: false,\n    freeModeMomentum: true,\n    freeModeMomentumRatio: 1,\n    freeModeMomentumBounce: true,\n    freeModeMomentumBounceRatio: 1,\n    freeModeMomentumVelocityRatio: 1,\n    freeModeSticky: false,\n    freeModeMinimumVelocity: 0.02,\n\n    // Autoheight\n    autoHeight: false,\n\n    // Set wrapper width\n    setWrapperSize: false,\n\n    // Virtual Translate\n    virtualTranslate: false,\n\n    // Effects\n    effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n    // Breakpoints\n    breakpoints: undefined,\n\n    // Slides grid\n    spaceBetween: 0,\n    slidesPerView: 1,\n    slidesPerColumn: 1,\n    slidesPerColumnFill: 'column',\n    slidesPerGroup: 1,\n    centeredSlides: false,\n    slidesOffsetBefore: 0, // in px\n    slidesOffsetAfter: 0, // in px\n    normalizeSlideIndex: true,\n\n    // Disable swiper and hide navigation when container not overflow\n    watchOverflow: false,\n\n    // Round length\n    roundLengths: false,\n\n    // Touches\n    touchRatio: 1,\n    touchAngle: 45,\n    simulateTouch: true,\n    shortSwipes: true,\n    longSwipes: true,\n    longSwipesRatio: 0.5,\n    longSwipesMs: 300,\n    followFinger: true,\n    allowTouchMove: true,\n    threshold: 0,\n    touchMoveStopPropagation: true,\n    touchReleaseOnEdges: false,\n\n    // Unique Navigation Elements\n    uniqueNavElements: true,\n\n    // Resistance\n    resistance: true,\n    resistanceRatio: 0.85,\n\n    // Progress\n    watchSlidesProgress: false,\n    watchSlidesVisibility: false,\n\n    // Cursor\n    grabCursor: false,\n\n    // Clicks\n    preventClicks: true,\n    preventClicksPropagation: true,\n    slideToClickedSlide: false,\n\n    // Images\n    preloadImages: true,\n    updateOnImagesReady: true,\n\n    // loop\n    loop: false,\n    loopAdditionalSlides: 0,\n    loopedSlides: null,\n    loopFillGroupWithBlank: false,\n\n    // Swiping/no swiping\n    allowSlidePrev: true,\n    allowSlideNext: true,\n    swipeHandler: null, // '.swipe-handler',\n    noSwiping: true,\n    noSwipingClass: 'swiper-no-swiping',\n    noSwipingSelector: null,\n\n    // Passive Listeners\n    passiveListeners: true,\n\n    // NS\n    containerModifierClass: 'swiper-container-', // NEW\n    slideClass: 'swiper-slide',\n    slideBlankClass: 'swiper-slide-invisible-blank',\n    slideActiveClass: 'swiper-slide-active',\n    slideDuplicateActiveClass: 'swiper-slide-duplicate-active',\n    slideVisibleClass: 'swiper-slide-visible',\n    slideDuplicateClass: 'swiper-slide-duplicate',\n    slideNextClass: 'swiper-slide-next',\n    slideDuplicateNextClass: 'swiper-slide-duplicate-next',\n    slidePrevClass: 'swiper-slide-prev',\n    slideDuplicatePrevClass: 'swiper-slide-duplicate-prev',\n    wrapperClass: 'swiper-wrapper',\n\n    // Callbacks\n    runCallbacksOnInit: true,\n  };\n\n  var prototypes = {\n    update: update,\n    translate: translate,\n    transition: transition$1,\n    slide: slide,\n    loop: loop,\n    grabCursor: grabCursor,\n    manipulation: manipulation,\n    events: events,\n    breakpoints: breakpoints,\n    checkOverflow: checkOverflow$1,\n    classes: classes,\n    images: images,\n  };\n\n  var extendedDefaults = {};\n\n  var Swiper = (function (SwiperClass$$1) {\n    function Swiper() {\n      var assign;\n\n      var args = [], len = arguments.length;\n      while ( len-- ) args[ len ] = arguments[ len ];\n      var el;\n      var params;\n      if (args.length === 1 && args[0].constructor && args[0].constructor === Object) {\n        params = args[0];\n      } else {\n        (assign = args, el = assign[0], params = assign[1]);\n      }\n      if (!params) { params = {}; }\n\n      params = Utils.extend({}, params);\n      if (el && !params.el) { params.el = el; }\n\n      SwiperClass$$1.call(this, params);\n\n      Object.keys(prototypes).forEach(function (prototypeGroup) {\n        Object.keys(prototypes[prototypeGroup]).forEach(function (protoMethod) {\n          if (!Swiper.prototype[protoMethod]) {\n            Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n          }\n        });\n      });\n\n      // Swiper Instance\n      var swiper = this;\n      if (typeof swiper.modules === 'undefined') {\n        swiper.modules = {};\n      }\n      Object.keys(swiper.modules).forEach(function (moduleName) {\n        var module = swiper.modules[moduleName];\n        if (module.params) {\n          var moduleParamName = Object.keys(module.params)[0];\n          var moduleParams = module.params[moduleParamName];\n          if (typeof moduleParams !== 'object') { return; }\n          if (!(moduleParamName in params && 'enabled' in moduleParams)) { return; }\n          if (params[moduleParamName] === true) {\n            params[moduleParamName] = { enabled: true };\n          }\n          if (\n            typeof params[moduleParamName] === 'object'\n            && !('enabled' in params[moduleParamName])\n          ) {\n            params[moduleParamName].enabled = true;\n          }\n          if (!params[moduleParamName]) { params[moduleParamName] = { enabled: false }; }\n        }\n      });\n\n      // Extend defaults with modules params\n      var swiperParams = Utils.extend({}, defaults);\n      swiper.useModulesParams(swiperParams);\n\n      // Extend defaults with passed params\n      swiper.params = Utils.extend({}, swiperParams, extendedDefaults, params);\n      swiper.originalParams = Utils.extend({}, swiper.params);\n      swiper.passedParams = Utils.extend({}, params);\n\n      // Save Dom lib\n      swiper.$ = $;\n\n      // Find el\n      var $el = $(swiper.params.el);\n      el = $el[0];\n\n      if (!el) {\n        return undefined;\n      }\n\n      if ($el.length > 1) {\n        var swipers = [];\n        $el.each(function (index, containerEl) {\n          var newParams = Utils.extend({}, params, { el: containerEl });\n          swipers.push(new Swiper(newParams));\n        });\n        return swipers;\n      }\n\n      el.swiper = swiper;\n      $el.data('swiper', swiper);\n\n      // Find Wrapper\n      var $wrapperEl = $el.children((\".\" + (swiper.params.wrapperClass)));\n\n      // Extend Swiper\n      Utils.extend(swiper, {\n        $el: $el,\n        el: el,\n        $wrapperEl: $wrapperEl,\n        wrapperEl: $wrapperEl[0],\n\n        // Classes\n        classNames: [],\n\n        // Slides\n        slides: $(),\n        slidesGrid: [],\n        snapGrid: [],\n        slidesSizesGrid: [],\n\n        // isDirection\n        isHorizontal: function isHorizontal() {\n          return swiper.params.direction === 'horizontal';\n        },\n        isVertical: function isVertical() {\n          return swiper.params.direction === 'vertical';\n        },\n        // RTL\n        rtl: (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n        rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n        wrongRTL: $wrapperEl.css('display') === '-webkit-box',\n\n        // Indexes\n        activeIndex: 0,\n        realIndex: 0,\n\n        //\n        isBeginning: true,\n        isEnd: false,\n\n        // Props\n        translate: 0,\n        previousTranslate: 0,\n        progress: 0,\n        velocity: 0,\n        animating: false,\n\n        // Locks\n        allowSlideNext: swiper.params.allowSlideNext,\n        allowSlidePrev: swiper.params.allowSlidePrev,\n\n        // Touch Events\n        touchEvents: (function touchEvents() {\n          var touch = ['touchstart', 'touchmove', 'touchend'];\n          var desktop = ['mousedown', 'mousemove', 'mouseup'];\n          if (Support.pointerEvents) {\n            desktop = ['pointerdown', 'pointermove', 'pointerup'];\n          } else if (Support.prefixedPointerEvents) {\n            desktop = ['MSPointerDown', 'MSPointerMove', 'MSPointerUp'];\n          }\n          swiper.touchEventsTouch = {\n            start: touch[0],\n            move: touch[1],\n            end: touch[2],\n          };\n          swiper.touchEventsDesktop = {\n            start: desktop[0],\n            move: desktop[1],\n            end: desktop[2],\n          };\n          return Support.touch || !swiper.params.simulateTouch ? swiper.touchEventsTouch : swiper.touchEventsDesktop;\n        }()),\n        touchEventsData: {\n          isTouched: undefined,\n          isMoved: undefined,\n          allowTouchCallbacks: undefined,\n          touchStartTime: undefined,\n          isScrolling: undefined,\n          currentTranslate: undefined,\n          startTranslate: undefined,\n          allowThresholdMove: undefined,\n          // Form elements to match\n          formElements: 'input, select, option, textarea, button, video',\n          // Last click time\n          lastClickTime: Utils.now(),\n          clickTimeout: undefined,\n          // Velocities\n          velocities: [],\n          allowMomentumBounce: undefined,\n          isTouchEvent: undefined,\n          startMoving: undefined,\n        },\n\n        // Clicks\n        allowClick: true,\n\n        // Touches\n        allowTouchMove: swiper.params.allowTouchMove,\n\n        touches: {\n          startX: 0,\n          startY: 0,\n          currentX: 0,\n          currentY: 0,\n          diff: 0,\n        },\n\n        // Images\n        imagesToLoad: [],\n        imagesLoaded: 0,\n\n      });\n\n      // Install Modules\n      swiper.useModules();\n\n      // Init\n      if (swiper.params.init) {\n        swiper.init();\n      }\n\n      // Return app instance\n      return swiper;\n    }\n\n    if ( SwiperClass$$1 ) Swiper.__proto__ = SwiperClass$$1;\n    Swiper.prototype = Object.create( SwiperClass$$1 && SwiperClass$$1.prototype );\n    Swiper.prototype.constructor = Swiper;\n\n    var staticAccessors = { extendedDefaults: { configurable: true },defaults: { configurable: true },Class: { configurable: true },$: { configurable: true } };\n\n    Swiper.prototype.slidesPerViewDynamic = function slidesPerViewDynamic () {\n      var swiper = this;\n      var params = swiper.params;\n      var slides = swiper.slides;\n      var slidesGrid = swiper.slidesGrid;\n      var swiperSize = swiper.size;\n      var activeIndex = swiper.activeIndex;\n      var spv = 1;\n      if (params.centeredSlides) {\n        var slideSize = slides[activeIndex].swiperSlideSize;\n        var breakLoop;\n        for (var i = activeIndex + 1; i < slides.length; i += 1) {\n          if (slides[i] && !breakLoop) {\n            slideSize += slides[i].swiperSlideSize;\n            spv += 1;\n            if (slideSize > swiperSize) { breakLoop = true; }\n          }\n        }\n        for (var i$1 = activeIndex - 1; i$1 >= 0; i$1 -= 1) {\n          if (slides[i$1] && !breakLoop) {\n            slideSize += slides[i$1].swiperSlideSize;\n            spv += 1;\n            if (slideSize > swiperSize) { breakLoop = true; }\n          }\n        }\n      } else {\n        for (var i$2 = activeIndex + 1; i$2 < slides.length; i$2 += 1) {\n          if (slidesGrid[i$2] - slidesGrid[activeIndex] < swiperSize) {\n            spv += 1;\n          }\n        }\n      }\n      return spv;\n    };\n\n    Swiper.prototype.update = function update$$1 () {\n      var swiper = this;\n      if (!swiper || swiper.destroyed) { return; }\n      var snapGrid = swiper.snapGrid;\n      var params = swiper.params;\n      // Breakpoints\n      if (params.breakpoints) {\n        swiper.setBreakpoint();\n      }\n      swiper.updateSize();\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n\n      function setTranslate() {\n        var translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n        var newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n        swiper.setTranslate(newTranslate);\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      }\n      var translated;\n      if (swiper.params.freeMode) {\n        setTranslate();\n        if (swiper.params.autoHeight) {\n          swiper.updateAutoHeight();\n        }\n      } else {\n        if ((swiper.params.slidesPerView === 'auto' || swiper.params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n          translated = swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n        } else {\n          translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n        }\n        if (!translated) {\n          setTranslate();\n        }\n      }\n      if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n        swiper.checkOverflow();\n      }\n      swiper.emit('update');\n    };\n\n    Swiper.prototype.init = function init () {\n      var swiper = this;\n      if (swiper.initialized) { return; }\n\n      swiper.emit('beforeInit');\n\n      // Set breakpoint\n      if (swiper.params.breakpoints) {\n        swiper.setBreakpoint();\n      }\n\n      // Add Classes\n      swiper.addClasses();\n\n      // Create loop\n      if (swiper.params.loop) {\n        swiper.loopCreate();\n      }\n\n      // Update size\n      swiper.updateSize();\n\n      // Update slides\n      swiper.updateSlides();\n\n      if (swiper.params.watchOverflow) {\n        swiper.checkOverflow();\n      }\n\n      // Set Grab Cursor\n      if (swiper.params.grabCursor) {\n        swiper.setGrabCursor();\n      }\n\n      if (swiper.params.preloadImages) {\n        swiper.preloadImages();\n      }\n\n      // Slide To Initial Slide\n      if (swiper.params.loop) {\n        swiper.slideTo(swiper.params.initialSlide + swiper.loopedSlides, 0, swiper.params.runCallbacksOnInit);\n      } else {\n        swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit);\n      }\n\n      // Attach events\n      swiper.attachEvents();\n\n      // Init Flag\n      swiper.initialized = true;\n\n      // Emit\n      swiper.emit('init');\n    };\n\n    Swiper.prototype.destroy = function destroy (deleteInstance, cleanStyles) {\n      if ( deleteInstance === void 0 ) deleteInstance = true;\n      if ( cleanStyles === void 0 ) cleanStyles = true;\n\n      var swiper = this;\n      var params = swiper.params;\n      var $el = swiper.$el;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slides = swiper.slides;\n\n      if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n        return null;\n      }\n\n      swiper.emit('beforeDestroy');\n\n      // Init Flag\n      swiper.initialized = false;\n\n      // Detach events\n      swiper.detachEvents();\n\n      // Destroy loop\n      if (params.loop) {\n        swiper.loopDestroy();\n      }\n\n      // Cleanup styles\n      if (cleanStyles) {\n        swiper.removeClasses();\n        $el.removeAttr('style');\n        $wrapperEl.removeAttr('style');\n        if (slides && slides.length) {\n          slides\n            .removeClass([\n              params.slideVisibleClass,\n              params.slideActiveClass,\n              params.slideNextClass,\n              params.slidePrevClass ].join(' '))\n            .removeAttr('style')\n            .removeAttr('data-swiper-slide-index')\n            .removeAttr('data-swiper-column')\n            .removeAttr('data-swiper-row');\n        }\n      }\n\n      swiper.emit('destroy');\n\n      // Detach emitter events\n      Object.keys(swiper.eventsListeners).forEach(function (eventName) {\n        swiper.off(eventName);\n      });\n\n      if (deleteInstance !== false) {\n        swiper.$el[0].swiper = null;\n        swiper.$el.data('swiper', null);\n        Utils.deleteProps(swiper);\n      }\n      swiper.destroyed = true;\n\n      return null;\n    };\n\n    Swiper.extendDefaults = function extendDefaults (newDefaults) {\n      Utils.extend(extendedDefaults, newDefaults);\n    };\n\n    staticAccessors.extendedDefaults.get = function () {\n      return extendedDefaults;\n    };\n\n    staticAccessors.defaults.get = function () {\n      return defaults;\n    };\n\n    staticAccessors.Class.get = function () {\n      return SwiperClass$$1;\n    };\n\n    staticAccessors.$.get = function () {\n      return $;\n    };\n\n    Object.defineProperties( Swiper, staticAccessors );\n\n    return Swiper;\n  }(SwiperClass));\n\n  var Device$1 = {\n    name: 'device',\n    proto: {\n      device: Device,\n    },\n    static: {\n      device: Device,\n    },\n  };\n\n  var Support$1 = {\n    name: 'support',\n    proto: {\n      support: Support,\n    },\n    static: {\n      support: Support,\n    },\n  };\n\n  var Browser$1 = {\n    name: 'browser',\n    proto: {\n      browser: Browser,\n    },\n    static: {\n      browser: Browser,\n    },\n  };\n\n  var Resize = {\n    name: 'resize',\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        resize: {\n          resizeHandler: function resizeHandler() {\n            if (!swiper || swiper.destroyed || !swiper.initialized) { return; }\n            swiper.emit('beforeResize');\n            swiper.emit('resize');\n          },\n          orientationChangeHandler: function orientationChangeHandler() {\n            if (!swiper || swiper.destroyed || !swiper.initialized) { return; }\n            swiper.emit('orientationchange');\n          },\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        // Emit resize\n        win.addEventListener('resize', swiper.resize.resizeHandler);\n\n        // Emit orientationchange\n        win.addEventListener('orientationchange', swiper.resize.orientationChangeHandler);\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        win.removeEventListener('resize', swiper.resize.resizeHandler);\n        win.removeEventListener('orientationchange', swiper.resize.orientationChangeHandler);\n      },\n    },\n  };\n\n  var Observer = {\n    func: win.MutationObserver || win.WebkitMutationObserver,\n    attach: function attach(target, options) {\n      if ( options === void 0 ) options = {};\n\n      var swiper = this;\n\n      var ObserverFunc = Observer.func;\n      var observer = new ObserverFunc(function (mutations) {\n        // The observerUpdate event should only be triggered\n        // once despite the number of mutations.  Additional\n        // triggers are redundant and are very costly\n        if (mutations.length === 1) {\n          swiper.emit('observerUpdate', mutations[0]);\n          return;\n        }\n        var observerUpdate = function observerUpdate() {\n          swiper.emit('observerUpdate', mutations[0]);\n        };\n\n        if (win.requestAnimationFrame) {\n          win.requestAnimationFrame(observerUpdate);\n        } else {\n          win.setTimeout(observerUpdate, 0);\n        }\n      });\n\n      observer.observe(target, {\n        attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n        childList: typeof options.childList === 'undefined' ? true : options.childList,\n        characterData: typeof options.characterData === 'undefined' ? true : options.characterData,\n      });\n\n      swiper.observer.observers.push(observer);\n    },\n    init: function init() {\n      var swiper = this;\n      if (!Support.observer || !swiper.params.observer) { return; }\n      if (swiper.params.observeParents) {\n        var containerParents = swiper.$el.parents();\n        for (var i = 0; i < containerParents.length; i += 1) {\n          swiper.observer.attach(containerParents[i]);\n        }\n      }\n      // Observe container\n      swiper.observer.attach(swiper.$el[0], { childList: false });\n\n      // Observe wrapper\n      swiper.observer.attach(swiper.$wrapperEl[0], { attributes: false });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      swiper.observer.observers.forEach(function (observer) {\n        observer.disconnect();\n      });\n      swiper.observer.observers = [];\n    },\n  };\n\n  var Observer$1 = {\n    name: 'observer',\n    params: {\n      observer: false,\n      observeParents: false,\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        observer: {\n          init: Observer.init.bind(swiper),\n          attach: Observer.attach.bind(swiper),\n          destroy: Observer.destroy.bind(swiper),\n          observers: [],\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.observer.init();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.observer.destroy();\n      },\n    },\n  };\n\n  var Virtual = {\n    update: function update(force) {\n      var swiper = this;\n      var ref = swiper.params;\n      var slidesPerView = ref.slidesPerView;\n      var slidesPerGroup = ref.slidesPerGroup;\n      var centeredSlides = ref.centeredSlides;\n      var ref$1 = swiper.virtual;\n      var previousFrom = ref$1.from;\n      var previousTo = ref$1.to;\n      var slides = ref$1.slides;\n      var previousSlidesGrid = ref$1.slidesGrid;\n      var renderSlide = ref$1.renderSlide;\n      var previousOffset = ref$1.offset;\n      swiper.updateActiveIndex();\n      var activeIndex = swiper.activeIndex || 0;\n\n      var offsetProp;\n      if (swiper.rtlTranslate) { offsetProp = 'right'; }\n      else { offsetProp = swiper.isHorizontal() ? 'left' : 'top'; }\n\n      var slidesAfter;\n      var slidesBefore;\n      if (centeredSlides) {\n        slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup;\n        slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup;\n      } else {\n        slidesAfter = slidesPerView + (slidesPerGroup - 1);\n        slidesBefore = slidesPerGroup;\n      }\n      var from = Math.max((activeIndex || 0) - slidesBefore, 0);\n      var to = Math.min((activeIndex || 0) + slidesAfter, slides.length - 1);\n      var offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n\n      Utils.extend(swiper.virtual, {\n        from: from,\n        to: to,\n        offset: offset,\n        slidesGrid: swiper.slidesGrid,\n      });\n\n      function onRendered() {\n        swiper.updateSlides();\n        swiper.updateProgress();\n        swiper.updateSlidesClasses();\n        if (swiper.lazy && swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      }\n\n      if (previousFrom === from && previousTo === to && !force) {\n        if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n          swiper.slides.css(offsetProp, (offset + \"px\"));\n        }\n        swiper.updateProgress();\n        return;\n      }\n      if (swiper.params.virtual.renderExternal) {\n        swiper.params.virtual.renderExternal.call(swiper, {\n          offset: offset,\n          from: from,\n          to: to,\n          slides: (function getSlides() {\n            var slidesToRender = [];\n            for (var i = from; i <= to; i += 1) {\n              slidesToRender.push(slides[i]);\n            }\n            return slidesToRender;\n          }()),\n        });\n        onRendered();\n        return;\n      }\n      var prependIndexes = [];\n      var appendIndexes = [];\n      if (force) {\n        swiper.$wrapperEl.find((\".\" + (swiper.params.slideClass))).remove();\n      } else {\n        for (var i = previousFrom; i <= previousTo; i += 1) {\n          if (i < from || i > to) {\n            swiper.$wrapperEl.find((\".\" + (swiper.params.slideClass) + \"[data-swiper-slide-index=\\\"\" + i + \"\\\"]\")).remove();\n          }\n        }\n      }\n      for (var i$1 = 0; i$1 < slides.length; i$1 += 1) {\n        if (i$1 >= from && i$1 <= to) {\n          if (typeof previousTo === 'undefined' || force) {\n            appendIndexes.push(i$1);\n          } else {\n            if (i$1 > previousTo) { appendIndexes.push(i$1); }\n            if (i$1 < previousFrom) { prependIndexes.push(i$1); }\n          }\n        }\n      }\n      appendIndexes.forEach(function (index) {\n        swiper.$wrapperEl.append(renderSlide(slides[index], index));\n      });\n      prependIndexes.sort(function (a, b) { return a < b; }).forEach(function (index) {\n        swiper.$wrapperEl.prepend(renderSlide(slides[index], index));\n      });\n      swiper.$wrapperEl.children('.swiper-slide').css(offsetProp, (offset + \"px\"));\n      onRendered();\n    },\n    renderSlide: function renderSlide(slide, index) {\n      var swiper = this;\n      var params = swiper.params.virtual;\n      if (params.cache && swiper.virtual.cache[index]) {\n        return swiper.virtual.cache[index];\n      }\n      var $slideEl = params.renderSlide\n        ? $(params.renderSlide.call(swiper, slide, index))\n        : $((\"<div class=\\\"\" + (swiper.params.slideClass) + \"\\\" data-swiper-slide-index=\\\"\" + index + \"\\\">\" + slide + \"</div>\"));\n      if (!$slideEl.attr('data-swiper-slide-index')) { $slideEl.attr('data-swiper-slide-index', index); }\n      if (params.cache) { swiper.virtual.cache[index] = $slideEl; }\n      return $slideEl;\n    },\n    appendSlide: function appendSlide(slide) {\n      var swiper = this;\n      swiper.virtual.slides.push(slide);\n      swiper.virtual.update(true);\n    },\n    prependSlide: function prependSlide(slide) {\n      var swiper = this;\n      swiper.virtual.slides.unshift(slide);\n      if (swiper.params.virtual.cache) {\n        var cache = swiper.virtual.cache;\n        var newCache = {};\n        Object.keys(cache).forEach(function (cachedIndex) {\n          newCache[cachedIndex + 1] = cache[cachedIndex];\n        });\n        swiper.virtual.cache = newCache;\n      }\n      swiper.virtual.update(true);\n      swiper.slideNext(0);\n    },\n  };\n\n  var Virtual$1 = {\n    name: 'virtual',\n    params: {\n      virtual: {\n        enabled: false,\n        slides: [],\n        cache: true,\n        renderSlide: null,\n        renderExternal: null,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        virtual: {\n          update: Virtual.update.bind(swiper),\n          appendSlide: Virtual.appendSlide.bind(swiper),\n          prependSlide: Virtual.prependSlide.bind(swiper),\n          renderSlide: Virtual.renderSlide.bind(swiper),\n          slides: swiper.params.virtual.slides,\n          cache: {},\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (!swiper.params.virtual.enabled) { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"virtual\"));\n        var overwriteParams = {\n          watchSlidesProgress: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n\n        swiper.virtual.update();\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (!swiper.params.virtual.enabled) { return; }\n        swiper.virtual.update();\n      },\n    },\n  };\n\n  var Keyboard = {\n    handle: function handle(event) {\n      var swiper = this;\n      var rtl = swiper.rtlTranslate;\n      var e = event;\n      if (e.originalEvent) { e = e.originalEvent; } // jquery fix\n      var kc = e.keyCode || e.charCode;\n      // Directions locks\n      if (!swiper.allowSlideNext && ((swiper.isHorizontal() && kc === 39) || (swiper.isVertical() && kc === 40))) {\n        return false;\n      }\n      if (!swiper.allowSlidePrev && ((swiper.isHorizontal() && kc === 37) || (swiper.isVertical() && kc === 38))) {\n        return false;\n      }\n      if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n        return undefined;\n      }\n      if (doc.activeElement && doc.activeElement.nodeName && (doc.activeElement.nodeName.toLowerCase() === 'input' || doc.activeElement.nodeName.toLowerCase() === 'textarea')) {\n        return undefined;\n      }\n      if (swiper.params.keyboard.onlyInViewport && (kc === 37 || kc === 39 || kc === 38 || kc === 40)) {\n        var inView = false;\n        // Check that swiper should be inside of visible area of window\n        if (swiper.$el.parents((\".\" + (swiper.params.slideClass))).length > 0 && swiper.$el.parents((\".\" + (swiper.params.slideActiveClass))).length === 0) {\n          return undefined;\n        }\n        var windowWidth = win.innerWidth;\n        var windowHeight = win.innerHeight;\n        var swiperOffset = swiper.$el.offset();\n        if (rtl) { swiperOffset.left -= swiper.$el[0].scrollLeft; }\n        var swiperCoord = [\n          [swiperOffset.left, swiperOffset.top],\n          [swiperOffset.left + swiper.width, swiperOffset.top],\n          [swiperOffset.left, swiperOffset.top + swiper.height],\n          [swiperOffset.left + swiper.width, swiperOffset.top + swiper.height] ];\n        for (var i = 0; i < swiperCoord.length; i += 1) {\n          var point = swiperCoord[i];\n          if (\n            point[0] >= 0 && point[0] <= windowWidth\n            && point[1] >= 0 && point[1] <= windowHeight\n          ) {\n            inView = true;\n          }\n        }\n        if (!inView) { return undefined; }\n      }\n      if (swiper.isHorizontal()) {\n        if (kc === 37 || kc === 39) {\n          if (e.preventDefault) { e.preventDefault(); }\n          else { e.returnValue = false; }\n        }\n        if ((kc === 39 && !rtl) || (kc === 37 && rtl)) { swiper.slideNext(); }\n        if ((kc === 37 && !rtl) || (kc === 39 && rtl)) { swiper.slidePrev(); }\n      } else {\n        if (kc === 38 || kc === 40) {\n          if (e.preventDefault) { e.preventDefault(); }\n          else { e.returnValue = false; }\n        }\n        if (kc === 40) { swiper.slideNext(); }\n        if (kc === 38) { swiper.slidePrev(); }\n      }\n      swiper.emit('keyPress', kc);\n      return undefined;\n    },\n    enable: function enable() {\n      var swiper = this;\n      if (swiper.keyboard.enabled) { return; }\n      $(doc).on('keydown', swiper.keyboard.handle);\n      swiper.keyboard.enabled = true;\n    },\n    disable: function disable() {\n      var swiper = this;\n      if (!swiper.keyboard.enabled) { return; }\n      $(doc).off('keydown', swiper.keyboard.handle);\n      swiper.keyboard.enabled = false;\n    },\n  };\n\n  var Keyboard$1 = {\n    name: 'keyboard',\n    params: {\n      keyboard: {\n        enabled: false,\n        onlyInViewport: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        keyboard: {\n          enabled: false,\n          enable: Keyboard.enable.bind(swiper),\n          disable: Keyboard.disable.bind(swiper),\n          handle: Keyboard.handle.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.keyboard.enabled) {\n          swiper.keyboard.enable();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.keyboard.enabled) {\n          swiper.keyboard.disable();\n        }\n      },\n    },\n  };\n\n  function isEventSupported() {\n    var eventName = 'onwheel';\n    var isSupported = eventName in doc;\n\n    if (!isSupported) {\n      var element = doc.createElement('div');\n      element.setAttribute(eventName, 'return;');\n      isSupported = typeof element[eventName] === 'function';\n    }\n\n    if (!isSupported\n      && doc.implementation\n      && doc.implementation.hasFeature\n      // always returns true in newer browsers as per the standard.\n      // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n      && doc.implementation.hasFeature('', '') !== true\n    ) {\n      // This is the only way to test support for the `wheel` event in IE9+.\n      isSupported = doc.implementation.hasFeature('Events.wheel', '3.0');\n    }\n\n    return isSupported;\n  }\n  var Mousewheel = {\n    lastScrollTime: Utils.now(),\n    event: (function getEvent() {\n      if (win.navigator.userAgent.indexOf('firefox') > -1) { return 'DOMMouseScroll'; }\n      return isEventSupported() ? 'wheel' : 'mousewheel';\n    }()),\n    normalize: function normalize(e) {\n      // Reasonable defaults\n      var PIXEL_STEP = 10;\n      var LINE_HEIGHT = 40;\n      var PAGE_HEIGHT = 800;\n\n      var sX = 0;\n      var sY = 0; // spinX, spinY\n      var pX = 0;\n      var pY = 0; // pixelX, pixelY\n\n      // Legacy\n      if ('detail' in e) {\n        sY = e.detail;\n      }\n      if ('wheelDelta' in e) {\n        sY = -e.wheelDelta / 120;\n      }\n      if ('wheelDeltaY' in e) {\n        sY = -e.wheelDeltaY / 120;\n      }\n      if ('wheelDeltaX' in e) {\n        sX = -e.wheelDeltaX / 120;\n      }\n\n      // side scrolling on FF with DOMMouseScroll\n      if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n        sX = sY;\n        sY = 0;\n      }\n\n      pX = sX * PIXEL_STEP;\n      pY = sY * PIXEL_STEP;\n\n      if ('deltaY' in e) {\n        pY = e.deltaY;\n      }\n      if ('deltaX' in e) {\n        pX = e.deltaX;\n      }\n\n      if ((pX || pY) && e.deltaMode) {\n        if (e.deltaMode === 1) { // delta in LINE units\n          pX *= LINE_HEIGHT;\n          pY *= LINE_HEIGHT;\n        } else { // delta in PAGE units\n          pX *= PAGE_HEIGHT;\n          pY *= PAGE_HEIGHT;\n        }\n      }\n\n      // Fall-back if spin cannot be determined\n      if (pX && !sX) {\n        sX = (pX < 1) ? -1 : 1;\n      }\n      if (pY && !sY) {\n        sY = (pY < 1) ? -1 : 1;\n      }\n\n      return {\n        spinX: sX,\n        spinY: sY,\n        pixelX: pX,\n        pixelY: pY,\n      };\n    },\n    handleMouseEnter: function handleMouseEnter() {\n      var swiper = this;\n      swiper.mouseEntered = true;\n    },\n    handleMouseLeave: function handleMouseLeave() {\n      var swiper = this;\n      swiper.mouseEntered = false;\n    },\n    handle: function handle(event) {\n      var e = event;\n      var swiper = this;\n      var params = swiper.params.mousewheel;\n\n      if (!swiper.mouseEntered && !params.releaseOnEdges) { return true; }\n\n      if (e.originalEvent) { e = e.originalEvent; } // jquery fix\n      var delta = 0;\n      var rtlFactor = swiper.rtlTranslate ? -1 : 1;\n\n      var data = Mousewheel.normalize(e);\n\n      if (params.forceToAxis) {\n        if (swiper.isHorizontal()) {\n          if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) { delta = data.pixelX * rtlFactor; }\n          else { return true; }\n        } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) { delta = data.pixelY; }\n        else { return true; }\n      } else {\n        delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n      }\n\n      if (delta === 0) { return true; }\n\n      if (params.invert) { delta = -delta; }\n\n      if (!swiper.params.freeMode) {\n        if (Utils.now() - swiper.mousewheel.lastScrollTime > 60) {\n          if (delta < 0) {\n            if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n              swiper.slideNext();\n              swiper.emit('scroll', e);\n            } else if (params.releaseOnEdges) { return true; }\n          } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n            swiper.slidePrev();\n            swiper.emit('scroll', e);\n          } else if (params.releaseOnEdges) { return true; }\n        }\n        swiper.mousewheel.lastScrollTime = (new win.Date()).getTime();\n      } else {\n        // Freemode or scrollContainer:\n        if (swiper.params.loop) {\n          swiper.loopFix();\n        }\n        var position = swiper.getTranslate() + (delta * params.sensitivity);\n        var wasBeginning = swiper.isBeginning;\n        var wasEnd = swiper.isEnd;\n\n        if (position >= swiper.minTranslate()) { position = swiper.minTranslate(); }\n        if (position <= swiper.maxTranslate()) { position = swiper.maxTranslate(); }\n\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n\n        if ((!wasBeginning && swiper.isBeginning) || (!wasEnd && swiper.isEnd)) {\n          swiper.updateSlidesClasses();\n        }\n\n        if (swiper.params.freeModeSticky) {\n          clearTimeout(swiper.mousewheel.timeout);\n          swiper.mousewheel.timeout = Utils.nextTick(function () {\n            swiper.slideToClosest();\n          }, 300);\n        }\n        // Emit event\n        swiper.emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplayDisableOnInteraction) { swiper.autoplay.stop(); }\n        // Return page scroll on edge positions\n        if (position === swiper.minTranslate() || position === swiper.maxTranslate()) { return true; }\n      }\n\n      if (e.preventDefault) { e.preventDefault(); }\n      else { e.returnValue = false; }\n      return false;\n    },\n    enable: function enable() {\n      var swiper = this;\n      if (!Mousewheel.event) { return false; }\n      if (swiper.mousewheel.enabled) { return false; }\n      var target = swiper.$el;\n      if (swiper.params.mousewheel.eventsTarged !== 'container') {\n        target = $(swiper.params.mousewheel.eventsTarged);\n      }\n      target.on('mouseenter', swiper.mousewheel.handleMouseEnter);\n      target.on('mouseleave', swiper.mousewheel.handleMouseLeave);\n      target.on(Mousewheel.event, swiper.mousewheel.handle);\n      swiper.mousewheel.enabled = true;\n      return true;\n    },\n    disable: function disable() {\n      var swiper = this;\n      if (!Mousewheel.event) { return false; }\n      if (!swiper.mousewheel.enabled) { return false; }\n      var target = swiper.$el;\n      if (swiper.params.mousewheel.eventsTarged !== 'container') {\n        target = $(swiper.params.mousewheel.eventsTarged);\n      }\n      target.off(Mousewheel.event, swiper.mousewheel.handle);\n      swiper.mousewheel.enabled = false;\n      return true;\n    },\n  };\n\n  var Mousewheel$1 = {\n    name: 'mousewheel',\n    params: {\n      mousewheel: {\n        enabled: false,\n        releaseOnEdges: false,\n        invert: false,\n        forceToAxis: false,\n        sensitivity: 1,\n        eventsTarged: 'container',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        mousewheel: {\n          enabled: false,\n          enable: Mousewheel.enable.bind(swiper),\n          disable: Mousewheel.disable.bind(swiper),\n          handle: Mousewheel.handle.bind(swiper),\n          handleMouseEnter: Mousewheel.handleMouseEnter.bind(swiper),\n          handleMouseLeave: Mousewheel.handleMouseLeave.bind(swiper),\n          lastScrollTime: Utils.now(),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.mousewheel.enabled) { swiper.mousewheel.enable(); }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.mousewheel.enabled) { swiper.mousewheel.disable(); }\n      },\n    },\n  };\n\n  var Navigation = {\n    update: function update() {\n      // Update Navigation Buttons\n      var swiper = this;\n      var params = swiper.params.navigation;\n\n      if (swiper.params.loop) { return; }\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n\n      if ($prevEl && $prevEl.length > 0) {\n        if (swiper.isBeginning) {\n          $prevEl.addClass(params.disabledClass);\n        } else {\n          $prevEl.removeClass(params.disabledClass);\n        }\n        $prevEl[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n      }\n      if ($nextEl && $nextEl.length > 0) {\n        if (swiper.isEnd) {\n          $nextEl.addClass(params.disabledClass);\n        } else {\n          $nextEl.removeClass(params.disabledClass);\n        }\n        $nextEl[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      var params = swiper.params.navigation;\n      if (!(params.nextEl || params.prevEl)) { return; }\n\n      var $nextEl;\n      var $prevEl;\n      if (params.nextEl) {\n        $nextEl = $(params.nextEl);\n        if (\n          swiper.params.uniqueNavElements\n          && typeof params.nextEl === 'string'\n          && $nextEl.length > 1\n          && swiper.$el.find(params.nextEl).length === 1\n        ) {\n          $nextEl = swiper.$el.find(params.nextEl);\n        }\n      }\n      if (params.prevEl) {\n        $prevEl = $(params.prevEl);\n        if (\n          swiper.params.uniqueNavElements\n          && typeof params.prevEl === 'string'\n          && $prevEl.length > 1\n          && swiper.$el.find(params.prevEl).length === 1\n        ) {\n          $prevEl = swiper.$el.find(params.prevEl);\n        }\n      }\n\n      if ($nextEl && $nextEl.length > 0) {\n        $nextEl.on('click', function (e) {\n          e.preventDefault();\n          if (swiper.isEnd && !swiper.params.loop) { return; }\n          swiper.slideNext();\n        });\n      }\n      if ($prevEl && $prevEl.length > 0) {\n        $prevEl.on('click', function (e) {\n          e.preventDefault();\n          if (swiper.isBeginning && !swiper.params.loop) { return; }\n          swiper.slidePrev();\n        });\n      }\n\n      Utils.extend(swiper.navigation, {\n        $nextEl: $nextEl,\n        nextEl: $nextEl && $nextEl[0],\n        $prevEl: $prevEl,\n        prevEl: $prevEl && $prevEl[0],\n      });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n      if ($nextEl && $nextEl.length) {\n        $nextEl.off('click');\n        $nextEl.removeClass(swiper.params.navigation.disabledClass);\n      }\n      if ($prevEl && $prevEl.length) {\n        $prevEl.off('click');\n        $prevEl.removeClass(swiper.params.navigation.disabledClass);\n      }\n    },\n  };\n\n  var Navigation$1 = {\n    name: 'navigation',\n    params: {\n      navigation: {\n        nextEl: null,\n        prevEl: null,\n\n        hideOnClick: false,\n        disabledClass: 'swiper-button-disabled',\n        hiddenClass: 'swiper-button-hidden',\n        lockClass: 'swiper-button-lock',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        navigation: {\n          init: Navigation.init.bind(swiper),\n          update: Navigation.update.bind(swiper),\n          destroy: Navigation.destroy.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.navigation.init();\n        swiper.navigation.update();\n      },\n      toEdge: function toEdge() {\n        var swiper = this;\n        swiper.navigation.update();\n      },\n      fromEdge: function fromEdge() {\n        var swiper = this;\n        swiper.navigation.update();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.navigation.destroy();\n      },\n      click: function click(e) {\n        var swiper = this;\n        var ref = swiper.navigation;\n        var $nextEl = ref.$nextEl;\n        var $prevEl = ref.$prevEl;\n        if (\n          swiper.params.navigation.hideOnClick\n          && !$(e.target).is($prevEl)\n          && !$(e.target).is($nextEl)\n        ) {\n          if ($nextEl) { $nextEl.toggleClass(swiper.params.navigation.hiddenClass); }\n          if ($prevEl) { $prevEl.toggleClass(swiper.params.navigation.hiddenClass); }\n        }\n      },\n    },\n  };\n\n  var Pagination = {\n    update: function update() {\n      // Render || Update Pagination bullets/items\n      var swiper = this;\n      var rtl = swiper.rtl;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n      var $el = swiper.pagination.$el;\n      // Current/Total\n      var current;\n      var total = swiper.params.loop ? Math.ceil((slidesLength - (swiper.loopedSlides * 2)) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.loop) {\n        current = Math.ceil((swiper.activeIndex - swiper.loopedSlides) / swiper.params.slidesPerGroup);\n        if (current > slidesLength - 1 - (swiper.loopedSlides * 2)) {\n          current -= (slidesLength - (swiper.loopedSlides * 2));\n        }\n        if (current > total - 1) { current -= total; }\n        if (current < 0 && swiper.params.paginationType !== 'bullets') { current = total + current; }\n      } else if (typeof swiper.snapIndex !== 'undefined') {\n        current = swiper.snapIndex;\n      } else {\n        current = swiper.activeIndex || 0;\n      }\n      // Types\n      if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n        var bullets = swiper.pagination.bullets;\n        var firstIndex;\n        var lastIndex;\n        var midIndex;\n        if (params.dynamicBullets) {\n          swiper.pagination.bulletSize = bullets.eq(0)[swiper.isHorizontal() ? 'outerWidth' : 'outerHeight'](true);\n          $el.css(swiper.isHorizontal() ? 'width' : 'height', ((swiper.pagination.bulletSize * (params.dynamicMainBullets + 4)) + \"px\"));\n          if (params.dynamicMainBullets > 1 && swiper.previousIndex !== undefined) {\n            swiper.pagination.dynamicBulletIndex += (current - swiper.previousIndex);\n            if (swiper.pagination.dynamicBulletIndex > (params.dynamicMainBullets - 1)) {\n              swiper.pagination.dynamicBulletIndex = params.dynamicMainBullets - 1;\n            } else if (swiper.pagination.dynamicBulletIndex < 0) {\n              swiper.pagination.dynamicBulletIndex = 0;\n            }\n          }\n          firstIndex = current - swiper.pagination.dynamicBulletIndex;\n          lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n          midIndex = (lastIndex + firstIndex) / 2;\n        }\n        bullets.removeClass(((params.bulletActiveClass) + \" \" + (params.bulletActiveClass) + \"-next \" + (params.bulletActiveClass) + \"-next-next \" + (params.bulletActiveClass) + \"-prev \" + (params.bulletActiveClass) + \"-prev-prev \" + (params.bulletActiveClass) + \"-main\"));\n        if ($el.length > 1) {\n          bullets.each(function (index, bullet) {\n            var $bullet = $(bullet);\n            var bulletIndex = $bullet.index();\n            if (bulletIndex === current) {\n              $bullet.addClass(params.bulletActiveClass);\n            }\n            if (params.dynamicBullets) {\n              if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n                $bullet.addClass(((params.bulletActiveClass) + \"-main\"));\n              }\n              if (bulletIndex === firstIndex) {\n                $bullet\n                  .prev()\n                  .addClass(((params.bulletActiveClass) + \"-prev\"))\n                  .prev()\n                  .addClass(((params.bulletActiveClass) + \"-prev-prev\"));\n              }\n              if (bulletIndex === lastIndex) {\n                $bullet\n                  .next()\n                  .addClass(((params.bulletActiveClass) + \"-next\"))\n                  .next()\n                  .addClass(((params.bulletActiveClass) + \"-next-next\"));\n              }\n            }\n          });\n        } else {\n          var $bullet = bullets.eq(current);\n          $bullet.addClass(params.bulletActiveClass);\n          if (params.dynamicBullets) {\n            var $firstDisplayedBullet = bullets.eq(firstIndex);\n            var $lastDisplayedBullet = bullets.eq(lastIndex);\n            for (var i = firstIndex; i <= lastIndex; i += 1) {\n              bullets.eq(i).addClass(((params.bulletActiveClass) + \"-main\"));\n            }\n            $firstDisplayedBullet\n              .prev()\n              .addClass(((params.bulletActiveClass) + \"-prev\"))\n              .prev()\n              .addClass(((params.bulletActiveClass) + \"-prev-prev\"));\n            $lastDisplayedBullet\n              .next()\n              .addClass(((params.bulletActiveClass) + \"-next\"))\n              .next()\n              .addClass(((params.bulletActiveClass) + \"-next-next\"));\n          }\n        }\n        if (params.dynamicBullets) {\n          var dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n          var bulletsOffset = (((swiper.pagination.bulletSize * dynamicBulletsLength) - (swiper.pagination.bulletSize)) / 2) - (midIndex * swiper.pagination.bulletSize);\n          var offsetProp = rtl ? 'right' : 'left';\n          bullets.css(swiper.isHorizontal() ? offsetProp : 'top', (bulletsOffset + \"px\"));\n        }\n      }\n      if (params.type === 'fraction') {\n        $el.find((\".\" + (params.currentClass))).text(params.formatFractionCurrent(current + 1));\n        $el.find((\".\" + (params.totalClass))).text(params.formatFractionTotal(total));\n      }\n      if (params.type === 'progressbar') {\n        var progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        var scale = (current + 1) / total;\n        var scaleX = 1;\n        var scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        $el.find((\".\" + (params.progressbarFillClass))).transform((\"translate3d(0,0,0) scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\")).transition(swiper.params.speed);\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        $el.html(params.renderCustom(swiper, current + 1, total));\n        swiper.emit('paginationRender', swiper, $el[0]);\n      } else {\n        swiper.emit('paginationUpdate', swiper, $el[0]);\n      }\n      $el[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n    },\n    render: function render() {\n      // Render Container\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n\n      var $el = swiper.pagination.$el;\n      var paginationHTML = '';\n      if (params.type === 'bullets') {\n        var numberOfBullets = swiper.params.loop ? Math.ceil((slidesLength - (swiper.loopedSlides * 2)) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n        for (var i = 0; i < numberOfBullets; i += 1) {\n          if (params.renderBullet) {\n            paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n          } else {\n            paginationHTML += \"<\" + (params.bulletElement) + \" class=\\\"\" + (params.bulletClass) + \"\\\"></\" + (params.bulletElement) + \">\";\n          }\n        }\n        $el.html(paginationHTML);\n        swiper.pagination.bullets = $el.find((\".\" + (params.bulletClass)));\n      }\n      if (params.type === 'fraction') {\n        if (params.renderFraction) {\n          paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n        } else {\n          paginationHTML = \"<span class=\\\"\" + (params.currentClass) + \"\\\"></span>\"\n          + ' / '\n          + \"<span class=\\\"\" + (params.totalClass) + \"\\\"></span>\";\n        }\n        $el.html(paginationHTML);\n      }\n      if (params.type === 'progressbar') {\n        if (params.renderProgressbar) {\n          paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n        } else {\n          paginationHTML = \"<span class=\\\"\" + (params.progressbarFillClass) + \"\\\"></span>\";\n        }\n        $el.html(paginationHTML);\n      }\n      if (params.type !== 'custom') {\n        swiper.emit('paginationRender', swiper.pagination.$el[0]);\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el) { return; }\n\n      var $el = $(params.el);\n      if ($el.length === 0) { return; }\n\n      if (\n        swiper.params.uniqueNavElements\n        && typeof params.el === 'string'\n        && $el.length > 1\n        && swiper.$el.find(params.el).length === 1\n      ) {\n        $el = swiper.$el.find(params.el);\n      }\n\n      if (params.type === 'bullets' && params.clickable) {\n        $el.addClass(params.clickableClass);\n      }\n\n      $el.addClass(params.modifierClass + params.type);\n\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        $el.addClass((\"\" + (params.modifierClass) + (params.type) + \"-dynamic\"));\n        swiper.pagination.dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        $el.addClass(params.progressbarOppositeClass);\n      }\n\n      if (params.clickable) {\n        $el.on('click', (\".\" + (params.bulletClass)), function onClick(e) {\n          e.preventDefault();\n          var index = $(this).index() * swiper.params.slidesPerGroup;\n          if (swiper.params.loop) { index += swiper.loopedSlides; }\n          swiper.slideTo(index);\n        });\n      }\n\n      Utils.extend(swiper.pagination, {\n        $el: $el,\n        el: $el[0],\n      });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var $el = swiper.pagination.$el;\n\n      $el.removeClass(params.hiddenClass);\n      $el.removeClass(params.modifierClass + params.type);\n      if (swiper.pagination.bullets) { swiper.pagination.bullets.removeClass(params.bulletActiveClass); }\n      if (params.clickable) {\n        $el.off('click', (\".\" + (params.bulletClass)));\n      }\n    },\n  };\n\n  var Pagination$1 = {\n    name: 'pagination',\n    params: {\n      pagination: {\n        el: null,\n        bulletElement: 'span',\n        clickable: false,\n        hideOnClick: false,\n        renderBullet: null,\n        renderProgressbar: null,\n        renderFraction: null,\n        renderCustom: null,\n        progressbarOpposite: false,\n        type: 'bullets', // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n        dynamicBullets: false,\n        dynamicMainBullets: 1,\n        formatFractionCurrent: function (number) { return number; },\n        formatFractionTotal: function (number) { return number; },\n        bulletClass: 'swiper-pagination-bullet',\n        bulletActiveClass: 'swiper-pagination-bullet-active',\n        modifierClass: 'swiper-pagination-', // NEW\n        currentClass: 'swiper-pagination-current',\n        totalClass: 'swiper-pagination-total',\n        hiddenClass: 'swiper-pagination-hidden',\n        progressbarFillClass: 'swiper-pagination-progressbar-fill',\n        progressbarOppositeClass: 'swiper-pagination-progressbar-opposite',\n        clickableClass: 'swiper-pagination-clickable', // NEW\n        lockClass: 'swiper-pagination-lock',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        pagination: {\n          init: Pagination.init.bind(swiper),\n          render: Pagination.render.bind(swiper),\n          update: Pagination.update.bind(swiper),\n          destroy: Pagination.destroy.bind(swiper),\n          dynamicBulletIndex: 0,\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.pagination.init();\n        swiper.pagination.render();\n        swiper.pagination.update();\n      },\n      activeIndexChange: function activeIndexChange() {\n        var swiper = this;\n        if (swiper.params.loop) {\n          swiper.pagination.update();\n        } else if (typeof swiper.snapIndex === 'undefined') {\n          swiper.pagination.update();\n        }\n      },\n      snapIndexChange: function snapIndexChange() {\n        var swiper = this;\n        if (!swiper.params.loop) {\n          swiper.pagination.update();\n        }\n      },\n      slidesLengthChange: function slidesLengthChange() {\n        var swiper = this;\n        if (swiper.params.loop) {\n          swiper.pagination.render();\n          swiper.pagination.update();\n        }\n      },\n      snapGridLengthChange: function snapGridLengthChange() {\n        var swiper = this;\n        if (!swiper.params.loop) {\n          swiper.pagination.render();\n          swiper.pagination.update();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.pagination.destroy();\n      },\n      click: function click(e) {\n        var swiper = this;\n        if (\n          swiper.params.pagination.el\n          && swiper.params.pagination.hideOnClick\n          && swiper.pagination.$el.length > 0\n          && !$(e.target).hasClass(swiper.params.pagination.bulletClass)\n        ) {\n          swiper.pagination.$el.toggleClass(swiper.params.pagination.hiddenClass);\n        }\n      },\n    },\n  };\n\n  var Scrollbar = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var rtl = swiper.rtlTranslate;\n      var progress = swiper.progress;\n      var dragSize = scrollbar.dragSize;\n      var trackSize = scrollbar.trackSize;\n      var $dragEl = scrollbar.$dragEl;\n      var $el = scrollbar.$el;\n      var params = swiper.params.scrollbar;\n\n      var newSize = dragSize;\n      var newPos = (trackSize - dragSize) * progress;\n      if (rtl) {\n        newPos = -newPos;\n        if (newPos > 0) {\n          newSize = dragSize - newPos;\n          newPos = 0;\n        } else if (-newPos + dragSize > trackSize) {\n          newSize = trackSize + newPos;\n        }\n      } else if (newPos < 0) {\n        newSize = dragSize + newPos;\n        newPos = 0;\n      } else if (newPos + dragSize > trackSize) {\n        newSize = trackSize - newPos;\n      }\n      if (swiper.isHorizontal()) {\n        if (Support.transforms3d) {\n          $dragEl.transform((\"translate3d(\" + newPos + \"px, 0, 0)\"));\n        } else {\n          $dragEl.transform((\"translateX(\" + newPos + \"px)\"));\n        }\n        $dragEl[0].style.width = newSize + \"px\";\n      } else {\n        if (Support.transforms3d) {\n          $dragEl.transform((\"translate3d(0px, \" + newPos + \"px, 0)\"));\n        } else {\n          $dragEl.transform((\"translateY(\" + newPos + \"px)\"));\n        }\n        $dragEl[0].style.height = newSize + \"px\";\n      }\n      if (params.hide) {\n        clearTimeout(swiper.scrollbar.timeout);\n        $el[0].style.opacity = 1;\n        swiper.scrollbar.timeout = setTimeout(function () {\n          $el[0].style.opacity = 0;\n          $el.transition(400);\n        }, 1000);\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n      swiper.scrollbar.$dragEl.transition(duration);\n    },\n    updateSize: function updateSize() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n\n      var scrollbar = swiper.scrollbar;\n      var $dragEl = scrollbar.$dragEl;\n      var $el = scrollbar.$el;\n\n      $dragEl[0].style.width = '';\n      $dragEl[0].style.height = '';\n      var trackSize = swiper.isHorizontal() ? $el[0].offsetWidth : $el[0].offsetHeight;\n\n      var divider = swiper.size / swiper.virtualSize;\n      var moveDivider = divider * (trackSize / swiper.size);\n      var dragSize;\n      if (swiper.params.scrollbar.dragSize === 'auto') {\n        dragSize = trackSize * divider;\n      } else {\n        dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n      }\n\n      if (swiper.isHorizontal()) {\n        $dragEl[0].style.width = dragSize + \"px\";\n      } else {\n        $dragEl[0].style.height = dragSize + \"px\";\n      }\n\n      if (divider >= 1) {\n        $el[0].style.display = 'none';\n      } else {\n        $el[0].style.display = '';\n      }\n      if (swiper.params.scrollbarHide) {\n        $el[0].style.opacity = 0;\n      }\n      Utils.extend(scrollbar, {\n        trackSize: trackSize,\n        divider: divider,\n        moveDivider: moveDivider,\n        dragSize: dragSize,\n      });\n      scrollbar.$el[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](swiper.params.scrollbar.lockClass);\n    },\n    setDragPosition: function setDragPosition(e) {\n      var swiper = this;\n      var scrollbar = swiper.scrollbar;\n      var rtl = swiper.rtlTranslate;\n      var $el = scrollbar.$el;\n      var dragSize = scrollbar.dragSize;\n      var trackSize = scrollbar.trackSize;\n\n      var pointerPosition;\n      if (swiper.isHorizontal()) {\n        pointerPosition = ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageX : e.pageX || e.clientX);\n      } else {\n        pointerPosition = ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageY : e.pageY || e.clientY);\n      }\n      var positionRatio;\n      positionRatio = ((pointerPosition) - $el.offset()[swiper.isHorizontal() ? 'left' : 'top'] - (dragSize / 2)) / (trackSize - dragSize);\n      positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n      if (rtl) {\n        positionRatio = 1 - positionRatio;\n      }\n\n      var position = swiper.minTranslate() + ((swiper.maxTranslate() - swiper.minTranslate()) * positionRatio);\n\n      swiper.updateProgress(position);\n      swiper.setTranslate(position);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    },\n    onDragStart: function onDragStart(e) {\n      var swiper = this;\n      var params = swiper.params.scrollbar;\n      var scrollbar = swiper.scrollbar;\n      var $wrapperEl = swiper.$wrapperEl;\n      var $el = scrollbar.$el;\n      var $dragEl = scrollbar.$dragEl;\n      swiper.scrollbar.isTouched = true;\n      e.preventDefault();\n      e.stopPropagation();\n\n      $wrapperEl.transition(100);\n      $dragEl.transition(100);\n      scrollbar.setDragPosition(e);\n\n      clearTimeout(swiper.scrollbar.dragTimeout);\n\n      $el.transition(0);\n      if (params.hide) {\n        $el.css('opacity', 1);\n      }\n      swiper.emit('scrollbarDragStart', e);\n    },\n    onDragMove: function onDragMove(e) {\n      var swiper = this;\n      var scrollbar = swiper.scrollbar;\n      var $wrapperEl = swiper.$wrapperEl;\n      var $el = scrollbar.$el;\n      var $dragEl = scrollbar.$dragEl;\n\n      if (!swiper.scrollbar.isTouched) { return; }\n      if (e.preventDefault) { e.preventDefault(); }\n      else { e.returnValue = false; }\n      scrollbar.setDragPosition(e);\n      $wrapperEl.transition(0);\n      $el.transition(0);\n      $dragEl.transition(0);\n      swiper.emit('scrollbarDragMove', e);\n    },\n    onDragEnd: function onDragEnd(e) {\n      var swiper = this;\n\n      var params = swiper.params.scrollbar;\n      var scrollbar = swiper.scrollbar;\n      var $el = scrollbar.$el;\n\n      if (!swiper.scrollbar.isTouched) { return; }\n      swiper.scrollbar.isTouched = false;\n      if (params.hide) {\n        clearTimeout(swiper.scrollbar.dragTimeout);\n        swiper.scrollbar.dragTimeout = Utils.nextTick(function () {\n          $el.css('opacity', 0);\n          $el.transition(400);\n        }, 1000);\n      }\n      swiper.emit('scrollbarDragEnd', e);\n      if (params.snapOnRelease) {\n        swiper.slideToClosest();\n      }\n    },\n    enableDraggable: function enableDraggable() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var touchEvents = swiper.touchEvents;\n      var touchEventsDesktop = swiper.touchEventsDesktop;\n      var params = swiper.params;\n      var $el = scrollbar.$el;\n      var target = $el[0];\n      var activeListener = Support.passiveListener && params.passiveListeners ? { passive: false, capture: false } : false;\n      var passiveListener = Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.addEventListener(touchEventsDesktop.start, swiper.scrollbar.onDragStart, activeListener);\n        doc.addEventListener(touchEventsDesktop.move, swiper.scrollbar.onDragMove, activeListener);\n        doc.addEventListener(touchEventsDesktop.end, swiper.scrollbar.onDragEnd, passiveListener);\n      } else {\n        if (Support.touch) {\n          target.addEventListener(touchEvents.start, swiper.scrollbar.onDragStart, activeListener);\n          target.addEventListener(touchEvents.move, swiper.scrollbar.onDragMove, activeListener);\n          target.addEventListener(touchEvents.end, swiper.scrollbar.onDragEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.addEventListener('mousedown', swiper.scrollbar.onDragStart, activeListener);\n          doc.addEventListener('mousemove', swiper.scrollbar.onDragMove, activeListener);\n          doc.addEventListener('mouseup', swiper.scrollbar.onDragEnd, passiveListener);\n        }\n      }\n    },\n    disableDraggable: function disableDraggable() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var touchEvents = swiper.touchEvents;\n      var touchEventsDesktop = swiper.touchEventsDesktop;\n      var params = swiper.params;\n      var $el = scrollbar.$el;\n      var target = $el[0];\n      var activeListener = Support.passiveListener && params.passiveListeners ? { passive: false, capture: false } : false;\n      var passiveListener = Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.removeEventListener(touchEventsDesktop.start, swiper.scrollbar.onDragStart, activeListener);\n        doc.removeEventListener(touchEventsDesktop.move, swiper.scrollbar.onDragMove, activeListener);\n        doc.removeEventListener(touchEventsDesktop.end, swiper.scrollbar.onDragEnd, passiveListener);\n      } else {\n        if (Support.touch) {\n          target.removeEventListener(touchEvents.start, swiper.scrollbar.onDragStart, activeListener);\n          target.removeEventListener(touchEvents.move, swiper.scrollbar.onDragMove, activeListener);\n          target.removeEventListener(touchEvents.end, swiper.scrollbar.onDragEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.removeEventListener('mousedown', swiper.scrollbar.onDragStart, activeListener);\n          doc.removeEventListener('mousemove', swiper.scrollbar.onDragMove, activeListener);\n          doc.removeEventListener('mouseup', swiper.scrollbar.onDragEnd, passiveListener);\n        }\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var $swiperEl = swiper.$el;\n      var params = swiper.params.scrollbar;\n\n      var $el = $(params.el);\n      if (swiper.params.uniqueNavElements && typeof params.el === 'string' && $el.length > 1 && $swiperEl.find(params.el).length === 1) {\n        $el = $swiperEl.find(params.el);\n      }\n\n      var $dragEl = $el.find((\".\" + (swiper.params.scrollbar.dragClass)));\n      if ($dragEl.length === 0) {\n        $dragEl = $((\"<div class=\\\"\" + (swiper.params.scrollbar.dragClass) + \"\\\"></div>\"));\n        $el.append($dragEl);\n      }\n\n      Utils.extend(scrollbar, {\n        $el: $el,\n        el: $el[0],\n        $dragEl: $dragEl,\n        dragEl: $dragEl[0],\n      });\n\n      if (params.draggable) {\n        scrollbar.enableDraggable();\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      swiper.scrollbar.disableDraggable();\n    },\n  };\n\n  var Scrollbar$1 = {\n    name: 'scrollbar',\n    params: {\n      scrollbar: {\n        el: null,\n        dragSize: 'auto',\n        hide: false,\n        draggable: false,\n        snapOnRelease: true,\n        lockClass: 'swiper-scrollbar-lock',\n        dragClass: 'swiper-scrollbar-drag',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        scrollbar: {\n          init: Scrollbar.init.bind(swiper),\n          destroy: Scrollbar.destroy.bind(swiper),\n          updateSize: Scrollbar.updateSize.bind(swiper),\n          setTranslate: Scrollbar.setTranslate.bind(swiper),\n          setTransition: Scrollbar.setTransition.bind(swiper),\n          enableDraggable: Scrollbar.enableDraggable.bind(swiper),\n          disableDraggable: Scrollbar.disableDraggable.bind(swiper),\n          setDragPosition: Scrollbar.setDragPosition.bind(swiper),\n          onDragStart: Scrollbar.onDragStart.bind(swiper),\n          onDragMove: Scrollbar.onDragMove.bind(swiper),\n          onDragEnd: Scrollbar.onDragEnd.bind(swiper),\n          isTouched: false,\n          timeout: null,\n          dragTimeout: null,\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.scrollbar.init();\n        swiper.scrollbar.updateSize();\n        swiper.scrollbar.setTranslate();\n      },\n      update: function update() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      resize: function resize() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        swiper.scrollbar.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        swiper.scrollbar.setTransition(duration);\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.scrollbar.destroy();\n      },\n    },\n  };\n\n  var Parallax = {\n    setTransform: function setTransform(el, progress) {\n      var swiper = this;\n      var rtl = swiper.rtl;\n\n      var $el = $(el);\n      var rtlFactor = rtl ? -1 : 1;\n\n      var p = $el.attr('data-swiper-parallax') || '0';\n      var x = $el.attr('data-swiper-parallax-x');\n      var y = $el.attr('data-swiper-parallax-y');\n      var scale = $el.attr('data-swiper-parallax-scale');\n      var opacity = $el.attr('data-swiper-parallax-opacity');\n\n      if (x || y) {\n        x = x || '0';\n        y = y || '0';\n      } else if (swiper.isHorizontal()) {\n        x = p;\n        y = '0';\n      } else {\n        y = p;\n        x = '0';\n      }\n\n      if ((x).indexOf('%') >= 0) {\n        x = (parseInt(x, 10) * progress * rtlFactor) + \"%\";\n      } else {\n        x = (x * progress * rtlFactor) + \"px\";\n      }\n      if ((y).indexOf('%') >= 0) {\n        y = (parseInt(y, 10) * progress) + \"%\";\n      } else {\n        y = (y * progress) + \"px\";\n      }\n\n      if (typeof opacity !== 'undefined' && opacity !== null) {\n        var currentOpacity = opacity - ((opacity - 1) * (1 - Math.abs(progress)));\n        $el[0].style.opacity = currentOpacity;\n      }\n      if (typeof scale === 'undefined' || scale === null) {\n        $el.transform((\"translate3d(\" + x + \", \" + y + \", 0px)\"));\n      } else {\n        var currentScale = scale - ((scale - 1) * (1 - Math.abs(progress)));\n        $el.transform((\"translate3d(\" + x + \", \" + y + \", 0px) scale(\" + currentScale + \")\"));\n      }\n    },\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var $el = swiper.$el;\n      var slides = swiper.slides;\n      var progress = swiper.progress;\n      var snapGrid = swiper.snapGrid;\n      $el.children('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]')\n        .each(function (index, el) {\n          swiper.parallax.setTransform(el, progress);\n        });\n      slides.each(function (slideIndex, slideEl) {\n        var slideProgress = slideEl.progress;\n        if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n          slideProgress += Math.ceil(slideIndex / 2) - (progress * (snapGrid.length - 1));\n        }\n        slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n        $(slideEl).find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]')\n          .each(function (index, el) {\n            swiper.parallax.setTransform(el, slideProgress);\n          });\n      });\n    },\n    setTransition: function setTransition(duration) {\n      if ( duration === void 0 ) duration = this.params.speed;\n\n      var swiper = this;\n      var $el = swiper.$el;\n      $el.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]')\n        .each(function (index, parallaxEl) {\n          var $parallaxEl = $(parallaxEl);\n          var parallaxDuration = parseInt($parallaxEl.attr('data-swiper-parallax-duration'), 10) || duration;\n          if (duration === 0) { parallaxDuration = 0; }\n          $parallaxEl.transition(parallaxDuration);\n        });\n    },\n  };\n\n  var Parallax$1 = {\n    name: 'parallax',\n    params: {\n      parallax: {\n        enabled: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        parallax: {\n          setTransform: Parallax.setTransform.bind(swiper),\n          setTranslate: Parallax.setTranslate.bind(swiper),\n          setTransition: Parallax.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (!swiper.params.parallax.enabled) { return; }\n        swiper.params.watchSlidesProgress = true;\n      },\n      init: function init() {\n        var swiper = this;\n        if (!swiper.params.parallax) { return; }\n        swiper.parallax.setTranslate();\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (!swiper.params.parallax) { return; }\n        swiper.parallax.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (!swiper.params.parallax) { return; }\n        swiper.parallax.setTransition(duration);\n      },\n    },\n  };\n\n  var Zoom = {\n    // Calc Scale From Multi-touches\n    getDistanceBetweenTouches: function getDistanceBetweenTouches(e) {\n      if (e.targetTouches.length < 2) { return 1; }\n      var x1 = e.targetTouches[0].pageX;\n      var y1 = e.targetTouches[0].pageY;\n      var x2 = e.targetTouches[1].pageX;\n      var y2 = e.targetTouches[1].pageY;\n      var distance = Math.sqrt((Math.pow( (x2 - x1), 2 )) + (Math.pow( (y2 - y1), 2 )));\n      return distance;\n    },\n    // Events\n    onGestureStart: function onGestureStart(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      zoom.fakeGestureTouched = false;\n      zoom.fakeGestureMoved = false;\n      if (!Support.gestures) {\n        if (e.type !== 'touchstart' || (e.type === 'touchstart' && e.targetTouches.length < 2)) {\n          return;\n        }\n        zoom.fakeGestureTouched = true;\n        gesture.scaleStart = Zoom.getDistanceBetweenTouches(e);\n      }\n      if (!gesture.$slideEl || !gesture.$slideEl.length) {\n        gesture.$slideEl = $(e.target).closest('.swiper-slide');\n        if (gesture.$slideEl.length === 0) { gesture.$slideEl = swiper.slides.eq(swiper.activeIndex); }\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n        gesture.maxRatio = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n        if (gesture.$imageWrapEl.length === 0) {\n          gesture.$imageEl = undefined;\n          return;\n        }\n      }\n      gesture.$imageEl.transition(0);\n      swiper.zoom.isScaling = true;\n    },\n    onGestureChange: function onGestureChange(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (!Support.gestures) {\n        if (e.type !== 'touchmove' || (e.type === 'touchmove' && e.targetTouches.length < 2)) {\n          return;\n        }\n        zoom.fakeGestureMoved = true;\n        gesture.scaleMove = Zoom.getDistanceBetweenTouches(e);\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (Support.gestures) {\n        swiper.zoom.scale = e.scale * zoom.currentScale;\n      } else {\n        zoom.scale = (gesture.scaleMove / gesture.scaleStart) * zoom.currentScale;\n      }\n      if (zoom.scale > gesture.maxRatio) {\n        zoom.scale = (gesture.maxRatio - 1) + (Math.pow( ((zoom.scale - gesture.maxRatio) + 1), 0.5 ));\n      }\n      if (zoom.scale < params.minRatio) {\n        zoom.scale = (params.minRatio + 1) - (Math.pow( ((params.minRatio - zoom.scale) + 1), 0.5 ));\n      }\n      gesture.$imageEl.transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n    },\n    onGestureEnd: function onGestureEnd(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (!Support.gestures) {\n        if (!zoom.fakeGestureTouched || !zoom.fakeGestureMoved) {\n          return;\n        }\n        if (e.type !== 'touchend' || (e.type === 'touchend' && e.changedTouches.length < 2 && !Device.android)) {\n          return;\n        }\n        zoom.fakeGestureTouched = false;\n        zoom.fakeGestureMoved = false;\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n      gesture.$imageEl.transition(swiper.params.speed).transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n      zoom.currentScale = zoom.scale;\n      zoom.isScaling = false;\n      if (zoom.scale === 1) { gesture.$slideEl = undefined; }\n    },\n    onTouchStart: function onTouchStart(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (image.isTouched) { return; }\n      if (Device.android) { e.preventDefault(); }\n      image.isTouched = true;\n      image.touchesStart.x = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n      image.touchesStart.y = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n    },\n    onTouchMove: function onTouchMove(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      var velocity = zoom.velocity;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      swiper.allowClick = false;\n      if (!image.isTouched || !gesture.$slideEl) { return; }\n\n      if (!image.isMoved) {\n        image.width = gesture.$imageEl[0].offsetWidth;\n        image.height = gesture.$imageEl[0].offsetHeight;\n        image.startX = Utils.getTranslate(gesture.$imageWrapEl[0], 'x') || 0;\n        image.startY = Utils.getTranslate(gesture.$imageWrapEl[0], 'y') || 0;\n        gesture.slideWidth = gesture.$slideEl[0].offsetWidth;\n        gesture.slideHeight = gesture.$slideEl[0].offsetHeight;\n        gesture.$imageWrapEl.transition(0);\n        if (swiper.rtl) {\n          image.startX = -image.startX;\n          image.startY = -image.startY;\n        }\n      }\n      // Define if we need image drag\n      var scaledWidth = image.width * zoom.scale;\n      var scaledHeight = image.height * zoom.scale;\n\n      if (scaledWidth < gesture.slideWidth && scaledHeight < gesture.slideHeight) { return; }\n\n      image.minX = Math.min(((gesture.slideWidth / 2) - (scaledWidth / 2)), 0);\n      image.maxX = -image.minX;\n      image.minY = Math.min(((gesture.slideHeight / 2) - (scaledHeight / 2)), 0);\n      image.maxY = -image.minY;\n\n      image.touchesCurrent.x = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n      image.touchesCurrent.y = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n\n      if (!image.isMoved && !zoom.isScaling) {\n        if (\n          swiper.isHorizontal()\n          && (\n            (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x)\n            || (Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)\n          )\n        ) {\n          image.isTouched = false;\n          return;\n        } if (\n          !swiper.isHorizontal()\n          && (\n            (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y)\n            || (Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)\n          )\n        ) {\n          image.isTouched = false;\n          return;\n        }\n      }\n      e.preventDefault();\n      e.stopPropagation();\n\n      image.isMoved = true;\n      image.currentX = (image.touchesCurrent.x - image.touchesStart.x) + image.startX;\n      image.currentY = (image.touchesCurrent.y - image.touchesStart.y) + image.startY;\n\n      if (image.currentX < image.minX) {\n        image.currentX = (image.minX + 1) - (Math.pow( ((image.minX - image.currentX) + 1), 0.8 ));\n      }\n      if (image.currentX > image.maxX) {\n        image.currentX = (image.maxX - 1) + (Math.pow( ((image.currentX - image.maxX) + 1), 0.8 ));\n      }\n\n      if (image.currentY < image.minY) {\n        image.currentY = (image.minY + 1) - (Math.pow( ((image.minY - image.currentY) + 1), 0.8 ));\n      }\n      if (image.currentY > image.maxY) {\n        image.currentY = (image.maxY - 1) + (Math.pow( ((image.currentY - image.maxY) + 1), 0.8 ));\n      }\n\n      // Velocity\n      if (!velocity.prevPositionX) { velocity.prevPositionX = image.touchesCurrent.x; }\n      if (!velocity.prevPositionY) { velocity.prevPositionY = image.touchesCurrent.y; }\n      if (!velocity.prevTime) { velocity.prevTime = Date.now(); }\n      velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n      velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n      if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) { velocity.x = 0; }\n      if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) { velocity.y = 0; }\n      velocity.prevPositionX = image.touchesCurrent.x;\n      velocity.prevPositionY = image.touchesCurrent.y;\n      velocity.prevTime = Date.now();\n\n      gesture.$imageWrapEl.transform((\"translate3d(\" + (image.currentX) + \"px, \" + (image.currentY) + \"px,0)\"));\n    },\n    onTouchEnd: function onTouchEnd() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      var velocity = zoom.velocity;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (!image.isTouched || !image.isMoved) {\n        image.isTouched = false;\n        image.isMoved = false;\n        return;\n      }\n      image.isTouched = false;\n      image.isMoved = false;\n      var momentumDurationX = 300;\n      var momentumDurationY = 300;\n      var momentumDistanceX = velocity.x * momentumDurationX;\n      var newPositionX = image.currentX + momentumDistanceX;\n      var momentumDistanceY = velocity.y * momentumDurationY;\n      var newPositionY = image.currentY + momentumDistanceY;\n\n      // Fix duration\n      if (velocity.x !== 0) { momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x); }\n      if (velocity.y !== 0) { momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y); }\n      var momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n\n      image.currentX = newPositionX;\n      image.currentY = newPositionY;\n\n      // Define if we need image drag\n      var scaledWidth = image.width * zoom.scale;\n      var scaledHeight = image.height * zoom.scale;\n      image.minX = Math.min(((gesture.slideWidth / 2) - (scaledWidth / 2)), 0);\n      image.maxX = -image.minX;\n      image.minY = Math.min(((gesture.slideHeight / 2) - (scaledHeight / 2)), 0);\n      image.maxY = -image.minY;\n      image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n      image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n\n      gesture.$imageWrapEl.transition(momentumDuration).transform((\"translate3d(\" + (image.currentX) + \"px, \" + (image.currentY) + \"px,0)\"));\n    },\n    onTransitionEnd: function onTransitionEnd() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (gesture.$slideEl && swiper.previousIndex !== swiper.activeIndex) {\n        gesture.$imageEl.transform('translate3d(0,0,0) scale(1)');\n        gesture.$imageWrapEl.transform('translate3d(0,0,0)');\n        gesture.$slideEl = undefined;\n        gesture.$imageEl = undefined;\n        gesture.$imageWrapEl = undefined;\n\n        zoom.scale = 1;\n        zoom.currentScale = 1;\n      }\n    },\n    // Toggle Zoom\n    toggle: function toggle(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n\n      if (zoom.scale && zoom.scale !== 1) {\n        // Zoom Out\n        zoom.out();\n      } else {\n        // Zoom In\n        zoom.in(e);\n      }\n    },\n    in: function in$1(e) {\n      var swiper = this;\n\n      var zoom = swiper.zoom;\n      var params = swiper.params.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n\n      if (!gesture.$slideEl) {\n        gesture.$slideEl = swiper.clickedSlide ? $(swiper.clickedSlide) : swiper.slides.eq(swiper.activeIndex);\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n\n      gesture.$slideEl.addClass((\"\" + (params.zoomedSlideClass)));\n\n      var touchX;\n      var touchY;\n      var offsetX;\n      var offsetY;\n      var diffX;\n      var diffY;\n      var translateX;\n      var translateY;\n      var imageWidth;\n      var imageHeight;\n      var scaledWidth;\n      var scaledHeight;\n      var translateMinX;\n      var translateMinY;\n      var translateMaxX;\n      var translateMaxY;\n      var slideWidth;\n      var slideHeight;\n\n      if (typeof image.touchesStart.x === 'undefined' && e) {\n        touchX = e.type === 'touchend' ? e.changedTouches[0].pageX : e.pageX;\n        touchY = e.type === 'touchend' ? e.changedTouches[0].pageY : e.pageY;\n      } else {\n        touchX = image.touchesStart.x;\n        touchY = image.touchesStart.y;\n      }\n\n      zoom.scale = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n      zoom.currentScale = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n      if (e) {\n        slideWidth = gesture.$slideEl[0].offsetWidth;\n        slideHeight = gesture.$slideEl[0].offsetHeight;\n        offsetX = gesture.$slideEl.offset().left;\n        offsetY = gesture.$slideEl.offset().top;\n        diffX = (offsetX + (slideWidth / 2)) - touchX;\n        diffY = (offsetY + (slideHeight / 2)) - touchY;\n\n        imageWidth = gesture.$imageEl[0].offsetWidth;\n        imageHeight = gesture.$imageEl[0].offsetHeight;\n        scaledWidth = imageWidth * zoom.scale;\n        scaledHeight = imageHeight * zoom.scale;\n\n        translateMinX = Math.min(((slideWidth / 2) - (scaledWidth / 2)), 0);\n        translateMinY = Math.min(((slideHeight / 2) - (scaledHeight / 2)), 0);\n        translateMaxX = -translateMinX;\n        translateMaxY = -translateMinY;\n\n        translateX = diffX * zoom.scale;\n        translateY = diffY * zoom.scale;\n\n        if (translateX < translateMinX) {\n          translateX = translateMinX;\n        }\n        if (translateX > translateMaxX) {\n          translateX = translateMaxX;\n        }\n\n        if (translateY < translateMinY) {\n          translateY = translateMinY;\n        }\n        if (translateY > translateMaxY) {\n          translateY = translateMaxY;\n        }\n      } else {\n        translateX = 0;\n        translateY = 0;\n      }\n      gesture.$imageWrapEl.transition(300).transform((\"translate3d(\" + translateX + \"px, \" + translateY + \"px,0)\"));\n      gesture.$imageEl.transition(300).transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n    },\n    out: function out() {\n      var swiper = this;\n\n      var zoom = swiper.zoom;\n      var params = swiper.params.zoom;\n      var gesture = zoom.gesture;\n\n      if (!gesture.$slideEl) {\n        gesture.$slideEl = swiper.clickedSlide ? $(swiper.clickedSlide) : swiper.slides.eq(swiper.activeIndex);\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n\n      zoom.scale = 1;\n      zoom.currentScale = 1;\n      gesture.$imageWrapEl.transition(300).transform('translate3d(0,0,0)');\n      gesture.$imageEl.transition(300).transform('translate3d(0,0,0) scale(1)');\n      gesture.$slideEl.removeClass((\"\" + (params.zoomedSlideClass)));\n      gesture.$slideEl = undefined;\n    },\n    // Attach/Detach Events\n    enable: function enable() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      if (zoom.enabled) { return; }\n      zoom.enabled = true;\n\n      var passiveListener = swiper.touchEvents.start === 'touchstart' && Support.passiveListener && swiper.params.passiveListeners ? { passive: true, capture: false } : false;\n\n      // Scale image\n      if (Support.gestures) {\n        swiper.$wrapperEl.on('gesturestart', '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.on('gesturechange', '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.on('gestureend', '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      } else if (swiper.touchEvents.start === 'touchstart') {\n        swiper.$wrapperEl.on(swiper.touchEvents.start, '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.on(swiper.touchEvents.move, '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.on(swiper.touchEvents.end, '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      }\n\n      // Move image\n      swiper.$wrapperEl.on(swiper.touchEvents.move, (\".\" + (swiper.params.zoom.containerClass)), zoom.onTouchMove);\n    },\n    disable: function disable() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      if (!zoom.enabled) { return; }\n\n      swiper.zoom.enabled = false;\n\n      var passiveListener = swiper.touchEvents.start === 'touchstart' && Support.passiveListener && swiper.params.passiveListeners ? { passive: true, capture: false } : false;\n\n      // Scale image\n      if (Support.gestures) {\n        swiper.$wrapperEl.off('gesturestart', '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.off('gesturechange', '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.off('gestureend', '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      } else if (swiper.touchEvents.start === 'touchstart') {\n        swiper.$wrapperEl.off(swiper.touchEvents.start, '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.off(swiper.touchEvents.move, '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.off(swiper.touchEvents.end, '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      }\n\n      // Move image\n      swiper.$wrapperEl.off(swiper.touchEvents.move, (\".\" + (swiper.params.zoom.containerClass)), zoom.onTouchMove);\n    },\n  };\n\n  var Zoom$1 = {\n    name: 'zoom',\n    params: {\n      zoom: {\n        enabled: false,\n        maxRatio: 3,\n        minRatio: 1,\n        toggle: true,\n        containerClass: 'swiper-zoom-container',\n        zoomedSlideClass: 'swiper-slide-zoomed',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      var zoom = {\n        enabled: false,\n        scale: 1,\n        currentScale: 1,\n        isScaling: false,\n        gesture: {\n          $slideEl: undefined,\n          slideWidth: undefined,\n          slideHeight: undefined,\n          $imageEl: undefined,\n          $imageWrapEl: undefined,\n          maxRatio: 3,\n        },\n        image: {\n          isTouched: undefined,\n          isMoved: undefined,\n          currentX: undefined,\n          currentY: undefined,\n          minX: undefined,\n          minY: undefined,\n          maxX: undefined,\n          maxY: undefined,\n          width: undefined,\n          height: undefined,\n          startX: undefined,\n          startY: undefined,\n          touchesStart: {},\n          touchesCurrent: {},\n        },\n        velocity: {\n          x: undefined,\n          y: undefined,\n          prevPositionX: undefined,\n          prevPositionY: undefined,\n          prevTime: undefined,\n        },\n      };\n      ('onGestureStart onGestureChange onGestureEnd onTouchStart onTouchMove onTouchEnd onTransitionEnd toggle enable disable in out').split(' ').forEach(function (methodName) {\n        zoom[methodName] = Zoom[methodName].bind(swiper);\n      });\n      Utils.extend(swiper, {\n        zoom: zoom,\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.zoom.enabled) {\n          swiper.zoom.enable();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.zoom.disable();\n      },\n      touchStart: function touchStart(e) {\n        var swiper = this;\n        if (!swiper.zoom.enabled) { return; }\n        swiper.zoom.onTouchStart(e);\n      },\n      touchEnd: function touchEnd(e) {\n        var swiper = this;\n        if (!swiper.zoom.enabled) { return; }\n        swiper.zoom.onTouchEnd(e);\n      },\n      doubleTap: function doubleTap(e) {\n        var swiper = this;\n        if (swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n          swiper.zoom.toggle(e);\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n          swiper.zoom.onTransitionEnd();\n        }\n      },\n    },\n  };\n\n  var Lazy = {\n    loadInSlide: function loadInSlide(index, loadInDuplicate) {\n      if ( loadInDuplicate === void 0 ) loadInDuplicate = true;\n\n      var swiper = this;\n      var params = swiper.params.lazy;\n      if (typeof index === 'undefined') { return; }\n      if (swiper.slides.length === 0) { return; }\n      var isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n      var $slideEl = isVirtual\n        ? swiper.$wrapperEl.children((\".\" + (swiper.params.slideClass) + \"[data-swiper-slide-index=\\\"\" + index + \"\\\"]\"))\n        : swiper.slides.eq(index);\n\n      var $images = $slideEl.find((\".\" + (params.elementClass) + \":not(.\" + (params.loadedClass) + \"):not(.\" + (params.loadingClass) + \")\"));\n      if ($slideEl.hasClass(params.elementClass) && !$slideEl.hasClass(params.loadedClass) && !$slideEl.hasClass(params.loadingClass)) {\n        $images = $images.add($slideEl[0]);\n      }\n      if ($images.length === 0) { return; }\n\n      $images.each(function (imageIndex, imageEl) {\n        var $imageEl = $(imageEl);\n        $imageEl.addClass(params.loadingClass);\n\n        var background = $imageEl.attr('data-background');\n        var src = $imageEl.attr('data-src');\n        var srcset = $imageEl.attr('data-srcset');\n        var sizes = $imageEl.attr('data-sizes');\n\n        swiper.loadImage($imageEl[0], (src || background), srcset, sizes, false, function () {\n          if (typeof swiper === 'undefined' || swiper === null || !swiper || (swiper && !swiper.params) || swiper.destroyed) { return; }\n          if (background) {\n            $imageEl.css('background-image', (\"url(\\\"\" + background + \"\\\")\"));\n            $imageEl.removeAttr('data-background');\n          } else {\n            if (srcset) {\n              $imageEl.attr('srcset', srcset);\n              $imageEl.removeAttr('data-srcset');\n            }\n            if (sizes) {\n              $imageEl.attr('sizes', sizes);\n              $imageEl.removeAttr('data-sizes');\n            }\n            if (src) {\n              $imageEl.attr('src', src);\n              $imageEl.removeAttr('data-src');\n            }\n          }\n\n          $imageEl.addClass(params.loadedClass).removeClass(params.loadingClass);\n          $slideEl.find((\".\" + (params.preloaderClass))).remove();\n          if (swiper.params.loop && loadInDuplicate) {\n            var slideOriginalIndex = $slideEl.attr('data-swiper-slide-index');\n            if ($slideEl.hasClass(swiper.params.slideDuplicateClass)) {\n              var originalSlide = swiper.$wrapperEl.children((\"[data-swiper-slide-index=\\\"\" + slideOriginalIndex + \"\\\"]:not(.\" + (swiper.params.slideDuplicateClass) + \")\"));\n              swiper.lazy.loadInSlide(originalSlide.index(), false);\n            } else {\n              var duplicatedSlide = swiper.$wrapperEl.children((\".\" + (swiper.params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + slideOriginalIndex + \"\\\"]\"));\n              swiper.lazy.loadInSlide(duplicatedSlide.index(), false);\n            }\n          }\n          swiper.emit('lazyImageReady', $slideEl[0], $imageEl[0]);\n        });\n\n        swiper.emit('lazyImageLoad', $slideEl[0], $imageEl[0]);\n      });\n    },\n    load: function load() {\n      var swiper = this;\n      var $wrapperEl = swiper.$wrapperEl;\n      var swiperParams = swiper.params;\n      var slides = swiper.slides;\n      var activeIndex = swiper.activeIndex;\n      var isVirtual = swiper.virtual && swiperParams.virtual.enabled;\n      var params = swiperParams.lazy;\n\n      var slidesPerView = swiperParams.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = 0;\n      }\n\n      function slideExist(index) {\n        if (isVirtual) {\n          if ($wrapperEl.children((\".\" + (swiperParams.slideClass) + \"[data-swiper-slide-index=\\\"\" + index + \"\\\"]\")).length) {\n            return true;\n          }\n        } else if (slides[index]) { return true; }\n        return false;\n      }\n      function slideIndex(slideEl) {\n        if (isVirtual) {\n          return $(slideEl).attr('data-swiper-slide-index');\n        }\n        return $(slideEl).index();\n      }\n\n      if (!swiper.lazy.initialImageLoaded) { swiper.lazy.initialImageLoaded = true; }\n      if (swiper.params.watchSlidesVisibility) {\n        $wrapperEl.children((\".\" + (swiperParams.slideVisibleClass))).each(function (elIndex, slideEl) {\n          var index = isVirtual ? $(slideEl).attr('data-swiper-slide-index') : $(slideEl).index();\n          swiper.lazy.loadInSlide(index);\n        });\n      } else if (slidesPerView > 1) {\n        for (var i = activeIndex; i < activeIndex + slidesPerView; i += 1) {\n          if (slideExist(i)) { swiper.lazy.loadInSlide(i); }\n        }\n      } else {\n        swiper.lazy.loadInSlide(activeIndex);\n      }\n      if (params.loadPrevNext) {\n        if (slidesPerView > 1 || (params.loadPrevNextAmount && params.loadPrevNextAmount > 1)) {\n          var amount = params.loadPrevNextAmount;\n          var spv = slidesPerView;\n          var maxIndex = Math.min(activeIndex + spv + Math.max(amount, spv), slides.length);\n          var minIndex = Math.max(activeIndex - Math.max(spv, amount), 0);\n          // Next Slides\n          for (var i$1 = activeIndex + slidesPerView; i$1 < maxIndex; i$1 += 1) {\n            if (slideExist(i$1)) { swiper.lazy.loadInSlide(i$1); }\n          }\n          // Prev Slides\n          for (var i$2 = minIndex; i$2 < activeIndex; i$2 += 1) {\n            if (slideExist(i$2)) { swiper.lazy.loadInSlide(i$2); }\n          }\n        } else {\n          var nextSlide = $wrapperEl.children((\".\" + (swiperParams.slideNextClass)));\n          if (nextSlide.length > 0) { swiper.lazy.loadInSlide(slideIndex(nextSlide)); }\n\n          var prevSlide = $wrapperEl.children((\".\" + (swiperParams.slidePrevClass)));\n          if (prevSlide.length > 0) { swiper.lazy.loadInSlide(slideIndex(prevSlide)); }\n        }\n      }\n    },\n  };\n\n  var Lazy$1 = {\n    name: 'lazy',\n    params: {\n      lazy: {\n        enabled: false,\n        loadPrevNext: false,\n        loadPrevNextAmount: 1,\n        loadOnTransitionStart: false,\n\n        elementClass: 'swiper-lazy',\n        loadingClass: 'swiper-lazy-loading',\n        loadedClass: 'swiper-lazy-loaded',\n        preloaderClass: 'swiper-lazy-preloader',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        lazy: {\n          initialImageLoaded: false,\n          load: Lazy.load.bind(swiper),\n          loadInSlide: Lazy.loadInSlide.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && swiper.params.preloadImages) {\n          swiper.params.preloadImages = false;\n        }\n      },\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && !swiper.params.loop && swiper.params.initialSlide === 0) {\n          swiper.lazy.load();\n        }\n      },\n      scroll: function scroll() {\n        var swiper = this;\n        if (swiper.params.freeMode && !swiper.params.freeModeSticky) {\n          swiper.lazy.load();\n        }\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      },\n      scrollbarDragMove: function scrollbarDragMove() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      },\n      transitionStart: function transitionStart() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          if (swiper.params.lazy.loadOnTransitionStart || (!swiper.params.lazy.loadOnTransitionStart && !swiper.lazy.initialImageLoaded)) {\n            swiper.lazy.load();\n          }\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && !swiper.params.lazy.loadOnTransitionStart) {\n          swiper.lazy.load();\n        }\n      },\n    },\n  };\n\n  /* eslint no-bitwise: [\"error\", { \"allow\": [\">>\"] }] */\n\n  var Controller = {\n    LinearSpline: function LinearSpline(x, y) {\n      var binarySearch = (function search() {\n        var maxIndex;\n        var minIndex;\n        var guess;\n        return function (array, val) {\n          minIndex = -1;\n          maxIndex = array.length;\n          while (maxIndex - minIndex > 1) {\n            guess = maxIndex + minIndex >> 1;\n            if (array[guess] <= val) {\n              minIndex = guess;\n            } else {\n              maxIndex = guess;\n            }\n          }\n          return maxIndex;\n        };\n      }());\n      this.x = x;\n      this.y = y;\n      this.lastIndex = x.length - 1;\n      // Given an x value (x2), return the expected y2 value:\n      // (x1,y1) is the known point before given value,\n      // (x3,y3) is the known point after given value.\n      var i1;\n      var i3;\n\n      this.interpolate = function interpolate(x2) {\n        if (!x2) { return 0; }\n\n        // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n        i3 = binarySearch(this.x, x2);\n        i1 = i3 - 1;\n\n        // We have our indexes i1 & i3, so we can calculate already:\n        // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n        return (((x2 - this.x[i1]) * (this.y[i3] - this.y[i1])) / (this.x[i3] - this.x[i1])) + this.y[i1];\n      };\n      return this;\n    },\n    // xxx: for now i will just save one spline function to to\n    getInterpolateFunction: function getInterpolateFunction(c) {\n      var swiper = this;\n      if (!swiper.controller.spline) {\n        swiper.controller.spline = swiper.params.loop\n          ? new Controller.LinearSpline(swiper.slidesGrid, c.slidesGrid)\n          : new Controller.LinearSpline(swiper.snapGrid, c.snapGrid);\n      }\n    },\n    setTranslate: function setTranslate(setTranslate$1, byController) {\n      var swiper = this;\n      var controlled = swiper.controller.control;\n      var multiplier;\n      var controlledTranslate;\n      function setControlledTranslate(c) {\n        // this will create an Interpolate function based on the snapGrids\n        // x is the Grid of the scrolled scroller and y will be the controlled scroller\n        // it makes sense to create this only once and recall it for the interpolation\n        // the function does a lot of value caching for performance\n        var translate = swiper.rtlTranslate ? -swiper.translate : swiper.translate;\n        if (swiper.params.controller.by === 'slide') {\n          swiper.controller.getInterpolateFunction(c);\n          // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n          // but it did not work out\n          controlledTranslate = -swiper.controller.spline.interpolate(-translate);\n        }\n\n        if (!controlledTranslate || swiper.params.controller.by === 'container') {\n          multiplier = (c.maxTranslate() - c.minTranslate()) / (swiper.maxTranslate() - swiper.minTranslate());\n          controlledTranslate = ((translate - swiper.minTranslate()) * multiplier) + c.minTranslate();\n        }\n\n        if (swiper.params.controller.inverse) {\n          controlledTranslate = c.maxTranslate() - controlledTranslate;\n        }\n        c.updateProgress(controlledTranslate);\n        c.setTranslate(controlledTranslate, swiper);\n        c.updateActiveIndex();\n        c.updateSlidesClasses();\n      }\n      if (Array.isArray(controlled)) {\n        for (var i = 0; i < controlled.length; i += 1) {\n          if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n            setControlledTranslate(controlled[i]);\n          }\n        }\n      } else if (controlled instanceof Swiper && byController !== controlled) {\n        setControlledTranslate(controlled);\n      }\n    },\n    setTransition: function setTransition(duration, byController) {\n      var swiper = this;\n      var controlled = swiper.controller.control;\n      var i;\n      function setControlledTransition(c) {\n        c.setTransition(duration, swiper);\n        if (duration !== 0) {\n          c.transitionStart();\n          if (c.params.autoHeight) {\n            Utils.nextTick(function () {\n              c.updateAutoHeight();\n            });\n          }\n          c.$wrapperEl.transitionEnd(function () {\n            if (!controlled) { return; }\n            if (c.params.loop && swiper.params.controller.by === 'slide') {\n              c.loopFix();\n            }\n            c.transitionEnd();\n          });\n        }\n      }\n      if (Array.isArray(controlled)) {\n        for (i = 0; i < controlled.length; i += 1) {\n          if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n            setControlledTransition(controlled[i]);\n          }\n        }\n      } else if (controlled instanceof Swiper && byController !== controlled) {\n        setControlledTransition(controlled);\n      }\n    },\n  };\n  var Controller$1 = {\n    name: 'controller',\n    params: {\n      controller: {\n        control: undefined,\n        inverse: false,\n        by: 'slide', // or 'container'\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        controller: {\n          control: swiper.params.controller.control,\n          getInterpolateFunction: Controller.getInterpolateFunction.bind(swiper),\n          setTranslate: Controller.setTranslate.bind(swiper),\n          setTransition: Controller.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      update: function update() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      setTranslate: function setTranslate(translate, byController) {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        swiper.controller.setTranslate(translate, byController);\n      },\n      setTransition: function setTransition(duration, byController) {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        swiper.controller.setTransition(duration, byController);\n      },\n    },\n  };\n\n  var a11y = {\n    makeElFocusable: function makeElFocusable($el) {\n      $el.attr('tabIndex', '0');\n      return $el;\n    },\n    addElRole: function addElRole($el, role) {\n      $el.attr('role', role);\n      return $el;\n    },\n    addElLabel: function addElLabel($el, label) {\n      $el.attr('aria-label', label);\n      return $el;\n    },\n    disableEl: function disableEl($el) {\n      $el.attr('aria-disabled', true);\n      return $el;\n    },\n    enableEl: function enableEl($el) {\n      $el.attr('aria-disabled', false);\n      return $el;\n    },\n    onEnterKey: function onEnterKey(e) {\n      var swiper = this;\n      var params = swiper.params.a11y;\n      if (e.keyCode !== 13) { return; }\n      var $targetEl = $(e.target);\n      if (swiper.navigation && swiper.navigation.$nextEl && $targetEl.is(swiper.navigation.$nextEl)) {\n        if (!(swiper.isEnd && !swiper.params.loop)) {\n          swiper.slideNext();\n        }\n        if (swiper.isEnd) {\n          swiper.a11y.notify(params.lastSlideMessage);\n        } else {\n          swiper.a11y.notify(params.nextSlideMessage);\n        }\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl && $targetEl.is(swiper.navigation.$prevEl)) {\n        if (!(swiper.isBeginning && !swiper.params.loop)) {\n          swiper.slidePrev();\n        }\n        if (swiper.isBeginning) {\n          swiper.a11y.notify(params.firstSlideMessage);\n        } else {\n          swiper.a11y.notify(params.prevSlideMessage);\n        }\n      }\n      if (swiper.pagination && $targetEl.is((\".\" + (swiper.params.pagination.bulletClass)))) {\n        $targetEl[0].click();\n      }\n    },\n    notify: function notify(message) {\n      var swiper = this;\n      var notification = swiper.a11y.liveRegion;\n      if (notification.length === 0) { return; }\n      notification.html('');\n      notification.html(message);\n    },\n    updateNavigation: function updateNavigation() {\n      var swiper = this;\n\n      if (swiper.params.loop) { return; }\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n\n      if ($prevEl && $prevEl.length > 0) {\n        if (swiper.isBeginning) {\n          swiper.a11y.disableEl($prevEl);\n        } else {\n          swiper.a11y.enableEl($prevEl);\n        }\n      }\n      if ($nextEl && $nextEl.length > 0) {\n        if (swiper.isEnd) {\n          swiper.a11y.disableEl($nextEl);\n        } else {\n          swiper.a11y.enableEl($nextEl);\n        }\n      }\n    },\n    updatePagination: function updatePagination() {\n      var swiper = this;\n      var params = swiper.params.a11y;\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.bullets.each(function (bulletIndex, bulletEl) {\n          var $bulletEl = $(bulletEl);\n          swiper.a11y.makeElFocusable($bulletEl);\n          swiper.a11y.addElRole($bulletEl, 'button');\n          swiper.a11y.addElLabel($bulletEl, params.paginationBulletMessage.replace(/{{index}}/, $bulletEl.index() + 1));\n        });\n      }\n    },\n    init: function init() {\n      var swiper = this;\n\n      swiper.$el.append(swiper.a11y.liveRegion);\n\n      // Navigation\n      var params = swiper.params.a11y;\n      var $nextEl;\n      var $prevEl;\n      if (swiper.navigation && swiper.navigation.$nextEl) {\n        $nextEl = swiper.navigation.$nextEl;\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl) {\n        $prevEl = swiper.navigation.$prevEl;\n      }\n      if ($nextEl) {\n        swiper.a11y.makeElFocusable($nextEl);\n        swiper.a11y.addElRole($nextEl, 'button');\n        swiper.a11y.addElLabel($nextEl, params.nextSlideMessage);\n        $nextEl.on('keydown', swiper.a11y.onEnterKey);\n      }\n      if ($prevEl) {\n        swiper.a11y.makeElFocusable($prevEl);\n        swiper.a11y.addElRole($prevEl, 'button');\n        swiper.a11y.addElLabel($prevEl, params.prevSlideMessage);\n        $prevEl.on('keydown', swiper.a11y.onEnterKey);\n      }\n\n      // Pagination\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.$el.on('keydown', (\".\" + (swiper.params.pagination.bulletClass)), swiper.a11y.onEnterKey);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (swiper.a11y.liveRegion && swiper.a11y.liveRegion.length > 0) { swiper.a11y.liveRegion.remove(); }\n\n      var $nextEl;\n      var $prevEl;\n      if (swiper.navigation && swiper.navigation.$nextEl) {\n        $nextEl = swiper.navigation.$nextEl;\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl) {\n        $prevEl = swiper.navigation.$prevEl;\n      }\n      if ($nextEl) {\n        $nextEl.off('keydown', swiper.a11y.onEnterKey);\n      }\n      if ($prevEl) {\n        $prevEl.off('keydown', swiper.a11y.onEnterKey);\n      }\n\n      // Pagination\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.$el.off('keydown', (\".\" + (swiper.params.pagination.bulletClass)), swiper.a11y.onEnterKey);\n      }\n    },\n  };\n  var A11y = {\n    name: 'a11y',\n    params: {\n      a11y: {\n        enabled: true,\n        notificationClass: 'swiper-notification',\n        prevSlideMessage: 'Previous slide',\n        nextSlideMessage: 'Next slide',\n        firstSlideMessage: 'This is the first slide',\n        lastSlideMessage: 'This is the last slide',\n        paginationBulletMessage: 'Go to slide {{index}}',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        a11y: {\n          liveRegion: $((\"<span class=\\\"\" + (swiper.params.a11y.notificationClass) + \"\\\" aria-live=\\\"assertive\\\" aria-atomic=\\\"true\\\"></span>\")),\n        },\n      });\n      Object.keys(a11y).forEach(function (methodName) {\n        swiper.a11y[methodName] = a11y[methodName].bind(swiper);\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.init();\n        swiper.a11y.updateNavigation();\n      },\n      toEdge: function toEdge() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updateNavigation();\n      },\n      fromEdge: function fromEdge() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updateNavigation();\n      },\n      paginationUpdate: function paginationUpdate() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updatePagination();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.destroy();\n      },\n    },\n  };\n\n  var History = {\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.history) { return; }\n      if (!win.history || !win.history.pushState) {\n        swiper.params.history.enabled = false;\n        swiper.params.hashNavigation.enabled = true;\n        return;\n      }\n      var history = swiper.history;\n      history.initialized = true;\n      history.paths = History.getPathValues();\n      if (!history.paths.key && !history.paths.value) { return; }\n      history.scrollToSlide(0, history.paths.value, swiper.params.runCallbacksOnInit);\n      if (!swiper.params.history.replaceState) {\n        win.addEventListener('popstate', swiper.history.setHistoryPopState);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (!swiper.params.history.replaceState) {\n        win.removeEventListener('popstate', swiper.history.setHistoryPopState);\n      }\n    },\n    setHistoryPopState: function setHistoryPopState() {\n      var swiper = this;\n      swiper.history.paths = History.getPathValues();\n      swiper.history.scrollToSlide(swiper.params.speed, swiper.history.paths.value, false);\n    },\n    getPathValues: function getPathValues() {\n      var pathArray = win.location.pathname.slice(1).split('/').filter(function (part) { return part !== ''; });\n      var total = pathArray.length;\n      var key = pathArray[total - 2];\n      var value = pathArray[total - 1];\n      return { key: key, value: value };\n    },\n    setHistory: function setHistory(key, index) {\n      var swiper = this;\n      if (!swiper.history.initialized || !swiper.params.history.enabled) { return; }\n      var slide = swiper.slides.eq(index);\n      var value = History.slugify(slide.attr('data-history'));\n      if (!win.location.pathname.includes(key)) {\n        value = key + \"/\" + value;\n      }\n      var currentState = win.history.state;\n      if (currentState && currentState.value === value) {\n        return;\n      }\n      if (swiper.params.history.replaceState) {\n        win.history.replaceState({ value: value }, null, value);\n      } else {\n        win.history.pushState({ value: value }, null, value);\n      }\n    },\n    slugify: function slugify(text) {\n      return text.toString().toLowerCase()\n        .replace(/\\s+/g, '-')\n        .replace(/[^\\w-]+/g, '')\n        .replace(/--+/g, '-')\n        .replace(/^-+/, '')\n        .replace(/-+$/, '');\n    },\n    scrollToSlide: function scrollToSlide(speed, value, runCallbacks) {\n      var swiper = this;\n      if (value) {\n        for (var i = 0, length = swiper.slides.length; i < length; i += 1) {\n          var slide = swiper.slides.eq(i);\n          var slideHistory = History.slugify(slide.attr('data-history'));\n          if (slideHistory === value && !slide.hasClass(swiper.params.slideDuplicateClass)) {\n            var index = slide.index();\n            swiper.slideTo(index, speed, runCallbacks);\n          }\n        }\n      } else {\n        swiper.slideTo(0, speed, runCallbacks);\n      }\n    },\n  };\n\n  var History$1 = {\n    name: 'history',\n    params: {\n      history: {\n        enabled: false,\n        replaceState: false,\n        key: 'slides',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        history: {\n          init: History.init.bind(swiper),\n          setHistory: History.setHistory.bind(swiper),\n          setHistoryPopState: History.setHistoryPopState.bind(swiper),\n          scrollToSlide: History.scrollToSlide.bind(swiper),\n          destroy: History.destroy.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.history.enabled) {\n          swiper.history.init();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.params.history.enabled) {\n          swiper.history.destroy();\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.history.initialized) {\n          swiper.history.setHistory(swiper.params.history.key, swiper.activeIndex);\n        }\n      },\n    },\n  };\n\n  var HashNavigation = {\n    onHashCange: function onHashCange() {\n      var swiper = this;\n      var newHash = doc.location.hash.replace('#', '');\n      var activeSlideHash = swiper.slides.eq(swiper.activeIndex).attr('data-hash');\n      if (newHash !== activeSlideHash) {\n        swiper.slideTo(swiper.$wrapperEl.children((\".\" + (swiper.params.slideClass) + \"[data-hash=\\\"\" + newHash + \"\\\"]\")).index());\n      }\n    },\n    setHash: function setHash() {\n      var swiper = this;\n      if (!swiper.hashNavigation.initialized || !swiper.params.hashNavigation.enabled) { return; }\n      if (swiper.params.hashNavigation.replaceState && win.history && win.history.replaceState) {\n        win.history.replaceState(null, null, ((\"#\" + (swiper.slides.eq(swiper.activeIndex).attr('data-hash'))) || ''));\n      } else {\n        var slide = swiper.slides.eq(swiper.activeIndex);\n        var hash = slide.attr('data-hash') || slide.attr('data-history');\n        doc.location.hash = hash || '';\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.hashNavigation.enabled || (swiper.params.history && swiper.params.history.enabled)) { return; }\n      swiper.hashNavigation.initialized = true;\n      var hash = doc.location.hash.replace('#', '');\n      if (hash) {\n        var speed = 0;\n        for (var i = 0, length = swiper.slides.length; i < length; i += 1) {\n          var slide = swiper.slides.eq(i);\n          var slideHash = slide.attr('data-hash') || slide.attr('data-history');\n          if (slideHash === hash && !slide.hasClass(swiper.params.slideDuplicateClass)) {\n            var index = slide.index();\n            swiper.slideTo(index, speed, swiper.params.runCallbacksOnInit, true);\n          }\n        }\n      }\n      if (swiper.params.hashNavigation.watchState) {\n        $(win).on('hashchange', swiper.hashNavigation.onHashCange);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (swiper.params.hashNavigation.watchState) {\n        $(win).off('hashchange', swiper.hashNavigation.onHashCange);\n      }\n    },\n  };\n  var HashNavigation$1 = {\n    name: 'hash-navigation',\n    params: {\n      hashNavigation: {\n        enabled: false,\n        replaceState: false,\n        watchState: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        hashNavigation: {\n          initialized: false,\n          init: HashNavigation.init.bind(swiper),\n          destroy: HashNavigation.destroy.bind(swiper),\n          setHash: HashNavigation.setHash.bind(swiper),\n          onHashCange: HashNavigation.onHashCange.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.hashNavigation.enabled) {\n          swiper.hashNavigation.init();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.params.hashNavigation.enabled) {\n          swiper.hashNavigation.destroy();\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.hashNavigation.initialized) {\n          swiper.hashNavigation.setHash();\n        }\n      },\n    },\n  };\n\n  /* eslint no-underscore-dangle: \"off\" */\n\n  var Autoplay = {\n    run: function run() {\n      var swiper = this;\n      var $activeSlideEl = swiper.slides.eq(swiper.activeIndex);\n      var delay = swiper.params.autoplay.delay;\n      if ($activeSlideEl.attr('data-swiper-autoplay')) {\n        delay = $activeSlideEl.attr('data-swiper-autoplay') || swiper.params.autoplay.delay;\n      }\n      swiper.autoplay.timeout = Utils.nextTick(function () {\n        if (swiper.params.autoplay.reverseDirection) {\n          if (swiper.params.loop) {\n            swiper.loopFix();\n            swiper.slidePrev(swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else if (!swiper.isBeginning) {\n            swiper.slidePrev(swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else if (!swiper.params.autoplay.stopOnLastSlide) {\n            swiper.slideTo(swiper.slides.length - 1, swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else {\n            swiper.autoplay.stop();\n          }\n        } else if (swiper.params.loop) {\n          swiper.loopFix();\n          swiper.slideNext(swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else if (!swiper.isEnd) {\n          swiper.slideNext(swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else {\n          swiper.autoplay.stop();\n        }\n      }, delay);\n    },\n    start: function start() {\n      var swiper = this;\n      if (typeof swiper.autoplay.timeout !== 'undefined') { return false; }\n      if (swiper.autoplay.running) { return false; }\n      swiper.autoplay.running = true;\n      swiper.emit('autoplayStart');\n      swiper.autoplay.run();\n      return true;\n    },\n    stop: function stop() {\n      var swiper = this;\n      if (!swiper.autoplay.running) { return false; }\n      if (typeof swiper.autoplay.timeout === 'undefined') { return false; }\n\n      if (swiper.autoplay.timeout) {\n        clearTimeout(swiper.autoplay.timeout);\n        swiper.autoplay.timeout = undefined;\n      }\n      swiper.autoplay.running = false;\n      swiper.emit('autoplayStop');\n      return true;\n    },\n    pause: function pause(speed) {\n      var swiper = this;\n      if (!swiper.autoplay.running) { return; }\n      if (swiper.autoplay.paused) { return; }\n      if (swiper.autoplay.timeout) { clearTimeout(swiper.autoplay.timeout); }\n      swiper.autoplay.paused = true;\n      if (speed === 0 || !swiper.params.autoplay.waitForTransition) {\n        swiper.autoplay.paused = false;\n        swiper.autoplay.run();\n      } else {\n        swiper.$wrapperEl[0].addEventListener('transitionend', swiper.autoplay.onTransitionEnd);\n        swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.autoplay.onTransitionEnd);\n      }\n    },\n  };\n\n  var Autoplay$1 = {\n    name: 'autoplay',\n    params: {\n      autoplay: {\n        enabled: false,\n        delay: 3000,\n        waitForTransition: true,\n        disableOnInteraction: true,\n        stopOnLastSlide: false,\n        reverseDirection: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        autoplay: {\n          running: false,\n          paused: false,\n          run: Autoplay.run.bind(swiper),\n          start: Autoplay.start.bind(swiper),\n          stop: Autoplay.stop.bind(swiper),\n          pause: Autoplay.pause.bind(swiper),\n          onTransitionEnd: function onTransitionEnd(e) {\n            if (!swiper || swiper.destroyed || !swiper.$wrapperEl) { return; }\n            if (e.target !== this) { return; }\n            swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.autoplay.onTransitionEnd);\n            swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.autoplay.onTransitionEnd);\n            swiper.autoplay.paused = false;\n            if (!swiper.autoplay.running) {\n              swiper.autoplay.stop();\n            } else {\n              swiper.autoplay.run();\n            }\n          },\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.autoplay.enabled) {\n          swiper.autoplay.start();\n        }\n      },\n      beforeTransitionStart: function beforeTransitionStart(speed, internal) {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          if (internal || !swiper.params.autoplay.disableOnInteraction) {\n            swiper.autoplay.pause(speed);\n          } else {\n            swiper.autoplay.stop();\n          }\n        }\n      },\n      sliderFirstMove: function sliderFirstMove() {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          if (swiper.params.autoplay.disableOnInteraction) {\n            swiper.autoplay.stop();\n          } else {\n            swiper.autoplay.pause();\n          }\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          swiper.autoplay.stop();\n        }\n      },\n    },\n  };\n\n  var Fade = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var slides = swiper.slides;\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = swiper.slides.eq(i);\n        var offset = $slideEl[0].swiperSlideOffset;\n        var tx = -offset;\n        if (!swiper.params.virtualTranslate) { tx -= swiper.translate; }\n        var ty = 0;\n        if (!swiper.isHorizontal()) {\n          ty = tx;\n          tx = 0;\n        }\n        var slideOpacity = swiper.params.fadeEffect.crossFade\n          ? Math.max(1 - Math.abs($slideEl[0].progress), 0)\n          : 1 + Math.min(Math.max($slideEl[0].progress, -1), 0);\n        $slideEl\n          .css({\n            opacity: slideOpacity,\n          })\n          .transform((\"translate3d(\" + tx + \"px, \" + ty + \"px, 0px)\"));\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var slides = swiper.slides;\n      var $wrapperEl = swiper.$wrapperEl;\n      slides.transition(duration);\n      if (swiper.params.virtualTranslate && duration !== 0) {\n        var eventTriggered = false;\n        slides.transitionEnd(function () {\n          if (eventTriggered) { return; }\n          if (!swiper || swiper.destroyed) { return; }\n          eventTriggered = true;\n          swiper.animating = false;\n          var triggerEvents = ['webkitTransitionEnd', 'transitionend'];\n          for (var i = 0; i < triggerEvents.length; i += 1) {\n            $wrapperEl.trigger(triggerEvents[i]);\n          }\n        });\n      }\n    },\n  };\n\n  var EffectFade = {\n    name: 'effect-fade',\n    params: {\n      fadeEffect: {\n        crossFade: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        fadeEffect: {\n          setTranslate: Fade.setTranslate.bind(swiper),\n          setTransition: Fade.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"fade\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          spaceBetween: 0,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.fadeEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.fadeEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Cube = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var $el = swiper.$el;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slides = swiper.slides;\n      var swiperWidth = swiper.width;\n      var swiperHeight = swiper.height;\n      var rtl = swiper.rtlTranslate;\n      var swiperSize = swiper.size;\n      var params = swiper.params.cubeEffect;\n      var isHorizontal = swiper.isHorizontal();\n      var isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n      var wrapperRotate = 0;\n      var $cubeShadowEl;\n      if (params.shadow) {\n        if (isHorizontal) {\n          $cubeShadowEl = $wrapperEl.find('.swiper-cube-shadow');\n          if ($cubeShadowEl.length === 0) {\n            $cubeShadowEl = $('<div class=\"swiper-cube-shadow\"></div>');\n            $wrapperEl.append($cubeShadowEl);\n          }\n          $cubeShadowEl.css({ height: (swiperWidth + \"px\") });\n        } else {\n          $cubeShadowEl = $el.find('.swiper-cube-shadow');\n          if ($cubeShadowEl.length === 0) {\n            $cubeShadowEl = $('<div class=\"swiper-cube-shadow\"></div>');\n            $el.append($cubeShadowEl);\n          }\n        }\n      }\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var slideIndex = i;\n        if (isVirtual) {\n          slideIndex = parseInt($slideEl.attr('data-swiper-slide-index'), 10);\n        }\n        var slideAngle = slideIndex * 90;\n        var round = Math.floor(slideAngle / 360);\n        if (rtl) {\n          slideAngle = -slideAngle;\n          round = Math.floor(-slideAngle / 360);\n        }\n        var progress = Math.max(Math.min($slideEl[0].progress, 1), -1);\n        var tx = 0;\n        var ty = 0;\n        var tz = 0;\n        if (slideIndex % 4 === 0) {\n          tx = -round * 4 * swiperSize;\n          tz = 0;\n        } else if ((slideIndex - 1) % 4 === 0) {\n          tx = 0;\n          tz = -round * 4 * swiperSize;\n        } else if ((slideIndex - 2) % 4 === 0) {\n          tx = swiperSize + (round * 4 * swiperSize);\n          tz = swiperSize;\n        } else if ((slideIndex - 3) % 4 === 0) {\n          tx = -swiperSize;\n          tz = (3 * swiperSize) + (swiperSize * 4 * round);\n        }\n        if (rtl) {\n          tx = -tx;\n        }\n\n        if (!isHorizontal) {\n          ty = tx;\n          tx = 0;\n        }\n\n        var transform = \"rotateX(\" + (isHorizontal ? 0 : -slideAngle) + \"deg) rotateY(\" + (isHorizontal ? slideAngle : 0) + \"deg) translate3d(\" + tx + \"px, \" + ty + \"px, \" + tz + \"px)\";\n        if (progress <= 1 && progress > -1) {\n          wrapperRotate = (slideIndex * 90) + (progress * 90);\n          if (rtl) { wrapperRotate = (-slideIndex * 90) - (progress * 90); }\n        }\n        $slideEl.transform(transform);\n        if (params.slideShadows) {\n          // Set shadows\n          var shadowBefore = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var shadowAfter = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if (shadowBefore.length === 0) {\n            shadowBefore = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append(shadowBefore);\n          }\n          if (shadowAfter.length === 0) {\n            shadowAfter = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append(shadowAfter);\n          }\n          if (shadowBefore.length) { shadowBefore[0].style.opacity = Math.max(-progress, 0); }\n          if (shadowAfter.length) { shadowAfter[0].style.opacity = Math.max(progress, 0); }\n        }\n      }\n      $wrapperEl.css({\n        '-webkit-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        '-moz-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        '-ms-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        'transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n      });\n\n      if (params.shadow) {\n        if (isHorizontal) {\n          $cubeShadowEl.transform((\"translate3d(0px, \" + ((swiperWidth / 2) + params.shadowOffset) + \"px, \" + (-swiperWidth / 2) + \"px) rotateX(90deg) rotateZ(0deg) scale(\" + (params.shadowScale) + \")\"));\n        } else {\n          var shadowAngle = Math.abs(wrapperRotate) - (Math.floor(Math.abs(wrapperRotate) / 90) * 90);\n          var multiplier = 1.5 - (\n            (Math.sin((shadowAngle * 2 * Math.PI) / 360) / 2)\n            + (Math.cos((shadowAngle * 2 * Math.PI) / 360) / 2)\n          );\n          var scale1 = params.shadowScale;\n          var scale2 = params.shadowScale / multiplier;\n          var offset = params.shadowOffset;\n          $cubeShadowEl.transform((\"scale3d(\" + scale1 + \", 1, \" + scale2 + \") translate3d(0px, \" + ((swiperHeight / 2) + offset) + \"px, \" + (-swiperHeight / 2 / scale2) + \"px) rotateX(-90deg)\"));\n        }\n      }\n      var zFactor = (Browser.isSafari || Browser.isUiWebView) ? (-swiperSize / 2) : 0;\n      $wrapperEl\n        .transform((\"translate3d(0px,0,\" + zFactor + \"px) rotateX(\" + (swiper.isHorizontal() ? 0 : wrapperRotate) + \"deg) rotateY(\" + (swiper.isHorizontal() ? -wrapperRotate : 0) + \"deg)\"));\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var $el = swiper.$el;\n      var slides = swiper.slides;\n      slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n      if (swiper.params.cubeEffect.shadow && !swiper.isHorizontal()) {\n        $el.find('.swiper-cube-shadow').transition(duration);\n      }\n    },\n  };\n\n  var EffectCube = {\n    name: 'effect-cube',\n    params: {\n      cubeEffect: {\n        slideShadows: true,\n        shadow: true,\n        shadowOffset: 20,\n        shadowScale: 0.94,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        cubeEffect: {\n          setTranslate: Cube.setTranslate.bind(swiper),\n          setTransition: Cube.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"cube\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          resistanceRatio: 0,\n          spaceBetween: 0,\n          centeredSlides: false,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.cubeEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.cubeEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Flip = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var slides = swiper.slides;\n      var rtl = swiper.rtlTranslate;\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var progress = $slideEl[0].progress;\n        if (swiper.params.flipEffect.limitRotation) {\n          progress = Math.max(Math.min($slideEl[0].progress, 1), -1);\n        }\n        var offset = $slideEl[0].swiperSlideOffset;\n        var rotate = -180 * progress;\n        var rotateY = rotate;\n        var rotateX = 0;\n        var tx = -offset;\n        var ty = 0;\n        if (!swiper.isHorizontal()) {\n          ty = tx;\n          tx = 0;\n          rotateX = -rotateY;\n          rotateY = 0;\n        } else if (rtl) {\n          rotateY = -rotateY;\n        }\n\n        $slideEl[0].style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n\n        if (swiper.params.flipEffect.slideShadows) {\n          // Set shadows\n          var shadowBefore = swiper.isHorizontal() ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var shadowAfter = swiper.isHorizontal() ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if (shadowBefore.length === 0) {\n            shadowBefore = $((\"<div class=\\\"swiper-slide-shadow-\" + (swiper.isHorizontal() ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append(shadowBefore);\n          }\n          if (shadowAfter.length === 0) {\n            shadowAfter = $((\"<div class=\\\"swiper-slide-shadow-\" + (swiper.isHorizontal() ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append(shadowAfter);\n          }\n          if (shadowBefore.length) { shadowBefore[0].style.opacity = Math.max(-progress, 0); }\n          if (shadowAfter.length) { shadowAfter[0].style.opacity = Math.max(progress, 0); }\n        }\n        $slideEl\n          .transform((\"translate3d(\" + tx + \"px, \" + ty + \"px, 0px) rotateX(\" + rotateX + \"deg) rotateY(\" + rotateY + \"deg)\"));\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var slides = swiper.slides;\n      var activeIndex = swiper.activeIndex;\n      var $wrapperEl = swiper.$wrapperEl;\n      slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n      if (swiper.params.virtualTranslate && duration !== 0) {\n        var eventTriggered = false;\n        // eslint-disable-next-line\n        slides.eq(activeIndex).transitionEnd(function onTransitionEnd() {\n          if (eventTriggered) { return; }\n          if (!swiper || swiper.destroyed) { return; }\n          // if (!$(this).hasClass(swiper.params.slideActiveClass)) return;\n          eventTriggered = true;\n          swiper.animating = false;\n          var triggerEvents = ['webkitTransitionEnd', 'transitionend'];\n          for (var i = 0; i < triggerEvents.length; i += 1) {\n            $wrapperEl.trigger(triggerEvents[i]);\n          }\n        });\n      }\n    },\n  };\n\n  var EffectFlip = {\n    name: 'effect-flip',\n    params: {\n      flipEffect: {\n        slideShadows: true,\n        limitRotation: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        flipEffect: {\n          setTranslate: Flip.setTranslate.bind(swiper),\n          setTransition: Flip.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"flip\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          spaceBetween: 0,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.flipEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.flipEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Coverflow = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var swiperWidth = swiper.width;\n      var swiperHeight = swiper.height;\n      var slides = swiper.slides;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slidesSizesGrid = swiper.slidesSizesGrid;\n      var params = swiper.params.coverflowEffect;\n      var isHorizontal = swiper.isHorizontal();\n      var transform = swiper.translate;\n      var center = isHorizontal ? -transform + (swiperWidth / 2) : -transform + (swiperHeight / 2);\n      var rotate = isHorizontal ? params.rotate : -params.rotate;\n      var translate = params.depth;\n      // Each slide offset from center\n      for (var i = 0, length = slides.length; i < length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var slideSize = slidesSizesGrid[i];\n        var slideOffset = $slideEl[0].swiperSlideOffset;\n        var offsetMultiplier = ((center - slideOffset - (slideSize / 2)) / slideSize) * params.modifier;\n\n        var rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n        var rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n        // var rotateZ = 0\n        var translateZ = -translate * Math.abs(offsetMultiplier);\n\n        var translateY = isHorizontal ? 0 : params.stretch * (offsetMultiplier);\n        var translateX = isHorizontal ? params.stretch * (offsetMultiplier) : 0;\n\n        // Fix for ultra small values\n        if (Math.abs(translateX) < 0.001) { translateX = 0; }\n        if (Math.abs(translateY) < 0.001) { translateY = 0; }\n        if (Math.abs(translateZ) < 0.001) { translateZ = 0; }\n        if (Math.abs(rotateY) < 0.001) { rotateY = 0; }\n        if (Math.abs(rotateX) < 0.001) { rotateX = 0; }\n\n        var slideTransform = \"translate3d(\" + translateX + \"px,\" + translateY + \"px,\" + translateZ + \"px)  rotateX(\" + rotateX + \"deg) rotateY(\" + rotateY + \"deg)\";\n\n        $slideEl.transform(slideTransform);\n        $slideEl[0].style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n        if (params.slideShadows) {\n          // Set shadows\n          var $shadowBeforeEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var $shadowAfterEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if ($shadowBeforeEl.length === 0) {\n            $shadowBeforeEl = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append($shadowBeforeEl);\n          }\n          if ($shadowAfterEl.length === 0) {\n            $shadowAfterEl = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append($shadowAfterEl);\n          }\n          if ($shadowBeforeEl.length) { $shadowBeforeEl[0].style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0; }\n          if ($shadowAfterEl.length) { $shadowAfterEl[0].style.opacity = (-offsetMultiplier) > 0 ? -offsetMultiplier : 0; }\n        }\n      }\n\n      // Set correct perspective for IE10\n      if (Support.pointerEvents || Support.prefixedPointerEvents) {\n        var ws = $wrapperEl[0].style;\n        ws.perspectiveOrigin = center + \"px 50%\";\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      swiper.slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n    },\n  };\n\n  var EffectCoverflow = {\n    name: 'effect-coverflow',\n    params: {\n      coverflowEffect: {\n        rotate: 50,\n        stretch: 0,\n        depth: 100,\n        modifier: 1,\n        slideShadows: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        coverflowEffect: {\n          setTranslate: Coverflow.setTranslate.bind(swiper),\n          setTransition: Coverflow.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"coverflow\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n\n        swiper.params.watchSlidesProgress = true;\n        swiper.originalParams.watchSlidesProgress = true;\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n        swiper.coverflowEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n        swiper.coverflowEffect.setTransition(duration);\n      },\n    },\n  };\n\n  // Swiper Class\n\n  var components = [\n    Device$1,\n    Support$1,\n    Browser$1,\n    Resize,\n    Observer$1,\n    Virtual$1,\n    Keyboard$1,\n    Mousewheel$1,\n    Navigation$1,\n    Pagination$1,\n    Scrollbar$1,\n    Parallax$1,\n    Zoom$1,\n    Lazy$1,\n    Controller$1,\n    A11y,\n    History$1,\n    HashNavigation$1,\n    Autoplay$1,\n    EffectFade,\n    EffectCube,\n    EffectFlip,\n    EffectCoverflow\n  ];\n\n  if (typeof Swiper.use === 'undefined') {\n    Swiper.use = Swiper.Class.use;\n    Swiper.installModule = Swiper.Class.installModule;\n  }\n\n  Swiper.use(components);\n\n  return Swiper;\n\n})));\n"]}