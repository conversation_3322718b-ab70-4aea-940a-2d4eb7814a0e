<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
  <title></title>
  <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
  <script type="text/javascript" src="../internal.js?20220503"></script>
  <style type="text/css">
    .wrapper {
      width: 600px;
      padding: 10px;
      height: 352px;
      overflow: hidden;
      position: relative;
      border-bottom: 1px solid #d7d7d7
    }

    .editor-wrap {
      display: flex;
    }

    .editor-wrap #editor {
      width: 0;
      flex-grow: 1;
      border: 1px solid #CCC;
      border-radius: 3px;
      padding: 5px;
      height: 100px;
      outline: none;
    }

    .input-tip {
      margin-top: 10px;
    }

    .input-tip a {
      color: #0f0d0d;
    }

    .editor-preview {
      background: #FFF;
      border-radius: 3px;
      margin-top: 10px;
      border: 1px solid #EEE;
      display:none;
    }

    .editor-preview .title {
      padding: 5px;
    }

    .editor-preview .body {
      padding: 5px 5px 15px 5px;
      text-align: center;
    }

    .editor-preview .body .image {
      max-width:100%;
      max-height:100px;
    }
  </style>
</head>
<body>
<div class="wrapper">
  <div class="editor-wrap">
    <textarea id="editor"></textarea>
  </div>
  <div class="input-tip">
    基于 latex 语法，<a href="javascript:;" id="inputDemo">点击输入示例</a>。
  </div>
  <div class="editor-preview" id="preview">
    <div class="title">预览</div>
    <div class="body">
      <img class="image" id="previewImage" />
    </div>
  </div>
</div>
<script src="../../third-party/jquery-1.10.2.js?20220503"></script>
<script type="text/javascript" src="../../third-party/clipboard/clipboard.js?20220503"></script>
<script type="text/javascript" src="formula.js?20220903"></script>
<script type="text/javascript">
  utils.domReady(function () {
    Formula.init();
  });
</script>
</body>
</html>
