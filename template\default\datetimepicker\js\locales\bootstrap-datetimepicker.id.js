/**
 * Bahasa translation for bootstrap-datetimepicker
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 * Addtional by <PERSON><PERSON> <<EMAIL>>
 */
;(function($){
	$.fn.datetimepicker.dates['id'] = {
		days: ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"],
		daysShort: ["Mng", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sab", "Mng"],
		daysMin: ["Mg", "Sn", "Sl", "Ra", "<PERSON>", "<PERSON>", "Sa", "Mg"],
		months: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "April", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Agus<PERSON>", "September", "Oktober", "November", "Des<PERSON>ber"],
		monthsShort: ["Jan", "Feb", "Mar", "Apr", "<PERSON>", "<PERSON>", "<PERSON>", "A<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"],
		today: "<PERSON> Ini",
		suffix: [],
		meridiem: [],
		weekStart: 1,
		format: "dd/mm/yyyy hh:ii:ss"
	};
}(j<PERSON>uery));
