body, html {
	height: 100%
}
body {
	background: url(../images/bg.jpg);
}
.user-login {
	position: relative;
	left: 0;
	top: 0;
	padding: 120px 0;
	min-height: 100%;
	box-sizing: border-box;
}
.user-login-main {
	width: 375px;
	margin: 0 auto;
	box-sizing: border-box;
	background: #ccc;
	background-color: rgba(248,248,255,0.2);
	border-radius: 5px;
}
.user-login-header {
	text-align: center;
	padding-top: 20px;
}
.user-login-header h2 {
	margin-bottom: 10px;
	font-weight: 300;
	font-size: 25px;
	color: #fff;
	font-weight: bold;
}
.user-login-header p {
	font-weight: 300;
	color: #eee;
}
.user-login-box {
	padding: 20px;
}
.user-login-icon {
	position: absolute;
	left: 1px;
	top: 1px;
	width: 38px;
	line-height: 36px;
	text-align: center;
	color: #d2d2d2;
}
.user-login-box .layui-form-item {
	position: relative;
}
.user-login-box .layui-form-item .layui-input {
	padding-left: 38px
}
.user-login-codeimg {
	height: 38px;
	width: 100%;
	cursor: pointer;
	box-sizing: border-box;
}
.user-login-footer {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	line-height: 1.5;
	padding: 20px;
	text-align: center;
	box-sizing: border-box;
	color: rgba(0,0,0,.5);
}
.user-login-footer span {
	padding: 0 5px;
}
.user-login-footer a {
	color: rgba(0,0,0,.5);
}
.user-login-footer a:hover {
	color: rgba(0,0,0,1);
}

@media screen and (max-width:768px) {
.user-login {
	padding-top: 60px;
}
.user-login-main {
	width: 300px;
}
.user-login-box {
	padding: 10px;
}
}

@media \0screen\,screen\9 {/* 只支持IE6、7、8 */
.user-login-header h2 {
	color: #000;
}
.user-login-header p {
	color: #999;
}
}
