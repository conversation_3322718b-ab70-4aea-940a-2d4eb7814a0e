<?php
/**
 * PbootCMS Kernel.php 加密版本部署脚本
 * 安全地替换原始文件并进行功能测试
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-23
 */

class KernelDeployment
{
    private $originalFile;
    private $encryptedFile;
    private $backupFile;
    private $testResults = array();
    
    public function __construct()
    {
        $this->originalFile = 'core/basic/Kernel.php';
        $this->encryptedFile = '最终加密版Kernel.php';
        $this->backupFile = 'core/basic/Kernel.php.backup.' . date('YmdHis');
    }
    
    /**
     * 执行部署流程
     */
    public function deploy()
    {
        echo "🚀 开始部署PbootCMS Kernel.php加密版本...\n\n";
        
        try {
            // 1. 环境检查
            $this->checkEnvironment();
            
            // 2. 备份原文件
            $this->backupOriginalFile();
            
            // 3. 部署加密版本
            $this->deployEncryptedVersion();
            
            // 4. 功能测试
            $this->runFunctionalTests();
            
            // 5. 显示结果
            $this->showResults();
            
        } catch (Exception $e) {
            echo "❌ 部署失败: " . $e->getMessage() . "\n";
            $this->rollback();
        }
    }
    
    /**
     * 环境检查
     */
    private function checkEnvironment()
    {
        echo "🔍 检查部署环境...\n";
        
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '5.4.0', '<')) {
            throw new Exception('PHP版本过低，需要5.4.0或更高版本');
        }
        echo "✅ PHP版本: " . PHP_VERSION . "\n";
        
        // 检查原文件是否存在
        if (!file_exists($this->originalFile)) {
            throw new Exception('原始Kernel.php文件不存在: ' . $this->originalFile);
        }
        echo "✅ 原始文件存在: " . $this->originalFile . "\n";
        
        // 检查加密文件是否存在
        if (!file_exists($this->encryptedFile)) {
            throw new Exception('加密版本文件不存在: ' . $this->encryptedFile);
        }
        echo "✅ 加密文件存在: " . $this->encryptedFile . "\n";
        
        // 检查目录权限
        if (!is_writable(dirname($this->originalFile))) {
            throw new Exception('目录不可写: ' . dirname($this->originalFile));
        }
        echo "✅ 目录权限正常\n";
        
        echo "\n";
    }
    
    /**
     * 备份原文件
     */
    private function backupOriginalFile()
    {
        echo "💾 备份原始文件...\n";
        
        if (!copy($this->originalFile, $this->backupFile)) {
            throw new Exception('备份文件失败');
        }
        
        echo "✅ 备份完成: " . $this->backupFile . "\n";
        echo "📊 原文件大小: " . $this->formatBytes(filesize($this->originalFile)) . "\n";
        echo "🔐 原文件MD5: " . md5_file($this->originalFile) . "\n\n";
    }
    
    /**
     * 部署加密版本
     */
    private function deployEncryptedVersion()
    {
        echo "🔄 部署加密版本...\n";
        
        if (!copy($this->encryptedFile, $this->originalFile)) {
            throw new Exception('部署加密版本失败');
        }
        
        echo "✅ 部署完成\n";
        echo "📊 新文件大小: " . $this->formatBytes(filesize($this->originalFile)) . "\n";
        echo "🔐 新文件MD5: " . md5_file($this->originalFile) . "\n\n";
    }
    
    /**
     * 运行功能测试
     */
    private function runFunctionalTests()
    {
        echo "🧪 运行功能测试...\n";
        
        // 测试1: 语法检查
        $this->testSyntax();
        
        // 测试2: 类加载测试
        $this->testClassLoading();
        
        // 测试3: 方法存在性测试
        $this->testMethodExistence();
        
        // 测试4: 反调试功能测试
        $this->testAntiDebugFeatures();
        
        echo "\n";
    }
    
    /**
     * 语法检查
     */
    private function testSyntax()
    {
        echo "  🔍 语法检查...";
        
        $output = array();
        $returnCode = 0;
        exec("php -l " . escapeshellarg($this->originalFile) . " 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo " ✅ 通过\n";
            $this->testResults['syntax'] = true;
        } else {
            echo " ❌ 失败\n";
            echo "    错误: " . implode("\n    ", $output) . "\n";
            $this->testResults['syntax'] = false;
        }
    }
    
    /**
     * 类加载测试
     */
    private function testClassLoading()
    {
        echo "  🔍 类加载测试...";
        
        try {
            // 临时包含文件测试
            $tempFile = tempnam(sys_get_temp_dir(), 'kernel_test_');
            $testCode = '<?php
            namespace core\\basic;
            require_once "' . realpath($this->originalFile) . '";
            if (class_exists("core\\basic\\Kernel")) {
                echo "SUCCESS";
            } else {
                echo "FAILED";
            }
            ?>';
            
            file_put_contents($tempFile, $testCode);
            $result = shell_exec("php " . escapeshellarg($tempFile) . " 2>&1");
            unlink($tempFile);
            
            if (strpos($result, 'SUCCESS') !== false) {
                echo " ✅ 通过\n";
                $this->testResults['class_loading'] = true;
            } else {
                echo " ❌ 失败\n";
                echo "    错误: " . trim($result) . "\n";
                $this->testResults['class_loading'] = false;
            }
        } catch (Exception $e) {
            echo " ❌ 失败\n";
            echo "    异常: " . $e->getMessage() . "\n";
            $this->testResults['class_loading'] = false;
        }
    }
    
    /**
     * 方法存在性测试
     */
    private function testMethodExistence()
    {
        echo "  🔍 方法存在性测试...";
        
        try {
            // 检查关键方法是否存在
            $reflection = new ReflectionClass('core\\basic\\Kernel');
            $requiredMethods = array('run', '__callStatic');
            $missingMethods = array();
            
            foreach ($requiredMethods as $method) {
                if (!$reflection->hasMethod($method)) {
                    $missingMethods[] = $method;
                }
            }
            
            if (empty($missingMethods)) {
                echo " ✅ 通过\n";
                $this->testResults['methods'] = true;
            } else {
                echo " ❌ 失败\n";
                echo "    缺少方法: " . implode(', ', $missingMethods) . "\n";
                $this->testResults['methods'] = false;
            }
        } catch (Exception $e) {
            echo " ❌ 失败\n";
            echo "    异常: " . $e->getMessage() . "\n";
            $this->testResults['methods'] = false;
        }
    }
    
    /**
     * 反调试功能测试
     */
    private function testAntiDebugFeatures()
    {
        echo "  🔍 反调试功能测试...";
        
        // 检查反调试代码是否存在
        $content = file_get_contents($this->originalFile);
        $antiDebugFeatures = array(
            'xdebug_is_enabled',
            'memory_limit',
            'max_execution_time',
            'microtime'
        );
        
        $foundFeatures = 0;
        foreach ($antiDebugFeatures as $feature) {
            if (strpos($content, $feature) !== false) {
                $foundFeatures++;
            }
        }
        
        if ($foundFeatures >= 3) {
            echo " ✅ 通过 (发现 {$foundFeatures}/{count($antiDebugFeatures)} 个特征)\n";
            $this->testResults['anti_debug'] = true;
        } else {
            echo " ⚠️  部分通过 (发现 {$foundFeatures}/{count($antiDebugFeatures)} 个特征)\n";
            $this->testResults['anti_debug'] = false;
        }
    }
    
    /**
     * 显示结果
     */
    private function showResults()
    {
        echo "📋 部署结果报告\n";
        echo "==========================================\n";
        
        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults));
        
        foreach ($this->testResults as $test => $result) {
            $status = $result ? '✅ 通过' : '❌ 失败';
            echo sprintf("%-20s: %s\n", ucfirst(str_replace('_', ' ', $test)), $status);
        }
        
        echo "==========================================\n";
        echo "总体结果: {$passedTests}/{$totalTests} 测试通过\n";
        
        if ($passedTests === $totalTests) {
            echo "🎉 部署成功！加密版本已正常工作。\n";
            echo "📁 备份文件: " . $this->backupFile . "\n";
            echo "🔄 如需回滚，请运行: php 部署加密版本.php rollback\n";
        } else {
            echo "⚠️  部署完成但存在问题，请检查测试结果。\n";
            echo "🔄 建议回滚到原版本: php 部署加密版本.php rollback\n";
        }
    }
    
    /**
     * 回滚到原版本
     */
    public function rollback()
    {
        echo "🔄 回滚到原版本...\n";
        
        // 查找最新的备份文件
        $backupFiles = glob('core/basic/Kernel.php.backup.*');
        if (empty($backupFiles)) {
            echo "❌ 未找到备份文件\n";
            return;
        }
        
        // 使用最新的备份文件
        rsort($backupFiles);
        $latestBackup = $backupFiles[0];
        
        if (copy($latestBackup, $this->originalFile)) {
            echo "✅ 回滚成功，已恢复到原版本\n";
            echo "📁 使用的备份文件: " . $latestBackup . "\n";
        } else {
            echo "❌ 回滚失败\n";
        }
    }
    
    /**
     * 格式化字节大小
     */
    private function formatBytes($size, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB');
        $base = log($size, 1024);
        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $units[floor($base)];
    }
}

// 命令行处理
if (isset($argv[1]) && $argv[1] === 'rollback') {
    $deployment = new KernelDeployment();
    $deployment->rollback();
} else {
    $deployment = new KernelDeployment();
    $deployment->deploy();
}
?>
