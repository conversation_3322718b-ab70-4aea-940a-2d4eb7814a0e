<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" src="../internal.js?20220503"></script>
    <link rel="stylesheet" type="text/css" href="video.css?20220503" />
</head>
<body>
<div class="wrapper">
    <div id="videoTab">
        <div id="tabHeads" class="tabhead">
            <span tabSrc="video" class="focus" data-content-id="video"><var id="lang_tab_insertV"></var></span>
            <span tabSrc="upload" style="display:none;" data-content-id="upload"><var id="lang_tab_uploadV"></var></span>
        </div>
        <div id="tabBodys" class="tabbody">
            <div id="video" class="panel focus">
               <table><tr><td><label for="videoUrl" class="url"><var id="lang_video_url"></var></label></td><td><input id="videoUrl" type="text"><a href="javascript:;" id="videoSelect" style="display:none;">选择视频</a> </td></tr></table>
               <div style="padding:0 5px 5px 5px;color:#999;">
                 外链视频支持：优酷、腾讯视频、哔哩哔哩
               </div>
               <div id="preview"></div>
               <div id="videoInfo">
                   <fieldset>
                       <legend><var id="lang_video_size"></var></legend>
                       <table>
                           <tr><td><label for="videoWidth"><var id="lang_videoW"></var></label></td><td><input class="txt" id="videoWidth" type="text"/></td></tr>
                           <tr><td><label for="videoHeight"><var id="lang_videoH"></var></label></td><td><input class="txt" id="videoHeight" type="text"/></td></tr>
                       </table>
                   </fieldset>
                   <fieldset>
                      <legend><var id="lang_alignment"></var></legend>
                      <div id="videoFloat"></div>
                  </fieldset>
               </div>
            </div>
            <div id="upload" class="panel">
                <div id="upload_left">
                    <div id="queueList" class="queueList">
                        <div class="statusBar element-invisible">
                            <div class="progress">
                                <span class="text">0%</span>
                                <span class="percentage"></span>
                            </div><div class="info"></div>
                            <div class="btns">
                                <div id="filePickerBtn"></div>
                                <div class="uploadBtn"><var id="lang_start_upload"></var></div>
                            </div>
                        </div>
                        <div id="dndArea" class="placeholder">
                            <div class="filePickerContainer">
                                <div id="filePickerReady"></div>
                            </div>
                        </div>
                        <ul class="filelist element-invisible">
                            <li id="filePickerBlock" class="filePickerBlock"></li>
                        </ul>
                    </div>
                </div>
                <div id="uploadVideoInfo">
                    <fieldset>
                        <legend><var id="lang_upload_size"></var></legend>
                        <table>
                            <tr><td><label><var id="lang_upload_width"></var></label></td><td><input class="txt" id="upload_width" type="text"/></td></tr>
                            <tr><td><label><var id="lang_upload_height"></var></label></td><td><input class="txt" id="upload_height" type="text"/></td></tr>
                        </table>
                    </fieldset>
                    <fieldset>
                        <legend><var id="lang_upload_alignment"></var></legend>
                        <div id="upload_alignment"></div>
                    </fieldset>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- jquery -->
<script type="text/javascript" src="../../third-party/jquery-1.10.2.js?20220503"></script>

<!-- webuploader -->
<script type="text/javascript" src="../../third-party/webuploader/webuploader.js?20220503"></script>
<link rel="stylesheet" type="text/css" href="../../third-party/webuploader/webuploader.css?20220503">

<!-- video -->
<script type="text/javascript" src="video.js?20220503"></script>
</body>
</html>
