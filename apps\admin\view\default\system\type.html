{include file='common/head.html'}

<div class="layui-body">
	{if([$list])}
	
	<div class="layui-tab layui-tab-brief" lay-filter="tab">
	  <ul class="layui-tab-title">
	    <li class="layui-this" lay-id="t1">类型列表</li>
	    <li lay-id="t2">类型新增</li>
	  </ul>
	  <div class="layui-tab-content">
	  	   <div class="layui-tab-item layui-show">
	  	   		<form action="{url./admin/Type/index}" method="get" class="layui-form">
		  	   		<div class="layui-form-item nospace">
		  	   			<div class="layui-input-inline">
		  	   			   {$pathinfo}
					       <select name="field" lay-verify="required">
	                            <option value="tcode" {if(get('field')=='tcode')}selected="selected" {/if} >类型编码</option>
                                <option value="name" {if(get('field')=='name')}selected="selected" {/if}>类型名称</option>
                                <option value="item" {if(get('field')=='item')}selected="selected" {/if}>类型项</option>
	                       </select>
					    </div>
	                     <div class="layui-input-inline">
	                     	<input type="text" name="keyword"  value="{$get.keyword}"  placeholder="请输入搜索关键字" class="layui-input">
	                     </div>
	                     <div class="layui-input-inline">
	                     	<button class="layui-btn" lay-submit>搜索</button>
	                     </div>
	                </div>
                </form>
                
				<table class="layui-table">
					<thead>
	                    <tr>
	                        <th>类型编码</th>
	                        <th>类型名称</th>
	                        <th>类型项</th>
	                        <th>类型值</th>
	                        <th>排序</th>
	                        <th>操作</th>
	                    </tr>
	                </thead>
	                <tbody>
	                    {php}$tcode=''{/php}
	                    {foreach $types(key,value,num)}
	                    {if($value->tcode!=$tcode)}
	                        {php}$tcode=$value->tcode;{/php}
	                        <tr>
	                            <td>[value->tcode]</td>
	                            <td>[value->name]</td>
	                            <td></td>
	                            <td></td>
	                            <td></td>
	                            <td></td>
	                        </tr>
	                    {/if}
	                    <tr>
	                        <td></td>
	                        <td></td>
	                        <td>[value->item]</td>
	                        <td>[value->value]</td>
	                        <td>[value->sorting]</td>
	                        <td>
	                            {fun=get_btn_del($value->id)}
	                            {fun=get_btn_mod($value->id)}
	                        </td>
	                    </tr>
	                    {/foreach}
	                </tbody>
				</table>
				<div class="page">{$pagebar}</div>
			</div>
			
			<div class="layui-tab-item">
				 <form action="{url./admin/Type/add}" method="post" class="layui-form">
				 	<input type="hidden" name="formcheck" value="{$formcheck}" > 
				 	<div class="layui-form-item">
	                     <label class="layui-form-label">类型编码</label>
	                     <div class="layui-input-block">
	                     	<select name="tcode">
		                        <option value="0">新建（自动编码）</option>
		                        {foreach $type_select(key,value)}
		                            <option value="[value->tcode]" title="[value->name]">[value->tcode] [value->name]</option>
		                        {/foreach}
		                    </select>
	                     </div>
	                </div>
					
					<div class="layui-form-item">
	                     <label class="layui-form-label">类型名称</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="name" required  lay-verify="required" placeholder="请输入类型名称" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">类型项名称</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="item" required  lay-verify="required"  placeholder="请输入类型项名称" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">类型项值</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="value" required  lay-verify="required"  placeholder="请输入类型项值" class="layui-input">
	                     </div>
	                </div>
	                
					<div class="layui-form-item">
	                     <label class="layui-form-label">排序</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="sorting" required  lay-verify="required" value="255"  placeholder="请输入排序值" class="layui-input">
	                     </div>
	                </div>
	                
	               <div class="layui-form-item">
						 <div class="layui-input-block">
						    <button class="layui-btn" lay-submit>立即提交</button>
						    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
						 </div>
				    </div>
				 </form>
			</div>
	  </div>
	</div>
	
	{/if} 
	
	
	{if([$mod])}
	
	<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
		<ul class="layui-tab-title">
		  <li class="layui-this">用户修改</li>
		</ul>
		<div class="layui-tab-content">
		   <div class="layui-tab-item layui-show">
		   		 <form action="{url./admin/Type/mod/id/'.[$get.id].'}{$backurl}" method="post" class="layui-form">
					<input type="hidden" name="formcheck" value="{$formcheck}" > 
					<div class="layui-form-item">
	                     <label class="layui-form-label">类型名称</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="name" required  lay-verify="required" value="{$type->name}" placeholder="请输入类型名称" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">类型项名称</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="item" required  lay-verify="required" value="{$type->item}"  placeholder="请输入类型项名称" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">类型项值</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="value" required  lay-verify="required" value="{$type->value}"  placeholder="请输入类型项值" class="layui-input">
	                     </div>
	                </div>
	                
					<div class="layui-form-item">
	                     <label class="layui-form-label">排序</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="sorting" required  lay-verify="required" value="{$type->sorting}"  placeholder="请输入排序值" class="layui-input">
	                     </div>
	                </div>
	                
	               <div class="layui-form-item">
						 <div class="layui-input-block">
						    <button class="layui-btn" lay-submit>立即提交</button>
						    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
						    {fun=get_btn_back()}
						 </div>
				    </div>
		   		 </form>
		   </div>
		</div>
	 </div>
	{/if} 

</div>

{include file='common/foot.html'}
