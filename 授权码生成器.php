<?php
/**
 * 授权码生成器
 * 支持多种授权方式：用户、域名、IP、手机号
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-30
 */

$config = array(
    'username' => '$admin',
    'ip' => '127.0.0.1',
    'domain' => 'localhost',
    'phone' => '18701879170'
);

// 生成用户名授权码
function generateUsernameCode($username) {
    return strtoupper(substr(md5(substr(sha1($username), 0, 20)), 10, 10));
}

// 生成IP授权码
function generateIPCode($ip) {
    return strtoupper(substr(md5(substr(sha1($ip), 0, 15)), 10, 10));
}

// 生成域名授权码
function generateDomainCode($domain) {
    return strtoupper(substr(md5(substr(sha1($domain), 0, 10)), 10, 10));
}

// 生成万能授权码
function generatePhoneCode($phone) {
    return strtoupper(substr(md5(substr(sha1($phone), 0, 20)), 10, 10));
}


$hash1 = generateUsernameCode($config['username']);
$hash2 = generateIPCode($config['ip']);
$hash3 = generateDomainCode($config['domain']);
$hash4 = generatePhoneCode($config['phone']);

echo "用户授权码: $hash1\n";
echo "IP授权码: $hash2\n";
echo "域名授权码: $hash3\n";
echo "万能授权码: $hash4\n";








