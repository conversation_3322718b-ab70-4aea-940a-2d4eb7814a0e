{include file='common/head.html'}

<div class="layui-body">
	{if([$list])}
	<div class="layui-tab layui-tab-brief" lay-filter="tab">
	  <ul class="layui-tab-title">
	    <li class="layui-this" lay-id="t1">表单列表</li>
	    <li lay-id="t2">表单新增</li>
	  </ul>
	  <div class="layui-tab-content">
	  	   <div class="layui-tab-item layui-show">
	  	   		<table class="layui-table">
	            	<thead>
	                    <tr>
	                    	<th>编码</th>
							<th>表单名称</th>
	                        <th>表名称</th>
	                        <th>数据</th>
	                        <th>字段</th>
	                        <th>操作</th>
	                    </tr>
	                </thead>
	                <tbody>
	                    {foreach $forms(key,value)}
	                    <tr>
	                    	<td>[value->fcode]</td>
							<td>[value->form_name]</td>
	                    	<td>[value->table_name]</td>
	                    	<td>
	                    	{if($value->id==1)}
	                    	<a href="{url./admin/Message/index}"><i class="fa fa-list"></i> 查看数据</a></td>
	                    	{else}
	                    	<a href="{url./admin/Form/index/fcode/'.$value->fcode.'/action/showdata}"><i class="fa fa-list"></i> 查看数据</a></td>
	                    	{/if}
	                    	<td><a href="{url./admin/Form/index/fcode/'.$value->fcode.'/action/showfield}"><i class="fa fa-pencil-square-o"></i> 编辑字段</a></td>
	                        <td>
								
	                        	{if($value->id!=1)}
									<a href="{url./admin/Form/mod/id/'.$value->id.'/action/addmenu}{$btnqs}" class="layui-btn layui-btn-xs layui-btn-primary">添加到菜单</a>
	                        		<a href="{url./admin/Form/del/id/'.$value->id.'/action/delform}" onclick="return confirm('您确定要删除么？')" class="layui-btn layui-btn-xs layui-btn-danger" title="删除">删除</a>
	                            {/if}
	                            <a href="{url./admin/Form/mod/id/'.$value->id.'/action/modform}{$btnqs}" class="layui-btn layui-btn-xs">修改</a>
								
	                        </td>
	                    </tr>
	                    {/foreach}
	                </tbody>
	            </table>
	            <div class="page">{$pagebar}</div>
	  	   </div>
	  	   
	  	    <div class="layui-tab-item">
	   			<form action="{url./admin/Form/add/action/addform}" method="post" class="layui-form">
	   				<input type="hidden" name="formcheck" value="{$formcheck}" > 
	   				<div class="layui-form-item">
	                     <label class="layui-form-label">表单名称</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="form_name" maxlength=20 required lay-verify="required"  placeholder="请输入表单名称，如：报名表" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">表名称</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="table_name" maxlength=30 required lay-verify="required"  placeholder="请输入表名称，如：baoming" class="layui-input">
	                     </div>
	                </div>
	                
	   				<div class="layui-form-item">
						 <div class="layui-input-block">
						    <button class="layui-btn" lay-submit>立即提交</button>
						    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
						 </div>
					</div>
	   			</form>
	   	 	</div>
	   </div>
	 </div>
	{/if} 
	
	{if([$showdata])}
	<div class="layui-tab layui-tab-brief" lay-filter="tab">
	  <ul class="layui-tab-title">
	    <li class="layui-this" lay-id="t1">{$form->form_name} — 数据</li>
	  </ul>
	  <div class="layui-tab-content">
	  	   <div class="layui-tab-item layui-show">
	  	   		{foreach $formdata(key,value,num)}
	  	   		<table class="layui-table table-two">
	  	   			<thead>
				  		<tr>
					  		<th colspan="2">
					  			{$form->form_name}-[num] 
					  			<span style="float:right"><a href="{url./admin/Form/del/id/'.$value->id.'/fcode/'.[$form->fcode].'/action/deldata}" onclick="return confirm('您确定要删除么？')" class="layui-btn layui-btn-xs layui-btn-danger" style="margin-bottom:0px" title="删除">删除</a></span>
					  		</th>
					  	</tr>
				    </thead>
	  	   			<tbody>
		  	   			{foreach $fields(key2,value2,num2)}
			  	   			<tr>
						      	<th>[value2->description]</th>
						      	{php} $field=$value2->name {/php}
						      	<td>[value->$field]</td>
						    </tr>
		  	   			{/foreach}
		  	   			<tr>
					      	<th>时间</th>
					      	<td>[value->create_time]</td>
					    </tr>
	  	   			</tbody>
	            </table>
	            {/foreach}
	            
	            {if(session('ucode')==10001 && [$formdata])}
			     	<a href="{url./admin/Form/clear/fcode/'.[$form->fcode].'}"  onclick='return confirm("您确定要清空么？")' class="layui-btn layui-btn-sm">清空记录</a>
			    {/if}
			    
			    {if([$formdata])}
			    	<a href="{url./admin/Form/index/fcode/'.[$form->fcode].'/action/showdata/export/1}" class="layui-btn layui-btn-sm">导出记录</a>
			    {/if}
			    
	            <div class="page">{$pagebar}</div>
	  	   </div>
	  	 </div>
	  </div>
	{/if}  	  
	
	{if([$showfield])}
	<div class="layui-tab layui-tab-brief" lay-filter="tab">
	  <ul class="layui-tab-title">
	    <li class="layui-this" lay-id="t1">{$form->form_name}-表单字段</li>
	    <li lay-id="t2">新增字段</li>
	  </ul>
	  <div class="layui-tab-content">
	  	   <div class="layui-tab-item layui-show">
	  	   		<table class="layui-table">
	            	<thead>
	                    <tr>
	                    	<th width=40>序号</th>
	                    	<th>描述</th>
	                    	<th>字段</th>
	                    	<th>长度</th>
	                    	<th>是否必填</th>
	                    	<th>排序</th>
	                        <th>操作</th>
	                    </tr>
	                </thead>
	                <tbody>
	                    {foreach $fields(key,value)}
	                    <tr>
	                    	<td>[num]</td>
	                    	<td>[value->description]</td>
	                    	<td>[value->name]</td>
	                    	<td>[value->length]</td>
	                    	<td>
		                        {if($value->required)}
		                        <a href="{url./admin/'.C.'/mod/id/'.$value->id.'/field/required/value/0}"><i class='fa fa-toggle-on' title="点击关闭必填"></i></a>
		                        {else}
		                        <a href="{url./admin/'.C.'/mod/id/'.$value->id.'/field/required/value/1}"><i class='fa fa-toggle-off' title="点击开启必填"></i></a>
		                        {/if}
	                    	</td>
	                    	<td>[value->sorting]</td>
	                        <td>
	                       		<a href="{url./admin/Form/del/id/'.$value->id.'/fcode/'.[$get.fcode].'}" onclick="return confirm('您确定要删除么？')" class="layui-btn layui-btn-xs layui-btn-danger" title="删除">删除</a>
	                            {fun=get_btn_mod($value->id)}
	                        </td>
	                    </tr>
	                    {/foreach}
	                </tbody>
	            </table>
	            <div class="page">{$pagebar}</div>
	            <div><a href="{url./admin/Form/index}" class="layui-btn">返回表单</a></div>
	  	   </div>
	  	   
	  	   <div class="layui-tab-item">
	   			<form action="{url./admin/Form/add}" method="post" class="layui-form">
					<input type="hidden" name="formcheck" value="{$formcheck}" > 
	                <div class="layui-form-item">
	                     <label class="layui-form-label">字段描述</label>
	                     <div class="layui-input-block">
	                     	<input type="hidden" value="{$get.fcode}" name="fcode" >
	                     	<input type="text" name="description" required lay-verify="required"  placeholder="请输入字段描述，如：联系人" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">字段名称</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="name" required maxlength="20" lay-verify="required"  placeholder="请输入字段名称，必须以字母开头，如：contacts" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">字段长度</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="length" value="20" required lay-verify="required" class="layui-input">
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">是否必填</label>
	                     <div class="layui-input-block">
	                     	<input type="radio" name="required" value="1" title="必填" checked>
							<input type="radio" name="required" value="0" title="非必填"  checked>
	                     </div>
	                </div>
	                
	                <div class="layui-form-item">
	                     <label class="layui-form-label">字段排序</label>
	                     <div class="layui-input-block">
	                     	<input type="text" name="sorting" value="255" required lay-verify="required" class="layui-input">
	                     </div>
	                </div>
	   			
	   				<div class="layui-form-item">
						 <div class="layui-input-block">
						    <button class="layui-btn" lay-submit>立即提交</button>
						    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
						 </div>
					</div>
	   			</form>
	   	 	</div>
	   	 	
	   </div>
	 </div>
	{/if} 
	
	
	{if(@[$mod])}
	
	{if([$form])}
	<div class="layui-tab layui-tab-brief" lay-filter="tab">
	  <ul class="layui-tab-title">
	    <li class="layui-this">自定义表单修改</li>
	  </ul>
	  <div class="layui-tab-content">
	  	<div class="layui-tab-item layui-show">
	  		<form action="{url./admin/Form/mod/id/'.[$get.id].'/action/modform}{$backurl}" method="post" class="layui-form">
	  			<input type="hidden" name="formcheck" value="{$formcheck}" > 
	  			<div class="layui-form-item">
                     <label class="layui-form-label">表单名称</label>
                     <div class="layui-input-block">
                     	<input type="text" name="form_name" maxlength=20 required value="{$form->form_name}" lay-verify="required"  placeholder="请输入表单名称，如：报名表" class="layui-input">
                     </div>
                </div>
                
                <div class="layui-form-item">
                     <label class="layui-form-label">表名称</label>
                     <div class="layui-input-block">
                     	<input type="text" maxlength=30 required readonly value="{$form->table_name}" lay-verify="required"  placeholder="请输入表名称，如：baoming" class="layui-input readonly">
                     </div>
                </div>

   				<div class="layui-form-item">
					 <div class="layui-input-block">
					    <button class="layui-btn" lay-submit>立即提交</button>
					    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
					    {fun=get_btn_back()}
					 </div>
				</div>
	  		</form>
	  	</div>
	  </div>
	 </div>
	 {/if} 
	 
	 {if(@[$field])}
	 <div class="layui-tab layui-tab-brief" lay-filter="tab">
	  <ul class="layui-tab-title">
	    <li class="layui-this">表单字段修改</li>
	  </ul>
	  <div class="layui-tab-content">
	  	<div class="layui-tab-item layui-show">
	  		<form action="{url./admin/Form/mod/id/'.[$get.id].'}{$backurl}" method="post" class="layui-form">
	  			<input type="hidden" name="formcheck" value="{$formcheck}" > 
                <div class="layui-form-item">
                     <label class="layui-form-label">字段描述</label>
                     <div class="layui-input-block">
                     	<input type="text" name="description"  value="{$field->description}" placeholder="请输入字段描述，如：联系人" class="layui-input">
                     </div>
                </div>
                
                <div class="layui-form-item">
                     <label class="layui-form-label">字段名称</label>
                     <div class="layui-input-block">
                     	<input type="text" name="name" readonly value="{$field->name}" class="layui-input readonly">
                     </div>
                </div>
                
                <div class="layui-form-item">
                     <label class="layui-form-label">字段长度</label>
                     <div class="layui-input-block">
                     	<input type="text" name="length" readonly value="{$field->length}" class="layui-input readonly">
                     </div>
                </div>
                
                <div class="layui-form-item">
                     <label class="layui-form-label">是否必填</label>
                     <div class="layui-input-block">
                     	<input type="radio" name="required" value="1" title="必填" {if([$field->required]==1)}checked{/if}>
						<input type="radio" name="required" value="0" title="非必填" {if([$field->required]==0)}checked{/if}>
                     </div>
                </div>
                
                <div class="layui-form-item">
                     <label class="layui-form-label">字段排序</label>
                     <div class="layui-input-block">
                     	<input type="text" name="sorting" value="{$field->sorting}" required lay-verify="required" class="layui-input">
                     </div>
                </div>
   			
   				<div class="layui-form-item">
					 <div class="layui-input-block">
					    <button class="layui-btn" lay-submit>立即提交</button>
					    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
					    {fun=get_btn_back()}
					 </div>
				</div>
	  		</form>
	  	</div>
	  </div>
	 </div>
	 {/if} 
	 
	{/if} 
</div>
{include file='common/foot.html'}
